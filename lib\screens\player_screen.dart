import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:provider/provider.dart';
import '../models/bhajan.dart';
import '../providers/audio_provider.dart';
import '../services/audio_service.dart';
import 'package:just_audio/just_audio.dart'
    show PlayerState, ProcessingState, LoopMode;
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';
import '../services/user_preferences_service.dart';
import '../services/auth_service.dart';
import '../services/playlist_service.dart';
import '../models/playlist.dart';
import '../utils/ui_helpers.dart';
import 'package:cached_network_image/cached_network_image.dart';

// Custom track shape for the progress slider
class CustomTrackShape extends RoundedRectSliderTrackShape {
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 0;
    final double trackLeft = offset.dx;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }
}

class PlayerScreen extends StatefulWidget {
  final Bhajan bhajan;

  const PlayerScreen({super.key, required this.bhajan});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  bool _isPlaying = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  Duration _bufferedPosition = Duration.zero;
  double _volume = 1.0;
  final UserPreferencesService _preferencesService = UserPreferencesService();
  final AuthService _authService = AuthService();
  bool _isFavorite = false;
  bool _isLoading = true;
  final PlaylistService _playlistService = PlaylistService();
  List<Playlist> _userPlaylists = [];
  bool _loadingPlaylists = false;

  @override
  void initState() {
    super.initState();
    // Initialize volume
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      setState(() {
        _volume = audioProvider.audioService.volume;
      });

      // Listen for current bhajan changes and update favorite status
      audioProvider.audioService.currentBhajanStream.listen((_) {
        // When the current bhajan changes, check the favorite status again
        _checkFavoriteStatus();
      });

      // Ensure mini player will be visible when we return to the app
      audioProvider.showMiniPlayer();
    });
    _checkFavoriteStatus();
  }

  @override
  void dispose() {
    // When leaving the player screen, ensure mini player state is preserved
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    
    // If audio is still playing, make sure mini player will be shown
    if (audioProvider.audioService.isPlaying) {
      audioProvider.showMiniPlayer();
    } else if (audioProvider.audioService.getCurrentBhajan() != null) {
      // If audio is paused but a bhajan exists, still ensure mini player is available
      audioProvider.showMiniPlayer();
    }
    
    super.dispose();
  }

  Future<void> _checkFavoriteStatus() async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        // If user is not logged in, set loading to false and don't attempt to check favorites
        setState(() {
          _isFavorite = false;
          _isLoading = false;
        });
        return;
      }

      // Get the current bhajan from the provider to ensure we're checking the correct one
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      final currentBhajan =
          audioProvider.audioService.currentBhajan ?? widget.bhajan;

      final preferences =
          await _preferencesService.getUserPreferences(user.uid);
      setState(() {
        _isFavorite =
            preferences?.favoriteBhajans.contains(currentBhajan.id) ?? false;
        _isLoading = false;
      });
    } catch (e) {
      // If there's an error checking favorites, log it and set loading to false
      print('Error checking favorite status: $e');
      setState(() {
        _isFavorite = false;
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleFavorite() async {
    final user = _authService.currentUser;
    if (user == null) {
      // Show sign in prompt
      ScaffoldMessenger.of(context).showSnackBar(UIHelpers.getInfoSnackBar(
          message: 'Please sign in to add favorites'));
      return;
    }

    // Get the current bhajan from the provider
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    final currentBhajan =
        audioProvider.audioService.currentBhajan ?? widget.bhajan;
    
    // Save the original state in case we need to revert
    final wasAlreadyFavorite = _isFavorite;
    
    // Update UI immediately (optimistic update)
    setState(() {
      _isFavorite = !_isFavorite;
    });
    
    // Show success message immediately
    ScaffoldMessenger.of(context).showSnackBar(UIHelpers.getSuccessSnackBar(
        message: _isFavorite ? 'Added to favorites' : 'Removed from favorites'));
    
    // Perform server operation in the background
    try {
      if (wasAlreadyFavorite) {
        await _preferencesService.removeFromFavorites(
          user.uid,
          'Bhajan',
          currentBhajan.id,
        );
      } else {
        await _preferencesService.addToFavorites(
          user.uid,
          'Bhajan',
          currentBhajan.id,
        );
      }
    } catch (e) {
      // Revert UI state if server operation fails
      if (mounted) {
        setState(() {
          _isFavorite = wasAlreadyFavorite;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
            UIHelpers.getErrorSnackBar(message: 'Failed to update favorites'));
      }
    }
  }

  // Load user playlists
  Future<void> _loadUserPlaylists() async {
    final user = _authService.currentUser;
    if (user == null) {
      return;
    }

    setState(() {
      _loadingPlaylists = true;
    });

    try {
      final playlists = await _playlistService.getUserPlaylists(user.uid);
      setState(() {
        _userPlaylists = playlists;
        _loadingPlaylists = false;
      });
    } catch (e) {
      print('Error loading playlists: $e');
      setState(() {
        _loadingPlaylists = false;
      });
    }
  }

  // Show playlist options dialog
  Future<void> _showPlaylistOptionsDialog() async {
    final user = _authService.currentUser;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(UIHelpers.getInfoSnackBar(
          message: 'Please sign in to use playlists'));
      return;
    }

    // Get the current bhajan from the provider
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    final currentBhajan =
        audioProvider.audioService.currentBhajan ?? widget.bhajan;

    // Load playlists first
    await _loadUserPlaylists();

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: const Color(0xFF1A1A1A),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        expand: false,
        builder: (context, scrollController) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[600],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    const Icon(
                      Icons.playlist_add,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Add to Playlist',
                      style: AppFontLoader.getPrakrtaStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Add "${currentBhajan.title}" to a playlist',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 20),

                // Create new playlist button
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _showCreatePlaylistDialog();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 14,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: AppColors.getPrimaryColor(context),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          'Create New Playlist',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Playlists list header
                _userPlaylists.isNotEmpty
                    ? Text(
                        'Your Playlists',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      )
                    : const SizedBox(),
                const SizedBox(height: 8),

                // Playlists list
                _loadingPlaylists
                    ? const Center(child: CircularProgressIndicator())
                    : Expanded(
                        child: _userPlaylists.isEmpty
                            ? Center(
                                child: Text(
                                  'No playlists yet',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.7),
                                  ),
                                ),
                              )
                            : ListView.builder(
                                controller: scrollController,
                                itemCount: _userPlaylists.length,
                                itemBuilder: (context, index) {
                                  final playlist = _userPlaylists[index];
                                  final isInPlaylist = playlist.bhajanIds
                                      .contains(currentBhajan.id);

                                  return ListTile(
                                    contentPadding: const EdgeInsets.symmetric(
                                      vertical: 4,
                                      horizontal: 16,
                                    ),
                                    leading: Container(
                                      width: 48,
                                      height: 48,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: playlist.imageUrl.isNotEmpty
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              child: Image.network(
                                                playlist.imageUrl,
                                                fit: BoxFit.cover,
                                              ),
                                            )
                                          : const Icon(
                                              Icons.music_note,
                                              color: Colors.white54,
                                            ),
                                    ),
                                    title: Text(
                                      playlist.name,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    subtitle: Text(
                                      '${playlist.bhajanIds.length} ${playlist.bhajanIds.length == 1 ? 'bhajan' : 'bhajans'}',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.5),
                                        fontSize: 12,
                                      ),
                                    ),
                                    trailing: isInPlaylist
                                        ? const Icon(
                                            Icons.check_circle,
                                            color: Colors.green,
                                          )
                                        : const Icon(
                                            Icons.add_circle_outline,
                                            color: Colors.white70,
                                          ),
                                    onTap: () async {
                                      // Add or remove bhajan from playlist
                                      try {
                                        // First prepare the action
                                        final isRemoving = isInPlaylist;
                                        final playlistName = playlist.name;
                                        
                                        // Get root scaffold messenger for reliable snackbar display
                                        final messenger = ScaffoldMessenger.of(context);
                                        final navigatorContext = Navigator.of(context).context;
                                        
                                        // Show snackbar before closing the dialog
                                        messenger.hideCurrentSnackBar();
                                        messenger.clearSnackBars();
                                        
                                        if (isRemoving) {
                                          messenger.showSnackBar(
                                            UIHelpers.getInfoSnackBar(
                                                message: 'Removing from "${playlistName}"...',
                                                duration: const Duration(seconds: 1)),
                                          );
                                        } else {
                                          messenger.showSnackBar(
                                            UIHelpers.getSuccessSnackBar(
                                                message: 'Adding to "${playlistName}"...',
                                                duration: const Duration(seconds: 1)),
                                          );
                                        }
                                        
                                        // Now close the dialog
                                        Navigator.pop(context);
                                        
                                        // Perform the action
                                        if (isRemoving) {
                                          // Remove from playlist
                                          debugPrint('Removing bhajan ${currentBhajan.id} from playlist ${playlist.id}');
                                          await _playlistService
                                              .removeBhajanFromPlaylist(
                                                  playlist.id,
                                                  currentBhajan.id);

                                          // Show final success message
                                          debugPrint('Successfully removed bhajan from playlist, showing snackbar');
                                          // Use the provider's global snackbar helper
                                          audioProvider.showGlobalSnackBar(
                                            message: 'Removed from "${playlistName}"',
                                            isSuccess: false
                                          );
                                        } else {
                                          // Add to playlist
                                          debugPrint('Adding bhajan ${currentBhajan.id} to playlist ${playlist.id}');
                                          await _playlistService
                                              .addBhajanToPlaylist(playlist.id,
                                                  currentBhajan.id);

                                          // Show final success message
                                          debugPrint('Successfully added bhajan to playlist, showing snackbar');
                                          // Use the provider's global snackbar helper
                                          audioProvider.showGlobalSnackBar(
                                            message: 'Added to "${playlistName}"',
                                            isSuccess: true
                                          );
                                        }
                                      } catch (e) {
                                        print('Error updating playlist: $e');
                                        // Use global snackbar for error message
                                        audioProvider.showGlobalSnackBar(
                                          message: 'Failed to update playlist',
                                          isError: true
                                        );
                                      }
                                    },
                                  );
                                },
                              ),
                      ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Show create playlist dialog
  Future<void> _showCreatePlaylistDialog() async {
    final user = _authService.currentUser;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(UIHelpers.getInfoSnackBar(
          message: 'Please sign in to create playlists'));
      return;
    }

    // Get the current bhajan from the provider
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    final currentBhajan =
        audioProvider.audioService.currentBhajan ?? widget.bhajan;

    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E1E1E),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Create New Playlist',
            style: AppFontLoader.getPrakrtaStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameController,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      labelText: 'Playlist Name',
                      labelStyle:
                          TextStyle(color: Colors.white.withOpacity(0.7)),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Colors.white.withOpacity(0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: AppColors.getPrimaryColor(context),
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.red),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.red),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a playlist name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: descriptionController,
                    style: const TextStyle(color: Colors.white),
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: 'Description (Optional)',
                      labelStyle:
                          TextStyle(color: Colors.white.withOpacity(0.7)),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Colors.white.withOpacity(0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: AppColors.getPrimaryColor(context),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final playlistName = nameController.text.trim();
                  
                  // Get root scaffold messenger for reliable snackbar display
                  final messenger = ScaffoldMessenger.of(context);
                  
                  // Show initial snackbar while dialog is still visible
                  messenger.hideCurrentSnackBar();
                  messenger.clearSnackBars();
                  messenger.showSnackBar(
                    UIHelpers.getInfoSnackBar(
                      message: 'Creating playlist "$playlistName"...',
                      duration: const Duration(seconds: 1)
                    )
                  );
                  
                  // Close dialog
                  Navigator.of(context).pop();

                  try {
                    // Create the playlist
                    final playlist = await _playlistService.createPlaylist(
                      name: playlistName,
                      description: descriptionController.text.trim(),
                      userId: user.uid,
                      initialBhajanIds: [
                        currentBhajan.id
                      ], // Add current bhajan
                    );

                    // Show final success message
                    // Use the provider's global snackbar helper
                    audioProvider.showGlobalSnackBar(
                      message: 'Added to new playlist "${playlist.name}"',
                      isSuccess: true
                    );
                  } catch (e) {
                    print('Error creating playlist: $e');
                    // Use global snackbar for error message
                    audioProvider.showGlobalSnackBar(
                      message: 'Failed to create playlist',
                      isError: true
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.getPrimaryColor(context),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Create'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive layout
    final screenSize = MediaQuery.of(context).size;
    final screenHeight = screenSize.height;
    final screenWidth = screenSize.width;

    // Determine if we're on a small screen
    final isSmallScreen = screenWidth < 360 || screenHeight < 700;

    // Calculate responsive dimensions
    final artworkSize = screenWidth * 0.7 > 280 ? 280.0 : screenWidth * 0.7;
    final horizontalPadding = screenWidth * 0.06;

    // Adjust vertical spacing based on screen height
    final verticalSpacing = isSmallScreen
        ? screenHeight * 0.025 // Smaller spacing for small screens
        : screenHeight * 0.03; // Standard spacing for normal screens

    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        return StreamBuilder<Bhajan?>(
          stream: audioProvider.audioService.currentBhajanStream,
          builder: (context, snapshot) {
            final currentBhajan = snapshot.data ?? widget.bhajan;

            return WillPopScope(
              onWillPop: () async {
                // Ensure mini player visibility when navigating back
                // but override user explicit dismissal
                if (audioProvider.audioService.getCurrentBhajan() != null) {
                  audioProvider.forceShowMiniPlayer(); // Use the new method
                }
                return true; // Allow the navigation
              },
              child: Scaffold(
                backgroundColor: const Color(0xFF0D0D0D),
                appBar: AppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                    onPressed: () {
                      // Ensure mini player is showing when manually navigating back
                      // but override user explicit dismissal
                      if (audioProvider.audioService.getCurrentBhajan() != null) {
                        audioProvider.forceShowMiniPlayer(); // Use the new method
                      }
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    'Now Playing',
                    style: AppFontLoader.getPrakrtaStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.2,
                    ),
                  ),
                  centerTitle: true,
                ),
                extendBodyBehindAppBar: true,
                body: Stack(
                  children: [
                    // Background with optimized blur
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: CachedNetworkImageProvider(
                              currentBhajan.artworkUrl),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Stack(
                        children: [
                          // Optimized blur with better visual hierarchy
                          BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                            child: Container(
                              color: Colors.black.withOpacity(0.65),
                            ),
                          ),
                          // Gradient overlay for better text readability
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.black.withOpacity(0.3),
                                  Colors.black.withOpacity(0.6),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Content - Single column with non-scrollable responsive layout
                    SafeArea(
                      child: Column(
                        children: [
                          // Artwork section with flexible sizing
                          Expanded(
                            flex: 5, // Larger portion for artwork
                            child: Center(
                              child: Hero(
                                tag: 'artwork-${currentBhajan.r2Url}',
                                child: GestureDetector(
                                  onVerticalDragEnd: (details) {
                                    // If swiped down with sufficient velocity
                                    if (details.primaryVelocity != null && details.primaryVelocity! > 300) {
                                      // Ensure mini player is visible when we return
                                      if (audioProvider.audioService.getCurrentBhajan() != null) {
                                        audioProvider.forceShowMiniPlayer();
                                      }
                                      // Return to the previous screen
                                      Navigator.of(context).pop();
                                      // Provide haptic feedback
                                      HapticFeedback.mediumImpact();
                                    }
                                  },
                                  child: Container(
                                    width: artworkSize,
                                    height: artworkSize,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color:
                                              AppColors.getPrimaryColor(context)
                                                  .withOpacity(0.2),
                                          blurRadius: 30,
                                          spreadRadius: 1,
                                          offset: const Offset(0, 10),
                                        ),
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.3),
                                          blurRadius: 20,
                                          offset: const Offset(0, 5),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: CachedNetworkImage(
                                        imageUrl: currentBhajan.artworkUrl,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => Container(
                                          color: Colors.black.withOpacity(0.2),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppColors.getPrimaryColor(
                                                  context),
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Container(
                                          color: Colors.black.withOpacity(0.2),
                                          child: const Icon(
                                            Icons.music_note,
                                            color: Colors.white,
                                            size: 50,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          
                          // Info and controls section
                          Expanded(
                            flex: 6, // Remaining space for controls and info
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: horizontalPadding,
                              ),
                              child: Column(
                                // Using start alignment with manual spacing instead of spaceEvenly
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  // Add top padding that's smaller on large screens
                                  SizedBox(height: screenHeight * 0.02),
                                  
                                  // Title and artist information with fixed height to prevent layout shifts
                                  Container(
                                    height: screenHeight * 0.11, // Increased container height (was 0.08)
                                    width: double.infinity,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        // Title with fixed constraints but more height for two lines
                                        Container(
                                          constraints: BoxConstraints(
                                            maxWidth: screenWidth - horizontalPadding * 2,
                                            maxHeight: screenHeight * 0.075, // Increased height (was 0.05)
                                          ),
                                          child: Text(
                                            currentBhajan.title,
                                            style: TextStyle(
                                              fontSize: screenWidth < 360 ? 22 : 24,
                                              fontWeight: FontWeight.bold,
                                              height: 1.2,
                                              color: Colors.white,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                      
                                        const SizedBox(height: 4),
                                        
                                        // Artist with fixed height
                                        Container(
                                          constraints: BoxConstraints(
                                            maxWidth: screenWidth - horizontalPadding * 2,
                                            maxHeight: screenHeight * 0.025, // Limit height
                                          ),
                                          child: Text(
                                            currentBhajan.artist,
                                            style: TextStyle(
                                              fontSize: screenWidth < 360 ? 14 : 16,
                                              fontWeight: FontWeight.normal,
                                              color: Colors.white.withOpacity(0.7),
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                  // Adaptive spacing that shrinks on larger screens - reduced due to taller title container
                                  SizedBox(height: screenHeight * (screenHeight > 800 ? 0.02 : 0.03)), // Reduced height

                                  // Enhanced progress bar with buffer indicator
                                  StreamBuilder<PositionData>(
                                    stream:
                                        audioProvider.audioService.positionDataStream,
                                    builder: (context, snapshot) {
                                      final positionData = snapshot.data ??
                                          PositionData(Duration.zero, Duration.zero,
                                              Duration.zero);

                                      // Calculate buffering percentage
                                      final bufferPercent =
                                          positionData.duration.inMilliseconds > 0
                                              ? positionData.bufferedPosition
                                                      .inMilliseconds /
                                                  positionData.duration.inMilliseconds
                                              : 0.0;

                                      return Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          // Custom progress bar with buffer indicator
                                          Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              // Buffer indicator track
                                              Container(
                                                height: 5,
                                                width: double.infinity,
                                                decoration: BoxDecoration(
                                                  color:
                                                      Colors.white.withOpacity(0.1),
                                                  borderRadius:
                                                      BorderRadius.circular(2.5),
                                                ),
                                              ),

                                              // Buffer progress indicator
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child: FractionallySizedBox(
                                                  widthFactor:
                                                      bufferPercent.clamp(0.0, 1.0),
                                                  child: Container(
                                                    height: 5,
                                                    decoration: BoxDecoration(
                                                      color: Colors.white
                                                          .withOpacity(0.3),
                                                      borderRadius:
                                                          BorderRadius.circular(2.5),
                                                    ),
                                                  ),
                                                ),
                                              ),

                                              // Actual progress slider
                                              SliderTheme(
                                                data: SliderThemeData(
                                                  trackHeight: 5,
                                                  trackShape: CustomTrackShape(),
                                                  thumbShape:
                                                      const RoundSliderThumbShape(
                                                    enabledThumbRadius:
                                                        8, // Larger thumb for better touch
                                                  ),
                                                  overlayShape:
                                                      const RoundSliderOverlayShape(
                                                    overlayRadius:
                                                        16, // Larger overlay for better feedback
                                                  ),
                                                  activeTrackColor:
                                                      AppColors.getPrimaryColor(
                                                          context),
                                                  inactiveTrackColor: Colors
                                                      .transparent, // Using custom background
                                                  thumbColor: Colors.white,
                                                  overlayColor: AppColors
                                                      .getPrimaryColorWithOpacity(
                                                          context, 0.3),
                                                ),
                                                child: Slider(
                                                  min: 0,
                                                  max: positionData.duration.inSeconds
                                                      .toDouble(),
                                                  value: positionData
                                                      .position.inSeconds
                                                      .toDouble()
                                                      .clamp(
                                                        0,
                                                        positionData
                                                            .duration.inSeconds
                                                            .toDouble(),
                                                      ),
                                                  onChanged: (value) {
                                                    // Use immediate seek during scrubbing for responsive UI
                                                    final position = Duration(
                                                        seconds: value.toInt());
                                                    audioProvider.seekImmediate(position);
                                                    
                                                    // Light haptic feedback during scrubbing
                                                    HapticFeedback.selectionClick();
                                                  },
                                                  onChangeEnd: (value) async {
                                                    // Use final seek when scrubbing ends
                                                    final position = Duration(
                                                        seconds: value.toInt());
                                                    await audioProvider.seekToPosition(position);
                                                    
                                                    // Stronger haptic feedback when scrubbing ends
                                                    HapticFeedback.lightImpact();
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),

                                          const SizedBox(height: 4),

                                          // Time labels
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 12),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                Text(
                                                  _formatDuration(
                                                      positionData.position),
                                                  style:
                                                      AppFontLoader.getJosefinStyle(
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        Colors.white.withOpacity(0.8),
                                                  ),
                                                ),
                                                Text(
                                                  _formatDuration(
                                                      positionData.duration),
                                                  style:
                                                      AppFontLoader.getJosefinStyle(
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        Colors.white.withOpacity(0.8),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  ),

                                  // Reduced spacing between progress bar and controls (was 0.03-0.04)
                                  SizedBox(height: screenHeight * (screenHeight > 800 ? 0.01 : 0.015)), // Further reduced by 5%

                                  // Enhanced playback controls with animations and feedback
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      // Previous button with ripple effect
                                      Material(
                                        color: Colors.transparent,
                                        borderRadius: BorderRadius.circular(30),
                                        child: InkWell(
                                          borderRadius: BorderRadius.circular(30),
                                          splashColor:
                                              AppColors.getPrimaryColorWithOpacity(
                                                  context, 0.3),
                                          highlightColor:
                                              Colors.white.withOpacity(0.05),
                                          onTap: () {
                                            audioProvider.audioService.playPrevious();
                                            HapticFeedback.mediumImpact();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.all(12.0),
                                            child: Icon(
                                              Icons.skip_previous,
                                              color: Colors.white,
                                              size: 34,
                                            ),
                                          ),
                                        ),
                                      ),

                                      // Play/Pause button with animated transition
                                      StreamBuilder<PlayerState>(
                                        stream: audioProvider
                                            .audioService.playerStateStream,
                                        builder: (context, snapshot) {
                                          final playerState = snapshot.data;
                                          final processingState =
                                              playerState?.processingState;
                                          final playing = playerState?.playing;

                                          // Loading/buffering state
                                          if (processingState ==
                                                  ProcessingState.loading ||
                                              processingState ==
                                                  ProcessingState.buffering) {
                                            return AnimatedContainer(
                                              duration:
                                                  const Duration(milliseconds: 300),
                                              width: 70,
                                              height: 70,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color:
                                                    AppColors.getPrimaryColor(context)
                                                        .withOpacity(0.9),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: AppColors
                                                        .getPrimaryColorWithOpacity(
                                                            context, 0.5),
                                                    blurRadius: 20,
                                                    spreadRadius: 2,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                ],
                                              ),
                                              child: const Center(
                                                child: SizedBox(
                                                  width: 24,
                                                  height: 24,
                                                  child: CircularProgressIndicator(
                                                    color: Colors.white,
                                                    strokeWidth: 2.5,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }

                                          // Play/pause button with animation
                                          return GestureDetector(
                                            onTap: () {
                                              if (playing == true) {
                                                audioProvider.pauseBhajan();
                                              } else {
                                                // Use audioProvider's resumeBhajan method which handles
                                                // both mini player visibility and resuming playback
                                                audioProvider.resumeBhajan();
                                              }
                                              // Haptic feedback
                                              HapticFeedback.mediumImpact();
                                            },
                                            child: AnimatedContainer(
                                              duration:
                                                  const Duration(milliseconds: 200),
                                              width: 70,
                                              height: 70,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: AppColors.getPrimaryColor(
                                                    context),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: AppColors
                                                        .getPrimaryColorWithOpacity(
                                                            context, 0.5),
                                                    blurRadius: 20,
                                                    spreadRadius: 2,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                ],
                                              ),
                                              child: Center(
                                                child: AnimatedSwitcher(
                                                  duration: const Duration(
                                                      milliseconds: 250),
                                                  transitionBuilder:
                                                      (child, animation) {
                                                    return ScaleTransition(
                                                      scale: animation,
                                                      child: FadeTransition(
                                                        opacity: animation,
                                                        child: child,
                                                      ),
                                                    );
                                                  },
                                                  child: Icon(
                                                    playing == true
                                                        ? Icons.pause
                                                        : Icons.play_arrow,
                                                    key: ValueKey<bool>(
                                                        playing == true),
                                                    color: Colors.white,
                                                    size: 38,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),

                                      // Next button with ripple effect
                                      Material(
                                        color: Colors.transparent,
                                        borderRadius: BorderRadius.circular(30),
                                        child: InkWell(
                                          borderRadius: BorderRadius.circular(30),
                                          splashColor:
                                              AppColors.getPrimaryColorWithOpacity(
                                                  context, 0.3),
                                          highlightColor:
                                              Colors.white.withOpacity(0.05),
                                          onTap: () {
                                            audioProvider.audioService.playNext();
                                            HapticFeedback.mediumImpact();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.all(12.0),
                                            child: Icon(
                                              Icons.skip_next,
                                              color: Colors.white,
                                              size: 34,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  // Adaptive spacing that's minimal on larger screens
                                  SizedBox(height: screenHeight * (screenHeight > 800 ? 0.025 : 0.035)), // Increased by 5%

                                  // Enhanced additional controls with better feedback
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      // Loop button
                                      StreamBuilder<LoopMode>(
                                        stream:
                                            audioProvider.audioService.loopModeStream,
                                        builder: (context, snapshot) {
                                          final loopMode =
                                              snapshot.data ?? LoopMode.off;
                                          IconData iconData;
                                          Color iconColor;
                                          String tooltip;

                                          switch (loopMode) {
                                            case LoopMode.off:
                                              iconData = Icons.repeat;
                                              iconColor =
                                                  Colors.white.withOpacity(0.6);
                                              tooltip = 'Repeat All';
                                              break;
                                            case LoopMode.all:
                                              iconData = Icons.repeat;
                                              iconColor =
                                                  AppColors.getPrimaryColor(context);
                                              tooltip = 'Repeat Current';
                                              break;
                                            case LoopMode.one:
                                              iconData = Icons.repeat_one;
                                              iconColor =
                                                  AppColors.getPrimaryColor(context);
                                              tooltip = 'Repeat Off';
                                              break;
                                          }

                                          return Material(
                                            color: Colors.transparent,
                                            borderRadius: BorderRadius.circular(20),
                                            child: Tooltip(
                                              message: tooltip,
                                              child: InkWell(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                splashColor: AppColors
                                                    .getPrimaryColorWithOpacity(
                                                        context, 0.2),
                                                highlightColor:
                                                    Colors.white.withOpacity(0.05),
                                                onTap: () {
                                                  audioProvider.audioService
                                                      .toggleRepeatMode();
                                                  HapticFeedback.selectionClick();
                                                },
                                                child: Padding(
                                                  padding: const EdgeInsets.all(10.0),
                                                  child: AnimatedSwitcher(
                                                    duration: const Duration(
                                                        milliseconds: 200),
                                                    child: Icon(
                                                      iconData,
                                                      key: ValueKey<LoopMode>(
                                                          loopMode),
                                                      color: iconColor,
                                                      size: 26,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),

                                      // Favorite button
                                      Material(
                                        color: Colors.transparent,
                                        borderRadius: BorderRadius.circular(20),
                                        child: InkWell(
                                          borderRadius: BorderRadius.circular(20),
                                          splashColor: Colors.red.withOpacity(0.2),
                                          highlightColor:
                                              Colors.white.withOpacity(0.05),
                                          onTap: () {
                                            _toggleFavorite();
                                            HapticFeedback.selectionClick();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.all(10.0),
                                            child: AnimatedSwitcher(
                                              duration: const Duration(
                                                  milliseconds: 200),
                                              transitionBuilder:
                                                  (child, animation) {
                                                return ScaleTransition(
                                                  scale: CurvedAnimation(
                                                    parent: animation,
                                                    curve: Curves.easeOutBack,
                                                  ),
                                                  child: FadeTransition(
                                                    opacity: animation,
                                                    child: child,
                                                  ),
                                                );
                                              },
                                              child: Icon(
                                                _isFavorite
                                                    ? Icons.favorite
                                                    : Icons.favorite_border,
                                                key:
                                                    ValueKey<bool>(_isFavorite),
                                                color: _isFavorite
                                                    ? Colors.red
                                                    : Colors.white
                                                        .withOpacity(0.8),
                                                size: 26,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),

                                      // Playlist button
                                      Material(
                                        color: Colors.transparent,
                                        borderRadius: BorderRadius.circular(20),
                                        child: Tooltip(
                                          message: 'Add to Playlist',
                                          child: InkWell(
                                            borderRadius: BorderRadius.circular(20),
                                            splashColor:
                                                AppColors.getPrimaryColorWithOpacity(
                                                    context, 0.2),
                                            highlightColor:
                                                Colors.white.withOpacity(0.05),
                                            onTap: () {
                                              _showPlaylistOptionsDialog();
                                              HapticFeedback.selectionClick();
                                            },
                                            child: Padding(
                                              padding: const EdgeInsets.all(10.0),
                                              child: Icon(
                                                Icons.playlist_add,
                                                color: Colors.white.withOpacity(0.8),
                                                size: 26,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  // Bottom padding to balance the layout
                                  SizedBox(height: screenHeight * 0.02),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
