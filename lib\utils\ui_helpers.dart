import 'package:flutter/material.dart';

class UIHelpers {
  // Private constructor to prevent instantiation
  UIHelpers._();

  /// Shows a styled snackbar with consistent appearance across the app
  /// Black background with white text
  static SnackBar getStyledSnackBar({
    required String message,
    Widget? icon,
    Duration duration = const Duration(seconds: 2),
  }) {
    return SnackBar(
      content: Row(
        children: [
          if (icon != null) ...[
            icon,
            const SizedBox(width: 12),
          ],
          Flexible(
            child: Text(
              message,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      backgroundColor: Colors.black,
      duration: duration,
      behavior: SnackBarBehavior.floating,
      elevation: 8.0,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    );
  }

  /// Shows a success snackbar with a check icon
  static SnackBar getSuccessSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 2),
  }) {
    return getStyledSnackBar(
      message: message,
      icon: Container(
        padding: const EdgeInsets.all(4),
        decoration: const BoxDecoration(
          color: Colors.green,
          shape: BoxShape.circle,
        ),
        child: const Icon(Icons.check, color: Colors.white, size: 18),
      ),
      duration: duration,
    );
  }

  /// Shows an error snackbar with an error icon
  static SnackBar getErrorSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 2),
  }) {
    return getStyledSnackBar(
      message: message,
      icon: Container(
        padding: const EdgeInsets.all(4),
        decoration: const BoxDecoration(
          color: Colors.red,
          shape: BoxShape.circle,
        ),
        child: const Icon(Icons.close, color: Colors.white, size: 18),
      ),
      duration: duration,
    );
  }

  /// Shows an info snackbar with an info icon
  static SnackBar getInfoSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 2),
  }) {
    return getStyledSnackBar(
      message: message,
      icon: Container(
        padding: const EdgeInsets.all(4),
        decoration: const BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
        ),
        child: const Icon(Icons.info, color: Colors.white, size: 18),
      ),
      duration: duration,
    );
  }
}
