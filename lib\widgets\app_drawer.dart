import 'package:flutter/material.dart';
import '../pages/date_converter_page.dart';
import '../pages/avatars_page.dart';
import '../pages/hindu_calendar_page.dart';
import '../pages/favorites_page.dart';
import '../pages/personalization_page.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';
import '../screens/sign_in_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/terms_of_service_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../services/audio_manager.dart';
import '../utils/ui_helpers.dart';
import 'about_dialog.dart';
import '../providers/auth_provider.dart' as app_provider;
import '../utils/navigation_service.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  // Show a confirmation dialog
  Future<void> _showLogoutConfirmation(BuildContext context) async {
    final bool confirmLogout = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1A1A1A),
            title: const Text('Logout', style: TextStyle(color: Colors.white)),
            content: const Text('Are you sure you want to logout?',
                style: TextStyle(color: Colors.white70)),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel',
                    style: TextStyle(color: Colors.white70)),
              ),
              ElevatedButton(
                style: AppColors.getPrimaryButtonStyle(context),
                onPressed: () => Navigator.pop(context, true),
                child:
                    const Text('Logout', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirmLogout) return;

    // If confirmed, proceed with logout
    if (context.mounted) {
      _performLogout(context);
    }
  }

  // Handle the actual logout process
  Future<void> _performLogout(BuildContext context) async {
    // Capture navigator key reference before any async operations
    final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
    navigatorKey.currentState ?? Navigator.of(context);

    bool hasCompleted = false;

    // Show loading dialog
    BuildContext? dialogContext;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        dialogContext = context;
        return const Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1B4332)),
            ),
          ),
        );
      },
    );

    try {
      // Close the drawer FIRST, before any async operations
      Navigator.pop(context);

      // Pause audio if playing
      try {
        final audioProvider =
            Provider.of<AudioProvider>(context, listen: false);
        if (audioProvider.isPlaying) {
          audioProvider.pauseBhajan();
        }

        final audioManager = AudioManager();
        if (audioManager.player?.playing == true) {
          await audioManager.player?.pause();
        }
      } catch (e) {
        debugPrint('Non-critical: Error handling audio: $e');
      }

      // Use AuthProvider to sign out
      try {
        final authProvider =
            Provider.of<app_provider.AuthProvider>(context, listen: false);
        await authProvider.signOut(context);
      } catch (e) {
        debugPrint('Error during sign out: $e');
      }

      // Safely close the dialog
      if (dialogContext != null && !hasCompleted) {
        try {
          Navigator.of(dialogContext!, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Non-critical: Error dismissing dialog: $e');
        }
      }

      // Navigate to SignInScreen using pushAndRemoveUntil
      Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
        MaterialPageRoute(builder: (_) => const SignInScreen()),
        (route) => false,
      );

      hasCompleted = true;
    } catch (error) {
      debugPrint('Error during logout flow: $error');

      // Dismiss dialog if not already dismissed
      if (dialogContext != null && !hasCompleted) {
        try {
          Navigator.of(dialogContext!, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Non-critical: Error dismissing dialog: $e');
        }
      }

      // Navigate to sign-in screen using rootNavigator
      if (!hasCompleted) {
        Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const SignInScreen()),
          (route) => false,
        );

        hasCompleted = true;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final User? currentUser = FirebaseAuth.instance.currentUser;

    return Drawer(
      backgroundColor: const Color(0xFF0D0D0D),
      child: Column(
        children: [
          // Header with app logo and title
          Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 16,
              bottom: 16,
              left: 16,
              right: 16,
            ),
            color: const Color(0xFF0D0D0D),
            child: Row(
              children: [
                Image.asset(
                  'assets/images/logo.png',
                  height: 52,
                  errorBuilder: (context, error, stackTrace) => const Icon(
                    Icons.account_circle,
                    size: 52,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Hindu Path',
                  style: AppFontLoader.getPrakrtaStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // User profile section (if signed in with Google)
          if (currentUser != null &&
              currentUser.providerData.any((p) => p.providerId == 'google.com'))
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: const Color(0xFF2A2A2A),
                    backgroundImage: currentUser.photoURL != null
                        ? NetworkImage(currentUser.photoURL!)
                        : null,
                    child: currentUser.photoURL == null
                        ? const Icon(Icons.person, color: Colors.white)
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentUser.displayName ?? 'User',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (currentUser.email != null)
                          Text(
                            currentUser.email!,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // Menu items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                ListTile(
                  leading: Image.asset(
                    'assets/icons/avatar.png',
                    width: 24,
                    height: 24,
                    color: Colors.white,
                  ),
                  title: const Text('Avatars',
                      style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const AvatarsPage()),
                    );
                  },
                ),
                ListTile(
                  leading:
                      const Icon(Icons.calendar_today, color: Colors.white),
                  title: const Text('Date Converter',
                      style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const DateConverterPage()),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.event_note, color: Colors.white),
                  title: const Text('Hindu Calendar',
                      style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    final navigator =
                        NavigationService.navigatorKey.currentState;
                    if (navigator != null) {
                      navigator.push(
                        MaterialPageRoute(
                          builder: (context) => const HinduCalendarPage(),
                        ),
                      );
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.favorite, color: Colors.white),
                  title: const Text('Favorites',
                      style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const FavoritesPage()),
                    );
                  },
                ),
                ListTile(
                  leading: Image.asset(
                    'assets/icons/theme.png',
                    width: 24,
                    height: 24,
                    color: Colors.white,
                  ),
                  title: const Text('Personalization',
                      style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const PersonalizationPage()),
                    );
                  },
                ),
                const Divider(color: Colors.white24),
                // Add GDPR compliance section
                if (currentUser != null)
                  Container(
                    margin: const EdgeInsets.only(
                      left: 16,
                      right: 16,
                      top: 24,
                      bottom: 8,
                    ),
                    child: const Text(
                      'ACCOUNT & PRIVACY',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                // Privacy Policy
                ListTile(
                  leading: const Icon(
                    Icons.privacy_tip_outlined,
                    color: Colors.white70,
                  ),
                  title: const Text(
                    'Privacy Policy',
                    style: TextStyle(color: Colors.white),
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close drawer
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                ),

                // Terms of Service
                ListTile(
                  leading: const Icon(
                    Icons.description_outlined,
                    color: Colors.white70,
                  ),
                  title: const Text(
                    'Terms of Service',
                    style: TextStyle(color: Colors.white),
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close drawer
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TermsOfServiceScreen(),
                      ),
                    );
                  },
                ),
                const Divider(color: Colors.white24),
                ListTile(
                  leading: const Icon(Icons.info, color: Colors.white),
                  title: const Text('About',
                      style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context); // Close drawer first
                    showDialog(
                      context: context,
                      builder: (context) => const CustomAboutDialog(),
                    );
                  },
                ),
              ],
            ),
          ),

          // Sign In or Logout button at bottom
          if (currentUser == null) ...[
            const Divider(color: Colors.white24),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton.icon(
                onPressed: () {
                  // Close drawer and navigate to sign in screen
                  Navigator.pop(context);
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (_) => const SignInScreen()),
                  );
                },
                style: AppColors.getPrimaryButtonStyle(
                  context,
                  minimumSize: const Size(double.infinity, 56),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: Image.asset(
                  'assets/icons/google.png',
                  width: 24,
                  height: 24,
                ),
                label: const Text(
                  'Sign In with Google',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ] else if (currentUser.providerData
              .any((p) => p.providerId == 'google.com')) ...[
            const Divider(color: Colors.white24),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton.icon(
                onPressed: () => _showLogoutConfirmation(context),
                style: AppColors.getPrimaryButtonStyle(
                  context,
                  minimumSize: const Size(double.infinity, 56),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.logout, color: Colors.white),
                label: const Text(
                  'Logout',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Show delete account confirmation dialog
  Future<void> _showDeleteAccountDialog(BuildContext context) async {
    final bool confirmDelete = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1A1A1A),
            title: const Text(
              'Delete Account',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'This will permanently delete your account and all associated data. This action cannot be undone.\n\nDo you want to proceed?',
              style: TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.white70),
                ),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                onPressed: () => Navigator.pop(context, true),
                child: const Text(
                  'Delete Account',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirmDelete) return;

    // If confirmed, proceed with account deletion
    if (context.mounted) {
      await _performAccountDeletion(context);
    }
  }

  // Handle actual account deletion
  Future<void> _performAccountDeletion(BuildContext context) async {
    // Close the drawer
    Navigator.pop(context);

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
          ),
        ),
      ),
    );

    try {
      final User? currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // Delete user data from Firestore first
        final authProvider =
            Provider.of<app_provider.AuthProvider>(context, listen: false);

        // Delete user account data
        await authProvider.deleteUserData(currentUser.uid);

        // Delete Firebase Auth account
        await currentUser.delete();

        // Dismiss loading dialog
        Navigator.pop(context);

        // Navigate to sign in screen
        Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const SignInScreen()),
          (route) => false,
        );

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            UIHelpers.getSuccessSnackBar(
              message: 'Your account has been successfully deleted',
            ),
          );
        }
      }
    } catch (error) {
      // Dismiss loading dialog
      Navigator.pop(context);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          UIHelpers.getErrorSnackBar(
            message: 'Failed to delete account: ${error.toString()}',
          ),
        );
      }
    }
  }
}
