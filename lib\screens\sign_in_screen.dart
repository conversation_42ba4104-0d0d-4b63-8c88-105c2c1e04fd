import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import 'dart:async';
import '../utils/ui_helpers.dart';
import '../services/audio_manager.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen>
    with TickerProviderStateMixin {
  bool _isLoading = false;
  late AnimationController _mainAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _loadingOpacityAnimation;

  // Animation controllers for secondary elements
  late AnimationController _guestButtonController;
  late AnimationController _privacyNoticeController;
  late Animation<double> _guestButtonOpacity;
  late Animation<double> _privacyNoticeOpacity;

  @override
  void initState() {
    super.initState();
    
    // Ensure any bhajan is stopped when sign-in screen appears
    AudioManager().forceStopAndReleasePlayer(AudioType.bhajan);
    
    // Main content animation controller
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // Loading overlay animation controller
    _loadingAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Guest button animation - Make it part of main animation
    _guestButtonController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
      value: 1.0, // Initialize to fully visible
    );

    // Privacy notice animation - Make it part of main animation
    _privacyNoticeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
      value: 1.0, // Initialize to fully visible
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: Curves.easeOut,
      ),
    );

    _loadingOpacityAnimation = Tween<double>(begin: 0.0, end: 0.6).animate(
      CurvedAnimation(
        parent: _loadingAnimationController,
        curve: Curves.easeOut,
      ),
    );

    _guestButtonOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _mainAnimationController, // Use main controller instead
        curve: Curves.easeOut,
      ),
    );

    _privacyNoticeOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _mainAnimationController, // Use main controller instead
        curve: Curves.easeOut,
      ),
    );

    // Just start the main animation - all elements will appear together
    _mainAnimationController.forward();

    // Clear loading state on init
    _isLoading = false;
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _loadingAnimationController.dispose();
    _guestButtonController.dispose();
    _privacyNoticeController.dispose();
    super.dispose();
  }

  Future<void> _handleGoogleSignIn(BuildContext context) async {
    if (_isLoading) return;

    // Start loading animation
    setState(() => _isLoading = true);
    await _loadingAnimationController.forward();

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.resetError();

      // Attempt sign-in
      final success = await authProvider.signInWithGoogle();

      if (!mounted) return;

      if (success) {
        // Keep loading state while loading favorites
        await authProvider.loadUserFavorites(context);

        // Fade out loading before navigation
        await _loadingAnimationController.reverse();
        if (mounted) {
          setState(() => _isLoading = false);
          Navigator.pushReplacementNamed(context, '/home');
        }
      } else {
        // Fade out loading before showing error
        await _loadingAnimationController.reverse();
        if (mounted) {
          setState(() => _isLoading = false);
          String errorMessage =
              authProvider.lastError ?? 'Failed to sign in with Google';
          ScaffoldMessenger.of(context)
              .showSnackBar(UIHelpers.getErrorSnackBar(message: errorMessage));
        }
      }
    } catch (e) {
      if (mounted) {
        // Fade out loading before showing error
        await _loadingAnimationController.reverse();
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
            UIHelpers.getErrorSnackBar(message: 'Error: ${e.toString()}'));
      }
    }
  }

  void _handleGuestSignIn(BuildContext context) {
    if (_isLoading) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    authProvider.signInAsGuest();
    Navigator.pushReplacementNamed(context, '/home');
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF0D0D0D),
        ),
        child: Stack(
          children: [
            // Main content
            SafeArea(
              child: FadeTransition(
                opacity: _fadeInAnimation,
                child: Stack(
                  children: [
                    // Decorative elements
                    Positioned(
                      top: size.height * 0.1,
                      left: -size.width * 0.2,
                      child: Container(
                        width: size.width * 0.6,
                        height: size.width * 0.6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.amber.withOpacity(0.05),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -size.width * 0.3,
                      right: -size.width * 0.3,
                      child: Container(
                        width: size.width * 0.8,
                        height: size.width * 0.8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.amber.withOpacity(0.03),
                        ),
                      ),
                    ),

                    // Main content
                    Center(
                      child: SingleChildScrollView(
                        physics: const ClampingScrollPhysics(),
                        padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.08,
                          vertical: size.height * 0.04,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Explore Hinduism text with animation
                            Transform.scale(
                              scale: 1.0,
                              child: const Text(
                                'Explore Hinduism',
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                  fontFamily: 'Prakrta',
                                  letterSpacing: 1.0,
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.001),
                            // Tagline with animated opacity
                            Opacity(
                              opacity: 1.0,
                              child: Text(
                                'Your spiritual journey begins here',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white.withOpacity(0.8),
                                  fontFamily: 'JosefinSans',
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.02),

                            // Logo with subtle animation
                            Transform.scale(
                              scale: 1.0,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: size.height * 0.008,
                                    horizontal: size.width * 0.05),
                                child: Image.asset(
                                  'assets/images/logo.png',
                                  height: size.height * 0.1725,
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.008),

                            // App name with shimmer effect
                            ShaderMask(
                              shaderCallback: (bounds) {
                                return const LinearGradient(
                                  colors: [
                                    Colors.white,
                                    Color(0xFFFFC107),
                                    Colors.white,
                                  ],
                                  stops: [0.0, 0.5, 1.0],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ).createShader(bounds);
                              },
                              child: const Text(
                                'Hindu Path',
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontFamily: 'Prakrta',
                                  letterSpacing: 1.2,
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.03),

                            // Sign in with Google button - Fixed height to prevent layout shifts
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              width: double.infinity,
                              height: 56,
                              child: ElevatedButton(
                                onPressed: _isLoading
                                    ? null
                                    : () => _handleGoogleSignIn(context),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  foregroundColor: Colors.black87,
                                  disabledBackgroundColor:
                                      Colors.white.withOpacity(0.7),
                                  disabledForegroundColor: Colors.black54,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.08,
                                    vertical: size.height * 0.018,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                ),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/icons/google.png',
                                          height: 24,
                                        ),
                                        SizedBox(width: size.width * 0.04),
                                        Text(
                                          'Continue with Google',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            fontFamily: 'JosefinSans',
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.012),

                            // Guest sign in button with fade animation - Clean professional design
                            FadeTransition(
                              opacity: _guestButtonOpacity,
                              child: Container(
                                width: double.infinity,
                                height: 56,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 1.5,
                                  ),
                                ),
                                child: TextButton(
                                  onPressed: _isLoading
                                      ? null
                                      : () => _handleGuestSignIn(context),
                                  style: TextButton.styleFrom(
                                    foregroundColor: Colors.white,
                                    backgroundColor: Colors.transparent,
                                    disabledForegroundColor:
                                        Colors.white.withOpacity(0.4),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: size.width * 0.08,
                                      vertical: size.height * 0.018,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(30),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        'assets/icons/guest.png',
                                        height: 22,
                                        color: _isLoading
                                            ? Colors.white.withOpacity(0.4)
                                            : Colors.white,
                                      ),
                                      SizedBox(width: size.width * 0.04),
                                      Text(
                                        'Continue as Guest',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'JosefinSans',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.015),

                            // Privacy notice with animation - Updated to match button styles
                            FadeTransition(
                              opacity: _privacyNoticeOpacity,
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: size.width * 0.08,
                                ),
                                child: Text(
                                  'By continuing, you agree to our Terms of Service and Privacy Policy',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white.withOpacity(0.6),
                                    fontFamily: 'JosefinSans',
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(height: size.height * 0.02),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Animated loading overlay - Fixed positioned element
            if (_isLoading)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.6),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF1E3A8A),
                      ),
                      strokeWidth: 3,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSignInButton({
    required VoidCallback? onPressed,
    required String imagePath,
    required String label,
    required bool isPrimary,
    required bool isLoading,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        boxShadow: isPrimary
            ? [
                BoxShadow(
                  color: const Color(0xFF1E3A8A).withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                )
              ]
            : null,
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary
              ? const Color(0xFF1E3A8A)
              : Colors.black.withOpacity(0.3),
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
            side: BorderSide(
              color: isPrimary
                  ? Colors.transparent
                  : Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading)
              const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.5,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            else
              Container(
                width: 24,
                height: 24,
                decoration: isPrimary
                    ? BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white,
                      )
                    : null,
                padding: isPrimary ? const EdgeInsets.all(4) : null,
                child: Image.asset(
                  imagePath,
                  width: 20,
                  height: 20,
                ),
              ),
            const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isPrimary ? FontWeight.bold : FontWeight.normal,
                color: Colors.white,
                fontFamily: 'JosefinSans',
                letterSpacing: 0.3,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
