import 'dart:async';

import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart' show AudioPlayer, PlayerState, ProcessingState, LoopMode, ConcatenatingAudioSource;
import 'package:rxdart/rxdart.dart';

import '../models/bhajan.dart';
import '../services/audio_manager.dart';

class PositionData {
  final Duration position;
  final Duration bufferedPosition;
  final Duration duration;

  PositionData(this.position, this.bufferedPosition, this.duration);
}

class AudioPlayerService {
  // Use the shared AudioManager instead of creating our own player
  final AudioManager _audioManager = AudioManager();
  AudioPlayer? _player;
  bool _isDismissing = false;
  bool _isScrubbing = false; // Track scrubbing state

  List<Bhajan> _bhajans = [];
  int _currentIndex = -1;
  bool _isInitialized = false;
  final _currentBhajanController = StreamController<Bhajan?>.broadcast();

  // Method to call when dismissing mini player
  void onDismiss() {
    debugPrint('AudioService: Dismissing player and resetting state');
    _isDismissing = true;
    if (_player != null && _player!.playing) {
      // Save current position before dismissing
      _lastPosition = _player!.position;
      debugPrint(
          'AudioService: Saved position before dismissal: $_lastPosition');

      // Pause playback
      _player!.pause().then((_) {
        debugPrint('AudioService: Player paused on dismissal');
      });
    }
  }

  // Method to reset dismissal state
  void resetDismissState() {
    _isDismissing = false;
  }

  // Keep track of last position when paused
  Duration? _lastPosition;

  Stream<PositionData> get positionDataStream {
    if (_player == null) {
      return Stream.value(
          PositionData(Duration.zero, Duration.zero, Duration.zero));
    }

    return Rx.combineLatest3<Duration, Duration, Duration?, PositionData>(
      _player!.positionStream,
      _player!.bufferedPositionStream,
      _player!.durationStream,
      (position, bufferedPosition, duration) =>
          PositionData(position, bufferedPosition, duration ?? Duration.zero),
    );
  }

  // Modified player state stream to handle scrubbing
  Stream<PlayerState> get playerStateStream {
    if (_player == null) {
      return Stream.value(PlayerState(false, ProcessingState.idle));
    }

    return _player!.playerStateStream.map((state) {
      // During scrubbing, maintain the current playing state
      // but don't show loading/buffering states
      if (_isScrubbing) {
        return PlayerState(state.playing, ProcessingState.ready);
      }
      return state;
    });
  }

  Stream<int?> get currentIndexStream =>
      _player?.currentIndexStream ?? Stream.value(null);
  Stream<double> get volumeStream => _player?.volumeStream ?? Stream.value(1.0);
  Stream<LoopMode> get loopModeStream =>
      _player?.loopModeStream ?? Stream.value(LoopMode.off);

  double get volume => _player?.volume ?? 1.0;
  bool get isPlaying => _player?.playing ?? false;
  Duration? get duration => _player?.duration;
  Duration get position => _player?.position ?? Duration.zero;
  AudioPlayer? get player => _player;
  Bhajan? get currentBhajan => getCurrentBhajan();
  bool get isInitialized => _isInitialized;

  // Expose the current bhajan stream
  Stream<Bhajan?> get currentBhajanStream => _currentBhajanController.stream;

  AudioPlayerService() {
    // Monitor audio type changes
    _audioManager.audioTypeStream.listen((audioType) {
      if (audioType != AudioType.bhajan) {
        // We no longer control the player
        _player = null;
      }
    });
  }

  Future<void> init(List<Bhajan> bhajans,
      {bool emitCurrentBhajan = true}) async {
    debugPrint('AudioService: Initializing with ${bhajans.length} bhajans');
    if (bhajans.isEmpty) {
      debugPrint('AudioService: Cannot initialize with empty bhajans list');
      throw Exception(
          'Cannot initialize audio service with empty bhajans list');
    }

    try {
      _bhajans = bhajans;

      // Get player from audio manager
      _player = await _audioManager.requestPlayer(AudioType.bhajan);

      if (_player == null) {
        throw Exception('Failed to get audio player for bhajans');
      }

      // Set up all player state listeners
      _setupPlayerListeners();

      // Create audio sources for first page of bhajans only (implementing lazy loading)
      // Only preload the first 5 bhajans or fewer if less are available
      final initialBhajansCount = bhajans.length > 5 ? 5 : bhajans.length;
      final initialBhajans = bhajans.sublist(0, initialBhajansCount);

      debugPrint(
          'AudioService: Creating initial playlist with $initialBhajansCount bhajans');
      final audioSources = await Future.wait(initialBhajans.map((bhajan) async {
        debugPrint(
            'AudioService: Creating audio source for bhajan: ${bhajan.title}');

        // Ensure the URL uses HTTPS
        var audioUrl = bhajan.r2Url;
        if (!audioUrl.startsWith('https://')) {
          audioUrl = audioUrl.replaceFirst('http://', 'https://');
        }

        // Use AudioManager to create AudioSource
        return await _audioManager.createAudioSource(
          url: audioUrl,
          id: bhajan.r2ObjectKey,
          title: bhajan.title,
          artist: bhajan.artist,
          artUri: Uri.parse(bhajan.artworkUrl),
          extras: {
            'duration': bhajan.duration,
            'type': 'bhajan',
          },
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
          },
        );
      }));

      final playlist = ConcatenatingAudioSource(
        children: audioSources,
      );

      debugPrint('AudioService: Setting initial audio source');

      // Add retry logic for setting audio source
      int maxRetries = 3;
      int currentTry = 0;
      while (currentTry < maxRetries) {
        try {
          debugPrint(
              'AudioService: Attempt ${currentTry + 1} to set audio source');
          await _player!.setAudioSource(playlist, preload: false);
          debugPrint('AudioService: Successfully set audio source');
          break;
        } catch (e) {
          currentTry++;
          debugPrint(
              'AudioService: Error setting audio source (attempt $currentTry): $e');
          debugPrint('AudioService: Stack trace: ${StackTrace.current}');
          if (currentTry == maxRetries) {
            rethrow;
          }
          debugPrint('AudioService: Waiting before retry...');
          await Future.delayed(
              Duration(seconds: 2 * currentTry)); // Exponential backoff
        }
      }

      // Set initial loop mode and other settings
      await _player!.setLoopMode(LoopMode.off);

      _isInitialized = true;
      _currentIndex = 0;

      // Emit current bhajan only if requested
      if (emitCurrentBhajan) {
        _currentBhajanController.add(getCurrentBhajan());
      }

      // If we have more bhajans than the initial preloaded ones, schedule adding the rest
      if (bhajans.length > initialBhajansCount) {
        _scheduleLoadRemainingBhajans(bhajans.sublist(initialBhajansCount));
      }

      debugPrint(
          'AudioService: Successfully initialized audio service with loop mode: ${_player!.loopMode}');
    } catch (e) {
      debugPrint('AudioService: Error during initialization: $e');
      debugPrint('AudioService: Stack trace: ${StackTrace.current}');
      _isInitialized = false;
      rethrow;
    }
  }

  // Load remaining bhajans in the background without blocking the UI
  Future<void> _scheduleLoadRemainingBhajans(
      List<Bhajan> remainingBhajans) async {
    // Wait a bit to let the UI settle and initial bhajans to be ready
    await Future.delayed(const Duration(seconds: 2));

    debugPrint(
        'AudioService: Loading remaining ${remainingBhajans.length} bhajans in the background');

    // Load the remaining bhajans in small chunks to avoid blocking the UI for too long
    const int chunkSize = 5;
    final chunks = <List<Bhajan>>[];

    for (var i = 0; i < remainingBhajans.length; i += chunkSize) {
      chunks.add(remainingBhajans.sublist(
          i,
          i + chunkSize < remainingBhajans.length
              ? i + chunkSize
              : remainingBhajans.length));
    }

    // Get the ConcatenatingAudioSource to add to
    final concatenatingSource =
        _player?.audioSource as ConcatenatingAudioSource?;
    if (concatenatingSource == null) {
      debugPrint(
          'AudioService: Cannot load remaining bhajans, player or audio source is null');
      return;
    }

    // Process each chunk with a delay between chunks
    for (var chunk in chunks) {
      try {
        final chunkAudioSources = await Future.wait(chunk.map((bhajan) async {
          var audioUrl = bhajan.r2Url;
          if (!audioUrl.startsWith('https://')) {
            audioUrl = audioUrl.replaceFirst('http://', 'https://');
          }

          return await _audioManager.createAudioSource(
            url: audioUrl,
            id: bhajan.r2ObjectKey,
            title: bhajan.title,
            artist: bhajan.artist,
            artUri: Uri.parse(bhajan.artworkUrl),
            extras: {
              'duration': bhajan.duration,
              'type': 'bhajan',
            },
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': '*/*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Connection': 'keep-alive',
            },
          );
        }));

        // Add this chunk to the playlist
        await concatenatingSource.addAll(chunkAudioSources);
        debugPrint(
            'AudioService: Added chunk of ${chunk.length} bhajans to playlist');

        // Small delay between chunks to not block UI
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        debugPrint('AudioService: Error adding chunk to playlist: $e');
        // Continue with next chunk even if this one failed
      }
    }

    debugPrint('AudioService: Finished loading all remaining bhajans');
  }

  /// Plays a bhajan at the specified index from the bhajans list.
  Future<void> play(int index) async {
    debugPrint(
        'AudioService: Starting play with dismissal state: $_isDismissing');
    try {
      // First, check if the bhajan index is valid
      if (index < 0 || index >= _bhajans.length) {
        debugPrint(
            'AudioService: Invalid index: $index, bhajans count: ${_bhajans.length}');
        throw Exception('Invalid bhajan index: $index');
      }

      // Track the original requested bhajan
      final requestedBhajan = _bhajans[index];

      // If we're starting a new playback session after dismissal
      if (_isDismissing) {
        debugPrint('AudioService: Starting new session after dismissal');
        // Use the saved position if we're playing the same track
        if (index == _currentIndex && _lastPosition != null) {
          debugPrint(
              'AudioService: Restoring position from dismissal: $_lastPosition');
        } else {
          debugPrint(
              'AudioService: New track after dismissal, starting from beginning');
          _lastPosition = Duration.zero;
        }
      } else if (index != _currentIndex) {
        // Playing a different track
        debugPrint(
            'AudioService: Playing different track, starting from beginning');
        _lastPosition = Duration.zero;
      } else if (_player?.position != null) {
        // Same track in active session
        _lastPosition = _player!.position;
        debugPrint(
            'AudioService: Preserving position ${_lastPosition} for same track');
      }

      _isDismissing = false; // Reset dismissal state

      // Set current index and emit immediately to trigger UI updates
      _currentIndex = index;
      _emitCurrentBhajanUpdate();

      debugPrint(
          'AudioService: Explicitly playing bhajan at index $index: ${requestedBhajan.title}');

      // If player is null, request a new one
      if (_player == null) {
        debugPrint('AudioService: Player is null, requesting new player');
        _player = await _audioManager.requestPlayer(AudioType.bhajan);

        if (_player == null) {
          debugPrint('AudioService: Failed to get player');
          throw Exception('Failed to get audio player for bhajans');
        }

        debugPrint('AudioService: Got new player, setting up listeners');

        // Set up player state listeners
        _setupPlayerListeners();

        // Setup playlist source with all bhajans
        await _setupPlaylistSource(
            initialIndex: index,
            initialPosition: _isDismissing ? Duration.zero : _lastPosition);
      } else {
        // Rebuild the playlist when explicitly selecting a track
        debugPrint(
            'AudioService: Rebuilding playlist with position: $_lastPosition, isDismissing: $_isDismissing');

        final startPosition = (_isDismissing || index != _currentIndex)
            ? Duration.zero
            : _lastPosition ?? Duration.zero;

        await _setupPlaylistSource(
            initialIndex: index, initialPosition: startPosition);
      }

      // Double check that the player is now at the correct index
      if (_player!.currentIndex != index) {
        debugPrint(
            'AudioService: Player index mismatch! Fixing to play requested bhajan');
        await _player!.seek(Duration.zero, index: index);
      }

      // If we have a saved position for the same track in the same session, use it
      if (_lastPosition != null && !_isDismissing && index == _currentIndex) {
        debugPrint(
            'AudioService: Resuming from saved position: $_lastPosition');
        await _player!.seek(_lastPosition!);
      } else {
        debugPrint('AudioService: Starting from beginning');
        await _player!.seek(Duration.zero);
      }

      // Start playback
      debugPrint('AudioService: Starting playback of ${requestedBhajan.title}');
      await _player!.play();

      // Emit again after playback starts
      _emitCurrentBhajanUpdate();

      // Verify one last time that we're playing the right track
      final playingBhajan = getCurrentBhajan();
      if (playingBhajan?.id != requestedBhajan.id) {
        debugPrint('AudioService: WARNING - Playing wrong bhajan! Fixing...');
        // Force the correct bhajan to play
        final correctIndex =
            _bhajans.indexWhere((b) => b.id == requestedBhajan.id);
        if (correctIndex >= 0) {
          await _player!.seek(Duration.zero, index: correctIndex);
          _currentIndex = correctIndex;
          _emitCurrentBhajanUpdate();
        }
      }

      debugPrint('AudioService: Playback started successfully');
    } catch (e) {
      debugPrint('AudioService: Error playing bhajan: $e');
      debugPrint('AudioService: Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  Future<void> pause() async {
    if (_player != null && _player!.playing) {
      // Save position and track information for accurate resuming
      final originalIndex = _player!.currentIndex;
      final originalPosition = _player!.position;
      final originalBhajan = originalIndex != null &&
              originalIndex >= 0 &&
              originalIndex < _bhajans.length
          ? _bhajans[originalIndex]
          : null;

      // Save both the index and the bhajan ID for double verification during resume
      if (originalIndex != null) {
        _currentIndex = originalIndex;
      }
      // Only save position if we're not dismissing the player
      if (!_isDismissing) {
        _lastPosition = originalPosition;
      }

      debugPrint(
          'AudioService: Pausing bhajan ${originalBhajan?.title} at index $_currentIndex, position: $_lastPosition');

      // Simple pause - no fancy logic that might cause issues
      await _player!.pause();

      // Verify that we're still paused at the same track
      final afterPauseIndex = _player!.currentIndex;
      if (afterPauseIndex != null && afterPauseIndex != originalIndex) {
        debugPrint(
            'AudioService: WARNING - Track changed during pause! Resetting...');
        _currentIndex = afterPauseIndex;
        _emitCurrentBhajanUpdate();
      }
    } else {
      debugPrint('AudioService: Player not playing, ignoring pause request');
    }
  }

  Future<void> resume() async {
    if (_player != null && !_player!.playing) {
      // Check if we have a valid index
      if (_currentIndex < 0 || _currentIndex >= _bhajans.length) {
        debugPrint(
            'AudioService: Invalid current index $_currentIndex for resume, cannot resume');
        return;
      }

      final bhajan = _bhajans[_currentIndex];
      debugPrint(
          'AudioService: Resuming bhajan ${bhajan.title} from position: $_lastPosition');

      // Ensure we're at the right index
      if (_player!.currentIndex != _currentIndex) {
        debugPrint('AudioService: Fixing index before resume');
        await _player!
            .seek(_lastPosition ?? Duration.zero, index: _currentIndex);
      } else if (_lastPosition != null) {
        // Only seek if we have a saved position
        await _player!.seek(_lastPosition!);
        debugPrint('AudioService: Seeking to saved position: $_lastPosition');
      }

      // Start playback
      await _player!.play();

      // Emit update
      _emitCurrentBhajanUpdate();
    } else if (_player == null) {
      debugPrint('AudioService: Player is null, cannot resume');
    } else {
      debugPrint(
          'AudioService: Player is already playing, ignoring resume request');
    }
  }

  /// Perform final seek when scrubbing ends
  /// Ensures accurate positioning and smooth state transition
  Future<void> seek(Duration position) async {
    if (_player != null) {
      try {
        final wasPlaying = _player!.playing;

        // End scrubbing state
        _isScrubbing = false;

        // Perform final accurate seek
        await _player!.seek(position, index: _player!.currentIndex);
        _lastPosition = position;

        // Ensure playback continues if needed
        if (wasPlaying && !_player!.playing) {
          await _player!.play();
        }

        debugPrint('AudioService: Final seek completed to position: $position');
      } catch (e) {
        debugPrint('AudioService: Error during final seek: $e');
        // Reset scrubbing state even on error
        _isScrubbing = false;
      }
    }
  }

  /// Perform immediate seek during active scrubbing
  /// Used for responsive UI updates while user is scrubbing
  Future<void> seekImmediate(Duration position) async {
    if (_player != null) {
      try {
        // Set scrubbing state to prevent UI flicker
        _isScrubbing = true;

        // Perform seek without waiting for completion
        _player!.seek(position, index: _player!.currentIndex);
        _lastPosition = position;
      } catch (e) {
        debugPrint('AudioService: Error during immediate seek: $e');
      }
    }
  }

  Future<void> playNext() async {
    if (_player != null) {
      // Always reset position to start when explicitly switching tracks
      _lastPosition = Duration.zero;

      // If in repeat one mode, we need to temporarily set it to off,
      // go to next track, then restore repeat one
      if (_player!.loopMode == LoopMode.one) {
        final savedLoopMode = _player!.loopMode;
        await _player!.setLoopMode(LoopMode.off);
        await _player!.seekToNext();
        await _player!.setLoopMode(savedLoopMode);
      } else {
        // If we're on the last track, loop back to the first track
        // This happens regardless of repeat mode to provide expected music player behavior
        final isLastTrack = _currentIndex >= _bhajans.length - 1 ||
            (_player!.currentIndex != null &&
                _player!.currentIndex! >= _bhajans.length - 1);

        if (isLastTrack) {
          debugPrint(
              'AudioService: At end of playlist, looping back to first track');
          await _player!.seek(Duration.zero, index: 0);

          // Ensure playback continues
          if (_player!.playing) {
            await _player!.play();
          }
        } else {
          // Normal case: not at the end of playlist
          // First seek to the beginning of the next track
          final nextIndex = (_player!.currentIndex ?? _currentIndex) + 1;
          if (nextIndex < _bhajans.length) {
            await _player!.seek(Duration.zero, index: nextIndex);
          } else {
            await _player!.seekToNext();
          }
        }
      }
      // Ensure currentBhajanStream is updated immediately
      _emitCurrentBhajanUpdate();
    }
  }

  Future<void> playPrevious() async {
    if (_player != null) {
      // Always reset position to start when explicitly switching tracks
      _lastPosition = Duration.zero;

      // If in repeat one mode, we need to temporarily set it to off,
      // go to previous track, then restore repeat one
      if (_player!.loopMode == LoopMode.one) {
        final savedLoopMode = _player!.loopMode;
        await _player!.setLoopMode(LoopMode.off);
        await _player!.seekToPrevious();
        await _player!.setLoopMode(savedLoopMode);
      } else {
        // If we're on the first track, loop to the last track
        // This happens regardless of repeat mode to provide expected music player behavior
        final isFirstTrack = _currentIndex <= 0 ||
            (_player!.currentIndex != null && _player!.currentIndex! <= 0);

        if (isFirstTrack) {
          debugPrint(
              'AudioService: At beginning of playlist, looping to last track');
          await _player!.seek(Duration.zero, index: _bhajans.length - 1);

          // Ensure playback continues
          if (_player!.playing) {
            await _player!.play();
          }
        } else {
          // Normal case: not at the beginning of playlist
          // First seek to the beginning of the previous track
          final prevIndex = (_player!.currentIndex ?? _currentIndex) - 1;
          if (prevIndex >= 0) {
            await _player!.seek(Duration.zero, index: prevIndex);
          } else {
            await _player!.seekToPrevious();
          }
        }
      }
      // Ensure currentBhajanStream is updated immediately
      _emitCurrentBhajanUpdate();
    }
  }

  Future<void> setVolume(double volume) async {
    if (_player != null) {
      await _player!.setVolume(volume);
    }
  }

  Future<void> toggleRepeatMode() async {
    if (_player != null) {
      // Cycle through repeat modes: none -> all -> one -> none
      switch (_player!.loopMode) {
        case LoopMode.off:
          await _player!.setLoopMode(LoopMode.all);
          break;
        case LoopMode.all:
          await _player!.setLoopMode(LoopMode.one);
          break;
        case LoopMode.one:
          await _player!.setLoopMode(LoopMode.off);
          break;
      }
    }
  }

  LoopMode? get loopMode => _player?.loopMode;

  Future<void> dispose() async {
    await _player?.dispose();
  }

  /// Updates the current list of bhajans with a new list.
  /// This is used when new bhajans are loaded or when a specific playlist is selected.
  Future<void> updateBhajans(List<Bhajan> newBhajans) async {
    if (newBhajans.isEmpty) {
      debugPrint('AudioService: Cannot update with empty bhajans list');
      return;
    }

    try {
      _bhajans = newBhajans;

      // If player is null, we need to request a new one
      if (_player == null) {
        _player = await _audioManager.requestPlayer(AudioType.bhajan);

        if (_player == null) {
          throw Exception('Failed to get audio player for bhajans');
        }

        // Set up player state listeners
        _setupPlayerListeners();
      }

      // Setup playlist source with initial bhajans
      await _setupPlaylistSource();

      // Reset current index to ensure we start with first bhajan
      _currentIndex = 0;
      _currentBhajanController.add(getCurrentBhajan());

      debugPrint('AudioService: Bhajans list updated successfully');
    } catch (e) {
      debugPrint('AudioService: Error updating bhajans: $e');
      throw Exception('Failed to update bhajans: $e');
    }
  }

  /// Sets up player event listeners for state changes
  void _setupPlayerListeners() {
    if (_player == null) return;

    // Flag to indicate we're in the middle of a playlist operation
    // This prevents unwanted track changes during playlist operations
    bool isPlaylistOperation = false;

    // Listen for player state changes - especially for handling track completion
    _player!.playerStateStream.listen((playerState) {
      // Skip state handling if we're in the middle of a playlist operation
      if (isPlaylistOperation) {
        return;
      }

      // Check if the track has completed playing (reached the end)
      final isCompleted =
          playerState.processingState == ProcessingState.completed;

      // Only handle completion when the player is actually playing or just stopped playing
      // This avoids accidentally triggering next track after pausing
      if (isCompleted) {
        debugPrint(
            'AudioService: Track completed, handling based on repeat mode');

        // Add a small delay to ensure it's really a completion and not a pause
        Future.delayed(Duration(milliseconds: 200), () {
          // Make sure player still exists and is still in completed state
          if (_player != null &&
              _player!.processingState == ProcessingState.completed) {
            // Set operation flag to prevent unwanted recursive track changes
            isPlaylistOperation = true;

            try {
              // Handle based on repeat mode
              if (_player!.loopMode == LoopMode.one) {
                // For repeat one, just restart the current track
                _player!.seek(Duration.zero);
                debugPrint(
                    'AudioService: Repeat One mode - Restarting current track');

                // If player was playing, ensure it continues playing
                if (_player!.playing) {
                  _player!.play();
                }
              } else if (_player!.loopMode == LoopMode.all ||
                  _currentIndex >= _bhajans.length - 1) {
                // For repeat all OR when we're at the last track (regardless of repeat mode)
                // Go back to the first track
                final resetToBeginning = _currentIndex >= _bhajans.length - 1;

                if (resetToBeginning) {
                  debugPrint(
                      'AudioService: Last track completed, restarting playlist from beginning');
                  _player!.seek(Duration.zero, index: 0);

                  // If player was playing, ensure it continues playing
                  if (_player!.playing) {
                    _player!.play();
                  }
                } else {
                  // Not at the end of the playlist yet, just play next track
                  debugPrint(
                      'AudioService: Track completed, moving to next track');
                  playNext();
                }
              } else {
                // Normal behavior - go to next track
                debugPrint(
                    'AudioService: Moving to next track after completion');
                playNext();
              }
            } finally {
              // Reset operation flag when done
              isPlaylistOperation = false;
            }
          }
        });
      }
    });

    // Listen for sequence state changes to update current bhajan
    _player!.sequenceStateStream.listen((sequenceState) {
      // Skip sequence state handling if we're in the middle of a playlist operation
      if (isPlaylistOperation) {
        return;
      }

      if (sequenceState != null) {
        final newIndex = sequenceState.currentIndex;

        // Only update if the index actually changed
        if (newIndex != null && _currentIndex != newIndex) {
          debugPrint(
              'AudioService: Sequence state changed index from $_currentIndex to $newIndex');

          // Set operation flag to prevent recursive state changes
          isPlaylistOperation = true;

          try {
            _currentIndex = newIndex;
            _currentBhajanController.add(getCurrentBhajan());
            debugPrint(
                'AudioService: Current bhajan updated to: ${getCurrentBhajan()?.title}');
          } finally {
            // Reset operation flag when done
            isPlaylistOperation = false;
          }
        }
      }
    });
  }

  /// Updates the playlist with new bhajans but keeps the current position
  /// This is used to add more bhajans to the existing playlist (e.g., pagination)
  Future<void> updatePlaylist(List<Bhajan> newBhajans,
      {bool emitCurrentBhajan = true}) async {
    try {
      if (newBhajans.isEmpty) {
        debugPrint('AudioService: Cannot update playlist with empty list');
        return;
      }

      _bhajans = newBhajans;

      // If player is null, initialize fully
      if (_player == null) {
        await init(newBhajans, emitCurrentBhajan: emitCurrentBhajan);
        return;
      }

      // If playing, remember current bhajan to restore position
      final currentBhajan = getCurrentBhajan();
      final wasPlaying = _player!.playing;
      final position = _player!.position;

      // Setup new playlist source
      await _setupPlaylistSource();

      // If we had a current bhajan, try to find it in the new list
      if (currentBhajan != null) {
        final newIndex = _bhajans.indexWhere((b) => b.id == currentBhajan.id);
        if (newIndex >= 0) {
          _currentIndex = newIndex;
          await _player!.seek(position, index: newIndex);

          // Resume playback if it was playing
          if (wasPlaying) {
            await _player!.play();
          }
        }
      }

      // Only emit current bhajan update if requested
      if (emitCurrentBhajan) {
        _currentBhajanController.add(getCurrentBhajan());
      }

      debugPrint('AudioService: Playlist updated successfully');
    } catch (e) {
      debugPrint('AudioService: Error updating playlist: $e');
      throw Exception('Failed to update playlist: $e');
    }
  }

  /// Resets the current bhajan reference
  /// Used when mini player is dismissed
  void resetCurrentBhajan() {
    _currentIndex = -1;
    // Clear the last position to avoid position restoration issues on next play
    _lastPosition = null;

    // Release any resources
    if (_player != null) {
      _player!.pause();
    }

    _currentBhajanController.add(null);
    debugPrint(
        'AudioService: Current bhajan reference and position completely reset');
  }

  /// Creates a temporary playlist for playback without modifying the main bhajans list
  /// This is used for playing specific playlists without affecting the main list
  Future<void> createTemporaryPlaylist(
      List<Bhajan> playlist, int startIndex) async {
    if (playlist.isEmpty) {
      debugPrint(
          'AudioService: Cannot create temporary playlist with empty list');
      return;
    }

    try {
      debugPrint(
          'AudioService: Creating temporary playlist with ${playlist.length} bhajans');

      // Check if we're playing the same bhajan as before
      final currentBhajan = getCurrentBhajan();
      final targetBhajan =
          playlist.length > startIndex ? playlist[startIndex] : null;
      final isSameBhajan = currentBhajan != null &&
          targetBhajan != null &&
          currentBhajan.id == targetBhajan.id &&
          _player != null;

      // Save the current position if we're playing the same bhajan
      final currentPosition = isSameBhajan ? _player!.position : null;

      if (isSameBhajan && currentPosition != null) {
        debugPrint(
            'AudioService: Creating temporary playlist with same bhajan, saving position: $currentPosition');
      }

      // Store the original bhajans list but don't restore it immediately
      // This allows the temporary playlist to be properly looped with repeat all mode
      List<Bhajan> originalBhajans = List.from(_bhajans);

      // Set _bhajans to the playlist for playback
      _bhajans = List.from(playlist);

      // If player is null, we need to request a new one
      if (_player == null) {
        _player = await _audioManager.requestPlayer(AudioType.bhajan);

        if (_player == null) {
          throw Exception('Failed to get audio player for bhajans');
        }

        // Set up player state listeners
        _setupPlayerListeners();
      }

      // Setup playlist source with the provided list
      // Start from beginning for new playlist, preserve position for same session
      await _setupPlaylistSource(
          initialIndex: startIndex,
          initialPosition: Duration.zero,
          isNewPlaylist: true // Indicate this is a playlist operation
          );

      // Set current index to the start index
      _currentIndex = startIndex;

      // If it's the same bhajan and we have a position, seek to it
      if (isSameBhajan &&
          currentPosition != null &&
          currentPosition.inMilliseconds > 500) {
        debugPrint(
            'AudioService: Seeking to previous position for same bhajan: $currentPosition');
        await _player!.seek(currentPosition);
      }

      // Start playback
      await _player!.play();

      // Emit current bhajan update
      _emitCurrentBhajanUpdate();

      // Keep using the temporary playlist until user changes to another list
      // No longer restoring the original bhajans list immediately

      debugPrint('AudioService: Temporary playlist created and playing');
    } catch (e) {
      debugPrint('AudioService: Error creating temporary playlist: $e');
      throw Exception('Failed to create temporary playlist: $e');
    }
  }

  // Helper method to set up playlist source
  Future<void> _setupPlaylistSource(
      {int? initialIndex,
      Duration? initialPosition,
      bool isNewPlaylist = false}) async {
    try {
      debugPrint('AudioService: Setting up playlist source');

      // Remember the current bhajan ID to check if we're playing the same bhajan
      final currentBhajan = getCurrentBhajan();

      // Get the target index and position
      final targetIndex = initialIndex ?? _currentIndex;

      // Check if we're rebuilding for the same bhajan
      final targetBhajan = _bhajans.length > targetIndex && targetIndex >= 0
          ? _bhajans[targetIndex]
          : null;
      final isSameBhajan = currentBhajan != null &&
          targetBhajan != null &&
          currentBhajan.id == targetBhajan.id;

      // Determine position based on context
      final position = isNewPlaylist
          ? Duration.zero
          : // Always start from beginning for playlists
          initialPosition != null
              ? initialPosition
              : // Use provided position if available
              (isSameBhajan && _player != null)
                  ? _player!.position
                  : // Preserve position in same session
                  Duration.zero; // Default to beginning

      // Log what we're doing
      if (isSameBhajan && position != null && position.inMilliseconds > 500) {
        debugPrint(
            'AudioService: Rebuilding playlist for same bhajan at position $position');
      } else if (!isSameBhajan) {
        debugPrint(
            'AudioService: Rebuilding playlist for different bhajan, starting from beginning');
      }

      // Create playlist items for each bhajan
      final audioSources = await Future.wait(_bhajans.map((bhajan) async {
        // Ensure the URL uses HTTPS
        var audioUrl = bhajan.r2Url;
        if (!audioUrl.startsWith('https://')) {
          audioUrl = audioUrl.replaceFirst('http://', 'https://');
        }

        // Use AudioManager to create AudioSource
        return await _audioManager.createAudioSource(
          url: audioUrl,
          id: bhajan.r2ObjectKey,
          title: bhajan.title,
          artist: bhajan.artist,
          artUri: Uri.parse(bhajan.artworkUrl),
          extras: {
            'duration': bhajan.duration,
            'type': 'bhajan',
          },
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
          },
        );
      }));

      final playlist = ConcatenatingAudioSource(
        children: audioSources,
      );

      // Set the audio source
      debugPrint(
          'AudioService: Setting audio source for player with index $targetIndex');
      await _player!.setAudioSource(playlist, initialIndex: targetIndex);

      // If we have a position to restore and it's meaningful, seek to it
      if (position != null && position.inMilliseconds > 500) {
        debugPrint('AudioService: Seeking to position: $position');
        await _player!.seek(position);
      }

      debugPrint('AudioService: Playlist source set up successfully');
    } catch (e) {
      debugPrint('AudioService: Error setting up playlist source: $e');
      throw Exception('Failed to set up playlist source: $e');
    }
  }

  Bhajan? getCurrentBhajan() {
    if (_currentIndex < 0 || _currentIndex >= _bhajans.length) return null;
    return _bhajans[_currentIndex];
  }

  List<Bhajan> getPlaylist() => _bhajans;

  // Helper method to consistently emit current bhajan updates
  void _emitCurrentBhajanUpdate() {
    final bhajan = getCurrentBhajan();
    _currentBhajanController.add(bhajan);
    if (bhajan != null) {
      debugPrint('AudioService: Current bhajan updated to: ${bhajan.title}');
    } else {
      debugPrint('AudioService: Current bhajan cleared');
    }
  }

  /// A convenient method for toggling play/pause state
  /// This is ideal for play/pause button in the mini player
  Future<void> togglePlayPause() async {
    if (_player == null) {
      debugPrint('AudioService: Player is null, cannot toggle play/pause');
      return;
    }

    if (_player!.playing) {
      // If playing, pause
      debugPrint('AudioService: Toggle - Currently playing, will pause');
      await pause();
    } else {
      // If paused, resume
      debugPrint('AudioService: Toggle - Currently paused, will resume');
      await resume();
    }
  }

  // Hide notification without stopping playback
  Future<void> hideNotification() async {
    try {
      if (_player != null) {
        // In JustAudio, stopping the player also hides the notification
        // but we want to keep our player object alive

        // Save current state for potential recovery
        final wasPlaying = _player!.playing;
        final savedPosition = _player!.position;
        final savedIndex = _player!.currentIndex;
        final savedBhajan = getCurrentBhajan();

        debugPrint('AudioService: Saving state before hiding notification: ' +
            'position=${savedPosition.inSeconds}s, ' +
            'index=$savedIndex, ' +
            'bhajan=${savedBhajan?.title}');

        // Call stop to hide notification
        await _player!.stop();

        // We intentionally don't call player.dispose() to keep player resources

        // Update internal state
        if (savedIndex != null) {
          _currentIndex = savedIndex;
        }

        // Save state in case we need to restore later
        _lastPosition = savedPosition;

        // Log hide notification action
        debugPrint('AudioService: Notification hidden, player state preserved');

        // Emit current bhajan update to ensure UI is consistent
        _emitCurrentBhajanUpdate();
      }
    } catch (e) {
      debugPrint('AudioService: Error hiding notification: $e');
    }
  }

  // Restart playback after notification was hidden
  Future<void> restartPlaybackAfterNotificationHide(
      int? index, Duration? position) async {
    try {
      if (_player != null && index != null && position != null) {
        // Try to restart playback at the saved position and index
        await _player!.seek(position, index: index);
        await _player!.play();
        debugPrint(
            'AudioService: Playback restarted after notification was hidden');
      }
    } catch (e) {
      debugPrint('AudioService: Error restarting playback: $e');
    }
  }
}
