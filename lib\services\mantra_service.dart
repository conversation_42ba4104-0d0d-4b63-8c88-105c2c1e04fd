import '../models/mantra.dart';
import '../data/mantra_data.dart' as data;

class MantraService {
  static List<Mantra> loadMantras() {
    try {
      return data.MantraData.categories
          .expand((category) => category.mantras.map((m) => Mantra(
                title: m.title,
                sanskrit: m.sanskrit,
                english: m.english,
                icon: Mantra.getIconForCategory(category.title),
              )))
          .toList();
    } catch (e) {
      // Return empty list in case of error
      print('Error loading mantras: $e');
      return [];
    }
  }

  static List<String> getCategories(List<Mantra> mantras) {
    try {
      return data.MantraData.categories
          .map((category) => category.title)
          .toList();
    } catch (e) {
      // Return empty list in case of error
      print('Error getting categories: $e');
      return [];
    }
  }

  static List<Mantra> getMantrasByCategory(
      List<Mantra> mantras, String category) {
    try {
      final categoryData = data.MantraData.categories.firstWhere(
        (c) => c.title == category,
        orElse: () => data.MantraCategory(title: '', mantras: []),
      );
      return categoryData.mantras
          .map((m) => Mantra(
                title: m.title,
                sanskrit: m.sanskrit,
                english: m.english,
                icon: Mantra.getIconForCategory(category),
              ))
          .toList();
    } catch (e) {
      // Return empty list in case of error
      print('Error getting mantras by category: $e');
      return [];
    }
  }

  static List<Mantra> searchMantras(List<Mantra> mantras, String query) {
    try {
      final lowercaseQuery = query.toLowerCase();
      return mantras.where((mantra) {
        return mantra.title.toLowerCase().contains(lowercaseQuery) ||
            mantra.sanskrit.toLowerCase().contains(lowercaseQuery) ||
            mantra.english.toLowerCase().contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      // Return original list in case of error
      print('Error searching mantras: $e');
      return mantras;
    }
  }
}
