import 'package:cloud_firestore/cloud_firestore.dart';

class Avatar {
  final String id;
  final String filename;
  final String url;
  final String size;
  final String r2ObjectKey;
  final DateTime uploadDate;

  Avatar({
    required this.id,
    required this.filename,
    required this.url,
    required this.size,
    required this.r2ObjectKey,
    required this.uploadDate,
  });

  factory Avatar.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return Avatar(
      id: doc.id,
      filename: data['filename'] ?? '',
      url: data['url'] ?? '',
      size: data['size'] ?? '',
      r2ObjectKey: data['r2ObjectKey'] ?? '',
      uploadDate: (data['uploadDate'] as Timestamp).toDate(),
    );
  }

  // Method to fix and normalize URL
  String get normalizedUrl {
    if (url.isEmpty) return '';

    // Fix encoding issues - decode URL to handle %28 and %29 sequences
    try {
      final decodedUrl = Uri.decodeFull(url);
      return decodedUrl;
    } catch (e) {
      print('Error normalizing URL: $e');
      return url; // Return original URL if decoding fails
    }
  }

  // Create a new Avatar with fixed URL
  factory Avatar.withFixedUrl(Avatar original) {
    return Avatar(
      id: original.id,
      filename: original.filename,
      url: original.normalizedUrl,
      size: original.size,
      r2ObjectKey: original.r2ObjectKey,
      uploadDate: original.uploadDate,
    );
  }

  @override
  String toString() {
    return 'Avatar{id: $id, filename: $filename, url: $url}';
  }
}
