import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'bhajan.dart';

class Playlist {
  final String id;
  final String name;
  final String description;
  final String userId;
  final List<String> bhajanIds;
  final String imageUrl;
  final String? localImagePath;
  final DateTime createdAt;
  final DateTime updatedAt;

  Playlist({
    required this.id,
    required this.name,
    this.description = '',
    required this.userId,
    this.bhajanIds = const [],
    this.imageUrl = '',
    this.localImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Create a copy of this playlist with updated properties
  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    String? userId,
    List<String>? bhajanIds,
    String? imageUrl,
    String? localImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      userId: userId ?? this.userId,
      bhajanIds: bhajanIds ?? this.bhajanIds,
      imageUrl: imageUrl ?? this.imageUrl,
      localImagePath: localImagePath ?? this.localImagePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Convert playlist to a map for Firestore storage
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'userId': userId,
      'bhajanIds': bhajanIds,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  // Create a playlist from a Firestore document
  factory Playlist.fromFirestore(DocumentSnapshot doc) {
    try {
      print('Converting Firestore doc to Playlist: ${doc.id}');
      final data = doc.data() as Map<String, dynamic>;
      print('Firestore data: $data');

      final playlist = Playlist(
        id: doc.id,
        name: data['name'] ?? '',
        description: data['description'] ?? '',
        userId: data['userId'] ?? '',
        bhajanIds: List<String>.from(data['bhajanIds'] ?? []),
        imageUrl: data['imageUrl'] ?? '',
        createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      );

      print('Successfully converted playlist: ${playlist.toString()}');
      return playlist;
    } catch (e) {
      print('Error converting Firestore doc to Playlist: $e');
      print('Document ID: ${doc.id}');
      print('Document data: ${doc.data()}');
      print('Stack trace: ${StackTrace.current}');

      // Return a default playlist in case of error to prevent app crashes
      return Playlist(
        id: doc.id,
        name: 'Error loading playlist',
        userId: '',
      );
    }
  }

  // Add a bhajan to the playlist
  Playlist addBhajan(String bhajanId) {
    if (bhajanIds.contains(bhajanId)) {
      return this;
    }

    final updatedBhajanIds = List<String>.from(bhajanIds);
    updatedBhajanIds.add(bhajanId);

    return copyWith(
      bhajanIds: updatedBhajanIds,
      updatedAt: DateTime.now(),
    );
  }

  // Remove a bhajan from the playlist
  Playlist removeBhajan(String bhajanId) {
    final updatedBhajanIds = List<String>.from(bhajanIds);
    updatedBhajanIds.remove(bhajanId);

    return copyWith(
      bhajanIds: updatedBhajanIds,
      updatedAt: DateTime.now(),
    );
  }

  // Update local image path
  Playlist updateLocalImage(String? path) {
    return copyWith(
      localImagePath: path,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, bhajanCount: ${bhajanIds.length})';
  }
}
