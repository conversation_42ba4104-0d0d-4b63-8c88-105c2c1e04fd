import 'package:flutter/material.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final primaryColor = AppColors.getPrimaryColor(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Terms of Service',
          style: AppFontLoader.getPrakrtaStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: const Color(0xFF0D0D0D),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms of Service',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '1. Acceptance of Terms',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'By accessing and using Hindu Path app, you agree to be bound by these Terms of Service. If you do not agree with any part of these terms, please do not use our app. The app provides access to religious content including bhajans, wallpapers, and display pictures.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '2. Content Usage and License',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Bhajans: You may listen to bhajans for personal, non-commercial use\n'
              '• Wallpapers: You may download and set wallpapers for personal use\n'
              '• Display Pictures: You may download and use DPs for personal profiles\n'
              '• Offline Access: You may cache content for offline use\n'
              '• Restrictions: You may not:\n'
              '  - Share or distribute content without permission\n'
              '  - Use content for commercial purposes\n'
              '  - Modify or create derivative works\n'
              '  - Remove copyright notices',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '3. User Responsibilities',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Users are responsible for:\n'
              '• Maintaining the confidentiality of their Google account\n'
              '• All activities that occur under their account\n'
              '• Managing notification preferences\n'
              '• Managing storage space for downloaded content\n'
              '• Ensuring their device meets the app\'s requirements\n'
              '• Respecting religious sentiments in content usage',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '4. App Features and Usage',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Notifications: The app sends scheduled notifications for:\n'
              '  - Daily bhajan reminders\n'
              '  - New wallpapers and DPs\n'
              '• Audio Playback: Supports background playback of bhajans\n'
              '• Storage: Manages local storage for offline content\n'
              '• Updates: Regular content updates and app improvements\n'
              '• Permissions: Required for full app functionality',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '5. Content Guidelines',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Users must not:\n'
              '• Share inappropriate or offensive content\n'
              '• Violate any intellectual property rights\n'
              '• Use the app for any illegal purposes\n'
              '• Attempt to reverse engineer the app\n'
              '• Share or distribute religious content without permission\n'
              '• Use content in a way that disrespects religious sentiments\n'
              '• Modify or alter religious content',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '6. Disclaimer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• The app provides religious content for personal use only\n'
              '• We do not guarantee the accuracy of religious content\n'
              '• Users should verify religious information independently\n'
              '• The app is provided "as is" without warranties\n'
              '• We are not responsible for any religious disputes\n'
              '• Content may be updated or modified without notice',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '7. Contact Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'If you have any questions about these Terms of Service, please contact us at:\n'
              'Email: <EMAIL>',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
