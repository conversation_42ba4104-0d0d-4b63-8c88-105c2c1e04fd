class NameData {
  final String name;
  final String nameInDevanagari;
  final String meaning;
  final String gender;

  NameData({
    required this.name,
    required this.nameInDevanagari,
    required this.meaning,
    required this.gender,
  });

  factory NameData.fromJson(Map<String, dynamic> json) {
    return NameData(
      name: json['Name'] ?? '',
      nameInDevanagari: json['Name in Devanagari'] ?? '',
      meaning: json['Meaning'] ?? '',
      gender: json['Gender'] ?? '',
    );
  }
}
