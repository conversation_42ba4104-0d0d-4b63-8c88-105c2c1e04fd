import 'package:flutter/material.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';

class CustomAboutDialog extends StatelessWidget {
  const CustomAboutDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final primaryColor = AppColors.getPrimaryColor(context);

    return Dialog(
      backgroundColor: const Color(0xFF0D0D0D),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Studio Habre Logo
            Image.asset(
              'assets/images/studio_habre.png',
              height: 80,
              width: 80,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 80,
                width: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.image_outlined,
                  size: 40,
                  color: Colors.white54,
                ),
              ),
            ),
            const SizedBox(height: 24),
            // App Logo Text
            Text(
              'Hindu Path',
              style: AppFontLoader.getPrakrtaStyle(
                fontSize: 32,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            // Version Text
            const Text(
              'Version 1.0.0',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 24),
            // Description Text
            const Text(
              'Hindu Path is your spiritual companion, offering a collection of divine wallpapers, bhajans, and religious information to help you stay connected with your faith.',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 24),
            // Copyright Text
            const Text(
              '2025 Studio Habre',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            // Close Button
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    primaryColor,
                    primaryColor.withOpacity(0.8),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  minimumSize: const Size(120, 40),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: const Text(
                  'Close',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
