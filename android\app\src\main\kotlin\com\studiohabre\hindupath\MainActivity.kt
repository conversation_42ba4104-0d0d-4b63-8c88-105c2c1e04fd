package com.studiohabre.hindupath

import android.content.ContentValues
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import androidx.annotation.NonNull
import com.ryanheise.audioservice.AudioServiceActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

class MainActivity: AudioServiceActivity() {
    private val CHANNEL = "com.studiohabre.hindupath/gallery"
    private val TAG = "MainActivity"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "saveToGallery") {
                val filePath = call.argument<String>("filePath")
                if (filePath == null) {
                    result.error("INVALID_ARGUMENT", "File path is required", null)
                    return@setMethodCallHandler
                }
                try {
                    saveToGallery(filePath)
                    result.success("File saved to gallery")
                } catch (e: Exception) {
                    Log.e(TAG, "Error saving file: ${e.message}", e)
                    result.error("SAVE_FAILED", e.message, null)
                }
            } else {
                result.notImplemented()
            }
        }
    }
    
    private val gallerySaver by lazy { HindupathGallerySaver(this) }
    
    @Throws(Exception::class)
    private fun saveToGallery(filePath: String) {
        gallerySaver.saveImage(filePath)
    }
}
