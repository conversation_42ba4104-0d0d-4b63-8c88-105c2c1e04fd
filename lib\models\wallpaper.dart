import 'package:cloud_firestore/cloud_firestore.dart';

class Wallpaper {
  final String id;
  final String url;
  final String? thumbnailUrl;
  final String filename;
  final String category;
  final bool featured;
  final DateTime uploadDate;

  Wallpaper({
    required this.id,
    required this.url,
    this.thumbnailUrl,
    required this.filename,
    required this.category,
    this.featured = false,
    DateTime? uploadDate,
  }) : uploadDate = uploadDate ?? DateTime.now();

  String get effectiveThumbnailUrl => thumbnailUrl ?? url;

  factory Wallpaper.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Wallpaper(
      id: doc.id,
      url: data['url'] ?? '',
      thumbnailUrl: data['thumbnailUrl'],
      filename: data['filename'] ?? '',
      category: data['category'] ?? 'Other',
      featured: data['featured'] ?? false,
      uploadDate:
          (data['uploadDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'url': url,
      'thumbnailUrl': thumbnailUrl,
      'filename': filename,
      'category': category,
      'featured': featured,
      'uploadDate': Timestamp.fromDate(uploadDate),
    };
  }

  @override
  String toString() {
    return 'Wallpaper{id: $id, category: $category, filename: $filename, url: $url}';
  }
}
