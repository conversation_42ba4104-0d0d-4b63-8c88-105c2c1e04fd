import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'dart:math';
import '../interfaces/audio_cache_interface.dart';

/// Implementation of the audio caching service
class AudioCacheService implements IAudioCacheService {
  // Singleton pattern
  static final AudioCacheService _instance = AudioCacheService._internal();
  factory AudioCacheService() => _instance;
  
  // Internal directory for caching
  Directory? _cacheDir;
  
  AudioCacheService._internal() {
    _initCacheDir();
  }
  
  Future<void> _initCacheDir() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDir = Directory('${appDir.path}/audio_cache');
      if (!_cacheDir!.existsSync()) {
        _cacheDir!.createSync();
      }
      debugPrint('AudioCacheService: Cache directory initialized at ${_cacheDir!.path}');
    } catch (e) {
      debugPrint('AudioCacheService: Error initializing cache directory: $e');
    }
  }
  
  @override
  Future<File?> getCachedFile(String fileId) async {
    if (_cacheDir == null) await _initCacheDir();
    
    final cacheFile = File('${_cacheDir!.path}/$fileId.mp3');
    if (await cacheFile.exists()) {
      return cacheFile;
    }
    return null;
  }
  
  @override
  Future<File> cacheFile(String url, String fileId) async {
    if (_cacheDir == null) await _initCacheDir();
    final cacheFile = File('${_cacheDir!.path}/$fileId.mp3');
    
    // If file already exists, return it
    if (await cacheFile.exists()) {
      return cacheFile;
    }
    
    // Download the file
    try {
      debugPrint('AudioCacheService: Downloading file $url');
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        await cacheFile.writeAsBytes(response.bodyBytes);
        debugPrint('AudioCacheService: File cached at ${cacheFile.path}');
        return cacheFile;
      } else {
        throw Exception('Failed to download file: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('AudioCacheService: Error caching file: $e');
      throw Exception('Error caching file: $e');
    }
  }
  
  @override
  Future<bool> isFileCached(String fileId) async {
    if (_cacheDir == null) await _initCacheDir();
    final cacheFile = File('${_cacheDir!.path}/$fileId.mp3');
    return cacheFile.exists();
  }
  
  @override
  Future<void> removeCachedFile(String fileId) async {
    if (_cacheDir == null) await _initCacheDir();
    final cacheFile = File('${_cacheDir!.path}/$fileId.mp3');
    if (await cacheFile.exists()) {
      await cacheFile.delete();
      debugPrint('AudioCacheService: Removed cached file $fileId');
    }
  }
  
  @override
  Future<void> clearCache() async {
    if (_cacheDir == null) await _initCacheDir();
    
    try {
      final files = _cacheDir!.listSync();
      for (var file in files) {
        if (file is File) {
          await file.delete();
        }
      }
      debugPrint('AudioCacheService: Cache cleared');
    } catch (e) {
      debugPrint('AudioCacheService: Error clearing cache: $e');
    }
  }
  
  @override
  Future<Directory> getCacheDirectory() async {
    if (_cacheDir == null) await _initCacheDir();
    return _cacheDir!;
  }
  
  @override
  Future<int> getCacheSize() async {
    if (_cacheDir == null) await _initCacheDir();
    
    int totalSize = 0;
    try {
      final files = _cacheDir!.listSync();
      for (var file in files) {
        if (file is File) {
          totalSize += file.lengthSync();
        }
      }
    } catch (e) {
      debugPrint('AudioCacheService: Error getting cache size: $e');
    }
    
    return totalSize;
  }
  
  @override
  Future<void> pruneCache(int maxSizeBytes) async {
    if (_cacheDir == null) await _initCacheDir();
    
    try {
      final files = _cacheDir!.listSync();
      
      // Skip pruning if cache is below the limit
      int totalSize = 0;
      for (var file in files) {
        if (file is File) {
          totalSize += file.lengthSync();
        }
      }
      
      if (totalSize <= maxSizeBytes) {
        debugPrint('AudioCacheService: Cache size ($totalSize bytes) is below limit ($maxSizeBytes bytes)');
        return;
      }
      
      // Sort files by last access time (oldest first)
      final fileStats = <MapEntry<File, DateTime>>[];
      for (var file in files) {
        if (file is File) {
          final stat = file.statSync();
          fileStats.add(MapEntry(file, stat.accessed));
        }
      }
      
      fileStats.sort((a, b) => a.value.compareTo(b.value));
      
      // Delete files until we're under the limit
      for (var entry in fileStats) {
        if (totalSize <= maxSizeBytes) break;
        
        final file = entry.key;
        final fileSize = file.lengthSync();
        await file.delete();
        totalSize -= fileSize;
        
        debugPrint('AudioCacheService: Pruned file ${file.path} (${fileSize} bytes)');
      }
      
      debugPrint('AudioCacheService: Cache pruned to $totalSize bytes');
    } catch (e) {
      debugPrint('AudioCacheService: Error pruning cache: $e');
    }
  }
} 