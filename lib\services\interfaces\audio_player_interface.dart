import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import '../../models/bhajan.dart';

/// Interface defining the core functionality of the audio player service
abstract class IAudioPlayerService {
  /// Current bhajan being played, may be null if no bhajan is active
  Bhajan? get currentBhajan;
  
  /// Whether the player is currently playing audio
  bool get isPlaying;
  
  /// The current player state stream
  Stream<PlayerState> get playerStateStream;
  
  /// Current position data stream (position, buffered position, duration)
  Stream<PositionData> get positionDataStream;
  
  /// Current loop mode
  LoopMode? get loopMode;
  
  /// Stream of currently playing bhajan
  Stream<Bhajan?> get currentBhajanStream;
  
  /// Initialize the audio service with a list of bhajans
  Future<void> init(List<Bhajan> bhajans, {bool emitCurrentBhajan = true});
  
  /// Play a specific bhajan at the given index
  Future<void> play(int index);
  
  /// Pause playback
  Future<void> pause();
  
  /// Resume playback
  Future<void> resume();
  
  /// Seek to a specific position
  Future<void> seek(Duration position);
  
  /// Play the next bhajan
  Future<void> playNext();
  
  /// Play the previous bhajan
  Future<void> playPrevious();
  
  /// Set the volume level (0.0 to 1.0)
  Future<void> setVolume(double volume);
  
  /// Toggle through repeat modes (none -> all -> one -> none)
  Future<void> toggleRepeatMode();
  
  /// Update the current list of bhajans
  Future<void> updateBhajans(List<Bhajan> newBhajans);
  
  /// Update the playlist with new bhajans but maintain playback state
  Future<void> updatePlaylist(List<Bhajan> newBhajans, {bool emitCurrentBhajan = true});
  
  /// Create a temporary playlist for playing a subset of bhajans
  Future<void> createTemporaryPlaylist(List<Bhajan> bhajans, int initialIndex);
  
  /// Reset the current bhajan reference
  void resetCurrentBhajan();
  
  /// Clean up resources
  Future<void> dispose();
}

/// Data class for position information
class PositionData {
  final Duration position;
  final Duration bufferedPosition;
  final Duration duration;

  PositionData(this.position, this.bufferedPosition, this.duration);
} 