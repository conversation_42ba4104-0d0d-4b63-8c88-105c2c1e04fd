import 'package:nepali_utils/nepali_utils.dart';

class BSDateUtils {
  static DateTime toBSDate(DateTime adDate) {
    try {
      // Convert AD (Gregorian) date to BS (Nepali) date using nepali_utils
      NepaliDateTime nepaliDate = NepaliDateTime.fromDateTime(adDate);

      // Return as regular DateTime for compatibility with the rest of the app
      return DateTime(
        nepaliDate.year,
        nepaliDate.month,
        nepaliDate.day,
      );
    } catch (e) {
      print('Error converting AD to BS date: $e');
      // Fallback to a reasonable range if conversion fails
      return DateTime(
        adDate.year + 56,
        adDate.month,
        adDate.day > 30 ? 30 : adDate.day,
      );
    }
  }

  static DateTime toADDate(DateTime bsDate) {
    try {
      // Create a NepaliDateTime from the BS date
      final nepaliDate = NepaliDateTime(
        bsDate.year,
        bsDate.month,
        bsDate.day > 32 ? 32 : bsDate.day,
      );

      // Convert to AD date
      final adDate = nepaliDate.toDateTime();
      return adDate;
    } catch (e) {
      print('Error converting BS to AD date: $e');
      // Fallback to a reasonable range if conversion fails
      return DateTime(
        bsDate.year - 56,
        bsDate.month,
        bsDate.day > 31 ? 31 : bsDate.day,
      );
    }
  }

  // Add methods to validate date inputs
  static bool isValidBSDate(int year, int month, int day) {
    try {
      // Check if this is a valid Nepali date
      NepaliDateTime(year, month, day);
      return true;
    } catch (e) {
      return false;
    }
  }

  static bool isValidADDate(int year, int month, int day) {
    try {
      // Check if this is a valid Gregorian date
      DateTime(year, month, day);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get days in a BS month
  static int getDaysInBSMonth(int year, int month) {
    if (month < 1 || month > 12) {
      print('Invalid month: $month. Using default value of 30 days.');
      return 30;
    }

    // Hardcoded days per month in BS calendar by year
    final Map<int, List<int>> daysInMonthBS = {
      // Common years pattern
      2075: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2018-2019
      2076: [31, 32, 31, 32, 31, 30, 30, 29, 30, 29, 30, 30], // 2019-2020
      2077: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31], // 2020-2021
      2078: [31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30], // 2021-2022
      2079: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2022-2023
      2080: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30], // 2023-2024
      2081: [31, 31, 32, 32, 31, 30, 30, 30, 29, 30, 30, 30], // 2024-2025
      2082: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30], // 2025-2026
      2083: [31, 31, 32, 31, 31, 30, 30, 30, 29, 30, 30, 30], // 2026-2027
      2084: [31, 31, 32, 31, 31, 30, 30, 30, 29, 30, 30, 30], // 2027-2028
      2085: [31, 32, 31, 32, 30, 31, 30, 30, 29, 30, 30, 30], // 2028-2029
      2086: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30], // 2029-2030
      2087: [31, 31, 32, 31, 31, 31, 30, 30, 29, 30, 30, 30], // 2030-2031
      2088: [30, 31, 32, 32, 30, 31, 30, 30, 29, 30, 30, 30], // 2031-2032
      2089: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30], // 2032-2033
      2090: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30], // 2033-2034
    };

    // Fallback pattern for years not in the map (approximate)
    final List<int> fallbackPattern = [
      31,
      31,
      32,
      31,
      31,
      31,
      30,
      29,
      30,
      29,
      30,
      30
    ];

    // First check if we have exact data for this year
    if (daysInMonthBS.containsKey(year)) {
      return daysInMonthBS[year]![month - 1];
    }

    // For years outside our data set, use the fallback pattern
    print('No data for BS year $year. Using fallback pattern.');
    return fallbackPattern[month - 1];
  }
}
