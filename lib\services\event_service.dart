import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HinduEvent {
  final String name;
  final DateTime date;

  HinduEvent({required this.name, required this.date});

  // Convert event to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'date': date.toIso8601String(),
    };
  }

  // Create event from JSON data
  factory HinduEvent.fromJson(Map<String, dynamic> json) {
    return HinduEvent(
      name: json['name'],
      date: DateTime.parse(json['date']),
    );
  }
}

enum EventCountry {
  india,
  nepal,
}

class EventService {
  static List<HinduEvent> _events = [];
  static bool _isInitialized = false;
  static bool _isOffline = false;
  static DateTime _lastSyncTime = DateTime(1970);
  static EventCountry _selectedCountry = EventCountry.india; // Default to India

  // Add a list of callback functions to notify listeners when country changes
  static final List<Function()> _countryChangeListeners = [];

  // Register a listener for country changes
  static void addCountryChangeListener(Function() listener) {
    _countryChangeListeners.add(listener);
  }

  // Remove a listener
  static void removeCountryChangeListener(Function() listener) {
    _countryChangeListeners.remove(listener);
  }

  // Notify all listeners of country change
  static void _notifyCountryChangeListeners() {
    for (var listener in _countryChangeListeners) {
      listener();
    }
  }

  // Keys for shared preferences
  static const String _eventsKey = 'hindupath_events';
  static const String _lastSyncKey = 'hindupath_events_last_sync';
  static const String _countryKey = 'hindupath_events_country';

  // Get the currently selected country
  static EventCountry get selectedCountry => _selectedCountry;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Try to load from shared preferences first
      final prefs = await SharedPreferences.getInstance();

      // Load selected country from preferences
      final countryStr = prefs.getString(_countryKey);
      if (countryStr != null) {
        _selectedCountry =
            countryStr == 'nepal' ? EventCountry.nepal : EventCountry.india;
      }

      await _loadFromSharedPreferences(prefs);

      // If events loaded from cache are not empty, use them
      if (_events.isNotEmpty) {
        // Check if we need to refresh from assets
        final syncTimeStr = prefs.getString(_lastSyncKey);
        if (syncTimeStr != null) {
          _lastSyncTime = DateTime.parse(syncTimeStr);
          // If data is recent enough (less than 24 hours old), don't refresh
          if (DateTime.now().difference(_lastSyncTime).inHours < 24) {
            _isInitialized = true;
            return;
          }
        }
      }

      // Try to load events from JSON file
      await _loadFromAssets();

      // Save to shared preferences for offline use
      await _saveToSharedPreferences(prefs);
    } catch (e) {
      print('Error during event service initialization: $e');

      // If we already have events from cache, keep using them
      if (_events.isEmpty) {
        _loadSampleEvents();

        // Save sample events to shared preferences
        try {
          final prefs = await SharedPreferences.getInstance();
          await _saveToSharedPreferences(prefs);
        } catch (e) {
          print('Error saving sample events to prefs: $e');
        }
      } else {
        _isOffline = true;
      }
    }

    // Sort events by date
    _events.sort((a, b) => a.date.compareTo(b.date));

    _isInitialized = true;
  }

  // Change the country and reload events
  static Future<void> setCountry(EventCountry country) async {
    if (_selectedCountry == country) return;

    print('Setting country to: ${country == EventCountry.india ? 'India' : 'Nepal'}');
    
    // Update the country selection
    _selectedCountry = country;
    _isInitialized = false;
    _events = []; // Clear the current events to avoid showing old data

    // Save the selected country preference
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _countryKey, country == EventCountry.nepal ? 'nepal' : 'india');

      // Reload events for the new country
      await initialize();
      
      print('Events loaded for ${country == EventCountry.india ? 'India' : 'Nepal'}: ${_events.length} events');
    } catch (e) {
      print('Error saving country preference: $e');
    }

    // Notify all listeners of country change
    _notifyCountryChangeListeners();
  }

  // Load events from shared preferences
  static Future<void> _loadFromSharedPreferences(
      SharedPreferences prefs) async {
    try {
      // Use a country-specific key to store events
      final String countryKey = _selectedCountry == EventCountry.india
          ? '${_eventsKey}_india'
          : '${_eventsKey}_nepal';

      final String? eventsJson = prefs.getString(countryKey);
      if (eventsJson != null) {
        final List<dynamic> jsonList = json.decode(eventsJson);
        _events = jsonList
            .map((eventJson) => HinduEvent.fromJson(eventJson))
            .toList();

        // Update events to current/next year if needed
        _updateEventYears();
      }
    } catch (e) {
      print('Error loading events from shared preferences: $e');
      _events = [];
    }
  }

  // Save events to shared preferences
  static Future<void> _saveToSharedPreferences(SharedPreferences prefs) async {
    try {
      final List<Map<String, dynamic>> jsonList =
          _events.map((event) => event.toJson()).toList();

      // Use a country-specific key to store events
      final String countryKey = _selectedCountry == EventCountry.india
          ? '${_eventsKey}_india'
          : '${_eventsKey}_nepal';

      await prefs.setString(countryKey, json.encode(jsonList));

      // Update last sync time
      _lastSyncTime = DateTime.now();
      await prefs.setString(_lastSyncKey, _lastSyncTime.toIso8601String());
    } catch (e) {
      print('Error saving events to shared preferences: $e');
    }
  }

  // Load events from asset file
  static Future<void> _loadFromAssets() async {
    try {
      // Choose the appropriate file based on selected country
      final String assetPath = _selectedCountry == EventCountry.india
          ? 'assets/hindu_events/events_india.json'
          : 'assets/hindu_events/events_nepal.json';

      final String jsonString = await rootBundle.loadString(assetPath);
      final List<dynamic> jsonList = json.decode(jsonString);

      _events = jsonList.map((json) {
        final String eventName = json['Events'] as String;
        final String dateStr = json['Date'] as String;

        // Parse the date - format is now "Month DD, YYYY" (e.g., "January 14, 2025")
        final DateTime eventDate = DateFormat('MMMM d, yyyy').parse(dateStr);

        // No need to adjust dates that are already in the future
        // This preserves the exact dates from the JSON file
        return HinduEvent(name: eventName, date: eventDate);
      }).toList();

      // Sort events by date
      _events.sort((a, b) => a.date.compareTo(b.date));
    } catch (e) {
      print('Error loading Hindu events from JSON: $e');
      // If there are events already loaded from cache, keep them
      if (_events.isEmpty) {
        _loadSampleEvents();
      } else {
        _isOffline = true;
      }
    }
  }

  static void _loadSampleEvents() {
    print('Loading sample Hindu festivals');
    final int currentYear = DateTime.now().year;

    // Sample important Hindu festivals - dates are approximate
    _events = [
      // Major Hindu festivals
      HinduEvent(name: 'Makar Sankranti', date: DateTime(currentYear, 1, 14)),
      HinduEvent(name: 'Vasant Panchami', date: DateTime(currentYear, 2, 5)),
      HinduEvent(name: 'Maha Shivaratri', date: DateTime(currentYear, 3, 1)),
      HinduEvent(name: 'Holi', date: DateTime(currentYear, 3, 18)),
      HinduEvent(name: 'Ram Navami', date: DateTime(currentYear, 4, 10)),
      HinduEvent(name: 'Hanuman Jayanti', date: DateTime(currentYear, 4, 16)),
      HinduEvent(name: 'Akshaya Tritiya', date: DateTime(currentYear, 5, 2)),
      HinduEvent(name: 'Buddha Purnima', date: DateTime(currentYear, 5, 15)),
      HinduEvent(name: 'Ganga Dussehra', date: DateTime(currentYear, 6, 8)),
      HinduEvent(
          name: 'Jagannath Rath Yatra', date: DateTime(currentYear, 7, 1)),
      HinduEvent(name: 'Guru Purnima', date: DateTime(currentYear, 7, 13)),
      HinduEvent(name: 'Nag Panchami', date: DateTime(currentYear, 7, 25)),
      HinduEvent(name: 'Raksha Bandhan', date: DateTime(currentYear, 8, 12)),
      HinduEvent(
          name: 'Krishna Janmashtami', date: DateTime(currentYear, 8, 19)),
      HinduEvent(name: 'Ganesh Chaturthi', date: DateTime(currentYear, 9, 2)),
      HinduEvent(name: 'Onam', date: DateTime(currentYear, 9, 10)),
      HinduEvent(name: 'Navaratri', date: DateTime(currentYear, 10, 3)),
      HinduEvent(name: 'Dussehra', date: DateTime(currentYear, 10, 12)),
      HinduEvent(name: 'Karwa Chauth', date: DateTime(currentYear, 10, 20)),
      HinduEvent(name: 'Dhanteras', date: DateTime(currentYear, 10, 29)),
      HinduEvent(name: 'Diwali', date: DateTime(currentYear, 11, 1)),
      HinduEvent(name: 'Govardhan Puja', date: DateTime(currentYear, 11, 3)),
      HinduEvent(name: 'Bhai Dooj', date: DateTime(currentYear, 11, 5)),
      HinduEvent(name: 'Tulsi Vivah', date: DateTime(currentYear, 11, 15)),
      HinduEvent(name: 'Gita Jayanti', date: DateTime(currentYear, 12, 15)),
    ];

    _updateEventYears();
  }

  // Update events to current/next year
  static void _updateEventYears() {
    // Adjust dates that have already passed this year
    final now = DateTime.now();
    final int currentYear = now.year;

    _events = _events.map((event) {
      // If the stored event is from a previous year or
      // if the event has already passed this year, update it
      if (event.date.year < currentYear ||
          (event.date.year == currentYear && event.date.isBefore(now))) {
        // Keep the month and day but update the year
        return HinduEvent(
          name: event.name,
          date: DateTime(
              event.date.year < currentYear ? currentYear : currentYear + 1,
              event.date.month,
              event.date.day),
        );
      }
      return event;
    }).toList();
  }

  static HinduEvent? getNextUpcomingEvent() {
    if (_events.isEmpty) return null;

    final now = DateTime.now();

    // Make sure events are sorted by date
    _events.sort((a, b) => a.date.compareTo(b.date));

    // For debugging: print the current date and next few events
    print("Current date: ${DateFormat('yyyy-MM-dd').format(now)}");
    print("Next 5 events in chronological order:");
    final nextFew = _events.take(5).toList();
    for (var i = 0; i < nextFew.length; i++) {
      print(
          "${i + 1}. ${nextFew[i].name} on ${DateFormat('yyyy-MM-dd').format(nextFew[i].date)}");
    }

    // Find the next upcoming event by comparing only date components (ignoring time)
    for (final event in _events) {
      // Normalize dates by setting time to midnight (00:00:00)
      final eventDateOnly =
          DateTime(event.date.year, event.date.month, event.date.day);
      final nowDateOnly = DateTime(now.year, now.month, now.day);

      // If the event is today or in the future, return it
      if (eventDateOnly.isAtSameMomentAs(nowDateOnly) ||
          eventDateOnly.isAfter(nowDateOnly)) {
        print(
            "Selected next event: ${event.name} on ${DateFormat('yyyy-MM-dd').format(event.date)}");
        return event;
      }
    }

    // If all events have passed, return the first event of next year (first in the sorted list)
    if (_events.isNotEmpty) {
      final firstEvent = _events.first;
      print(
          "All events passed, returning first event: ${firstEvent.name} on ${DateFormat('yyyy-MM-dd').format(firstEvent.date)}");
      return firstEvent;
    }

    return null;
  }

  /// Get all Hindu events
  static List<HinduEvent> getAllEvents() {
    return List<HinduEvent>.from(_events);
  }

  /// Get event for a specific date if it exists
  static HinduEvent? getEventForDate(DateTime date) {
    // Normalize the date to remove time components
    final targetDate = DateTime(date.year, date.month, date.day);

    // Search for an event that matches this date
    for (final event in _events) {
      final eventDate =
          DateTime(event.date.year, event.date.month, event.date.day);
      if (eventDate.isAtSameMomentAs(targetDate)) {
        return event;
      }
    }

    return null;
  }

  /// Get all events for a specific date
  static List<HinduEvent> getAllEventsForDate(DateTime date) {
    // Normalize the date to remove time components
    final targetDate = DateTime(date.year, date.month, date.day);

    // Find all events that match this date
    return _events.where((event) {
      final eventDate =
          DateTime(event.date.year, event.date.month, event.date.day);
      return eventDate.isAtSameMomentAs(targetDate);
    }).toList();
  }

  /// Get all events for a specific month
  static List<HinduEvent> getEventsForMonth(int year, int month) {
    // Filter events for the specified month and year
    return _events.where((event) {
      return event.date.year == year && event.date.month == month;
    }).toList();
  }

  static String formatEventDate(DateTime date) {
    return DateFormat('MMM d, yyyy').format(date);
  }

  /// Indicates if we're operating in offline mode
  static bool isOffline() {
    return _isOffline;
  }

  /// Force a refresh of the event data
  static Future<bool> refreshEvents() async {
    try {
      // Load from assets
      await _loadFromAssets();

      // Update last sync time and save to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await _saveToSharedPreferences(prefs);

      _isOffline = false;
      return true;
    } catch (e) {
      print('Error refreshing events: $e');
      return false;
    }
  }

  /// Get events for a specific date range (inclusive)
  static List<HinduEvent> getEventsForDateRange(
      DateTime startDate, DateTime endDate) {
    // Normalize the dates to remove time components
    final normalizedStartDate =
        DateTime(startDate.year, startDate.month, startDate.day);
    final normalizedEndDate =
        DateTime(endDate.year, endDate.month, endDate.day);

    // Debug logging
    print(
        '🔍 Filtering events from ${DateFormat('yyyy-MM-dd').format(normalizedStartDate)} to ${DateFormat('yyyy-MM-dd').format(normalizedEndDate)}');

    // Ensure events are sorted by date
    _events.sort((a, b) => a.date.compareTo(b.date));

    // Filter events that fall STRICTLY within the given date range (inclusive)
    final rangeEvents = _events.where((event) {
      final eventDate =
          DateTime(event.date.year, event.date.month, event.date.day);

      // Check if the event date is within range: startDate <= eventDate <= endDate
      final bool isAfterOrEqualToStart =
          eventDate.isAtSameMomentAs(normalizedStartDate) ||
              eventDate.isAfter(normalizedStartDate);
      final bool isBeforeOrEqualToEnd =
          eventDate.isAtSameMomentAs(normalizedEndDate) ||
              eventDate.isBefore(normalizedEndDate);
      final isInRange = isAfterOrEqualToStart && isBeforeOrEqualToEnd;

      // Log each event evaluation for debugging
      print('Event: ${event.name} on ${DateFormat('yyyy-MM-dd').format(eventDate)} - ' +
          'In range: $isInRange (after start: $isAfterOrEqualToStart, before end: $isBeforeOrEqualToEnd)');

      return isInRange;
    }).toList();

    // Log diagnostic info
    print('Total events in range: ${rangeEvents.length}');
    if (rangeEvents.isNotEmpty) {
      print(
          'First event in range: ${rangeEvents.first.name} on ${DateFormat('yyyy-MM-dd').format(rangeEvents.first.date)}');
      print(
          'Last event in range: ${rangeEvents.last.name} on ${DateFormat('yyyy-MM-dd').format(rangeEvents.last.date)}');
    }

    // Sort filtered events by date
    rangeEvents.sort((a, b) => a.date.compareTo(b.date));

    return rangeEvents;
  }

  /// For debugging: Dump all events data to console
  static void dumpEventsData() {
    print('====== EVENTS DATA DUMP ======');
    print('Total events: ${_events.length}');
    print(
        'Country: ${_selectedCountry == EventCountry.india ? 'India' : 'Nepal'}');

    // Group events by month for easier reading
    final Map<int, List<HinduEvent>> eventsByMonth = {};
    for (var event in _events) {
      final month = event.date.month;
      if (!eventsByMonth.containsKey(month)) {
        eventsByMonth[month] = [];
      }
      eventsByMonth[month]!.add(event);
    }

    // Print events by month
    for (var month = 1; month <= 12; month++) {
      final events = eventsByMonth[month] ?? [];
      if (events.isNotEmpty) {
        print(
            '==== MONTH ${DateFormat('MMMM').format(DateTime(2025, month, 1))} ====');
        for (var event in events) {
          print(
              '${DateFormat('yyyy-MM-dd').format(event.date)}: ${event.name}');
        }
      }
    }

    print('====== END OF DUMP ======');
  }

  /// Get all upcoming events starting from today
  static List<HinduEvent> getAllUpcomingEvents() {
    final now = DateTime.now();
    final todayDateOnly = DateTime(now.year, now.month, now.day);

    // Find all events from today onwards, sorted by date
    final upcomingEvents = _events.where((event) {
      final eventDateOnly =
          DateTime(event.date.year, event.date.month, event.date.day);
      return eventDateOnly.isAtSameMomentAs(todayDateOnly) ||
          eventDateOnly.isAfter(todayDateOnly);
    }).toList();

    // Sort by date
    upcomingEvents.sort((a, b) => a.date.compareTo(b.date));

    return upcomingEvents;
  }
}
