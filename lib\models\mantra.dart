import 'package:flutter/material.dart';

class Mantra {
  final String title;
  final String sanskrit;
  final String english;
  final dynamic icon; // Can be either IconData or String (asset path)

  Mantra({
    required this.title,
    required this.sanskrit,
    required this.english,
    required this.icon,
  });

  // Helper method to get icon based on category
  static dynamic getIconForCategory(String category) {
    // Use the same book icon (auto_stories) for all deity mantras
    return Icons.auto_stories;

    // Previous implementation with different icons for each deity
    /* 
    switch (category.toLowerCase()) {
      case 'shri ganesha mantras':
        return Icons.auto_stories;
      case 'shri krishna mantras':
        return Icons.music_note;
      case 'shri rama mantras':
        return Icons.arrow_forward;
      case 'shiva mantras':
        return Icons.water_drop;
      case 'shri vishnu mantras':
        return Icons.self_improvement;
      case 'shri hanuman mantras':
        return Icons.fitness_center;
      case 'mahalakshmi mantras':
        return Icons.workspace_premium;
      case 'saraswati mantras':
        return Icons.school;
      case 'kubera mantras':
        return Icons.account_balance;
      case 'shri gayatri mantra':
        return Icons.light_mode;
      case 'shri durga mantras':
        return Icons.bolt;
      case 'narasimha gayatri mantra':
        return Icons.pets;
      case 'agni gayatri mantra':
        return Icons.local_fire_department;
      case 'indra gayatri mantra':
        return Icons.flash_on;
      default:
        return Icons.auto_stories;
    }
    */
  }
}
