import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';

class ThemeProvider extends ChangeNotifier {
  // Default theme colors
  static const Color defaultPrimaryColor = Color(0xFFEF4444);
  static const Color defaultBackgroundColor = Color(0xFF0D0D0D);

  // Current theme colors
  Color _primaryColor = defaultPrimaryColor;
  Color _backgroundColor = defaultBackgroundColor;

  // Keys for shared preferences
  static const String _primaryColorKey = 'primary_color';
  static const String _backgroundColorKey = 'background_color';

  // Getters for current colors
  Color get primaryColor => _primaryColor;
  Color get backgroundColor => _backgroundColor;

  // Static instance for accessing from anywhere
  static ThemeProvider? _instance;

  // Constructor loads saved theme
  ThemeProvider() {
    _instance = this;
    _loadTheme();
  }

  // Get the current primary color or fallback to default color
  static Color getGreenColor(BuildContext context) {
    try {
      return Provider.of<ThemeProvider>(context, listen: true).primaryColor;
    } catch (e) {
      // If provider is not available, use the default
      return defaultPrimaryColor;
    }
  }

  // Static method to get the current theme color without context
  static Color get currentGreen {
    return _instance?._primaryColor ?? defaultPrimaryColor;
  }

  // Load theme from shared preferences
  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load primary color if available
      final primaryColorValue = prefs.getInt(_primaryColorKey);
      if (primaryColorValue != null) {
        _primaryColor = Color(primaryColorValue);
      }

      // Load background color if available
      final backgroundColorValue = prefs.getInt(_backgroundColorKey);
      if (backgroundColorValue != null) {
        _backgroundColor = Color(backgroundColorValue);
      }

      // Notify listeners that the theme has been loaded
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme: $e');
    }
  }

  // Update and save the theme
  Future<void> updateTheme({required Color primaryColor}) async {
    // Update the colors
    _primaryColor = primaryColor;

    // Notify listeners
    notifyListeners();

    // Save to shared preferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_primaryColorKey, _primaryColor.value);
    } catch (e) {
      debugPrint('Error saving theme: $e');
    }
  }

  // Reset theme to defaults
  Future<void> resetTheme() async {
    _primaryColor = defaultPrimaryColor;
    _backgroundColor = defaultBackgroundColor;

    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_primaryColorKey);
      await prefs.remove(_backgroundColorKey);
    } catch (e) {
      debugPrint('Error resetting theme: $e');
    }
  }

  // Generate ThemeData based on current colors
  ThemeData getTheme() {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: _primaryColor,
        brightness: Brightness.dark,
        background: _backgroundColor,
        surface: _backgroundColor,
        secondary: _primaryColor,
        onBackground: Colors.white,
        onSurface: Colors.white,
        onPrimary: Colors.white,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: _backgroundColor,
        foregroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.transparent,
        centerTitle: true,
      ),
      scaffoldBackgroundColor: _backgroundColor,
      textTheme: const TextTheme(
        headlineLarge: TextStyle(fontFamily: 'Prakrta'),
        headlineMedium: TextStyle(fontFamily: 'Prakrta'),
        headlineSmall: TextStyle(fontFamily: 'Prakrta'),
        titleLarge: TextStyle(fontFamily: 'Prakrta', color: Colors.white),
        titleMedium: TextStyle(fontFamily: 'Prakrta', color: Colors.white),
        titleSmall: TextStyle(fontFamily: 'Prakrta', color: Colors.white),
        bodyLarge: TextStyle(color: Colors.white),
        bodyMedium: TextStyle(color: Colors.white),
        bodySmall: TextStyle(color: Colors.white),
      ),
      useMaterial3: true,
    );
  }
}
