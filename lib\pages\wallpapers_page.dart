import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../providers/wallpaper_provider.dart';
import '../models/wallpaper.dart';
import '../models/bhajan.dart';
import '../screens/player_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../screens/wallpaper_viewer_screen.dart';
import '../utils/color_utils.dart';
import '../utils/theme_widgets.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class WallpapersPage extends StatefulWidget {
  const WallpapersPage({super.key});

  @override
  State<WallpapersPage> createState() => _WallpapersPageState();
}

class _WallpapersPageState extends State<WallpapersPage>
    with SingleTickerProviderStateMixin {
  String? _selectedCategory;
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Set Lord Krishna as default category
    _selectedCategory = 'Lord Krishna';
    // Load wallpapers when the page is initialized
    _loadWallpapers();

    // Also load the default category wallpapers explicitly
    Future.delayed(Duration.zero, () {
      context
          .read<WallpaperProvider>()
          .loadWallpapersByCategory(_selectedCategory!);
    });

    // Add tab change listener
    _tabController.addListener(_onTabChanged);
    
    // Add scroll listener for pagination
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _tabController.removeListener(_onTabChanged);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.index == 0) {
      // Newly Added tab is selected, refresh wallpapers
      _refreshWallpapers();
    }
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 500) {
      // When we're near the bottom, load more wallpapers
      final wallpaperProvider = context.read<WallpaperProvider>();
      if (!wallpaperProvider.isLoading && wallpaperProvider.hasMoreWallpapers) {
        wallpaperProvider.loadMoreWallpapers();
      }
    }
  }

  Future<void> _loadWallpapers() async {
    final wallpaperProvider = context.read<WallpaperProvider>();
    if (wallpaperProvider.wallpapers.isEmpty) {
      await wallpaperProvider.loadWallpapers();
    }
  }

  Future<void> _refreshWallpapers() async {
    final wallpaperProvider = context.read<WallpaperProvider>();
    await wallpaperProvider.refreshWallpapers();
  }

  // List of Hindu deities for category buttons
  final List<String> _deities = [
    'Lord Krishna',
    'Lord Shiva',
    'Lord Hanuman',
    'Lord Ram',
    'Lord Ganesha',
    'Lord Vishnu',
    'Shri Durga',
    'Shri Lakshmi',
    'Shri Saraswati',
  ];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        body: Column(
          children: [
            TabBar(
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 14,
              ),
              indicator: ThemedTabIndicator(
                context: context,
                radius: 4,
                indicatorHeight: 3,
                insets: const EdgeInsets.symmetric(horizontal: 20),
              ),
              tabs: const [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Newly Added'),
                      SizedBox(width: 8),
                      Icon(Icons.new_releases, size: 20),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Categories'),
                      SizedBox(width: 8),
                      Icon(Icons.category, size: 20),
                    ],
                  ),
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildNewlyAddedTab(),
                  _buildCategoriesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewlyAddedTab() {
    return Consumer<WallpaperProvider>(
      builder: (context, wallpaperProvider, child) {
        if (wallpaperProvider.isLoading &&
            wallpaperProvider.wallpapers.isEmpty) {
          return Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: AppColors.getPrimaryColor(context),
            ),
          );
        }

        if (wallpaperProvider.error != null &&
            wallpaperProvider.wallpapers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  wallpaperProvider.error!,
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadWallpapers,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final newWallpapers = wallpaperProvider.getNewlyAddedWallpapers();

        if (newWallpapers.isEmpty) {
          return const Center(
            child: Text(
              'No new wallpapers available',
              style: TextStyle(color: Colors.white70),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshWallpapers,
          color: AppColors.getPrimaryColor(context),
          backgroundColor: const Color(0xFF222222),
          child: _buildWallpaperGrid(newWallpapers),
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Deity category buttons in a scrollable row
        Container(
          height: 50,
          margin: const EdgeInsets.only(top: 16, bottom: 16),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: _deities.length,
            itemBuilder: (context, index) {
              final deity = _deities[index];
              final isSelected = deity == _selectedCategory;

              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: _buildCategoryButton(
                  deity,
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      _selectedCategory = deity;
                    });
                    // Load wallpapers for this specific category
                    context
                        .read<WallpaperProvider>()
                        .loadWallpapersByCategory(deity);
                  },
                ),
              );
            },
          ),
        ),

        // Wallpapers grid
        Expanded(
          child: Consumer<WallpaperProvider>(
            builder: (context, wallpaperProvider, child) {
              if (wallpaperProvider.isLoading &&
                  wallpaperProvider.wallpapers.isEmpty) {
                return Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppColors.getPrimaryColor(context),
                  ),
                );
              }

              if (wallpaperProvider.error != null &&
                  wallpaperProvider.wallpapers.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      Text(
                        wallpaperProvider.error!,
                        style: const TextStyle(color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadWallpapers,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              final categoryWallpapers =
                  wallpaperProvider.getWallpapersByCategory(_selectedCategory!);

              if (categoryWallpapers.isEmpty) {
                return Center(
                  child: Text(
                    'No wallpapers available for $_selectedCategory',
                    style: const TextStyle(color: Colors.white70),
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: _refreshWallpapers,
                color: AppColors.getPrimaryColor(context),
                backgroundColor: const Color(0xFF222222),
                child: _buildWallpaperGrid(categoryWallpapers),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildWallpaperGrid(List<Wallpaper> wallpapers) {
    return MasonryGridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: wallpapers.length,
      gridDelegate: const SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
      ),
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      itemBuilder: (context, index) {
        final wallpaper = wallpapers[index];
        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WallpaperViewerScreen(
                  wallpapers: wallpapers,
                  initialIndex: index,
                ),
              ),
            );
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: const Color(0xFF222222),
            ),
            clipBehavior: Clip.antiAlias,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Wallpaper image
                Hero(
                  tag: 'wallpaper-${wallpaper.id}',
                  child: AspectRatio(
                    aspectRatio: 0.7 + (index % 5) * 0.05,
                    child: CachedNetworkImage(
                      imageUrl: wallpaper.effectiveThumbnailUrl,
                      fit: BoxFit.cover,
                      httpHeaders: const {
                        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                        'User-Agent':
                            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36'
                      },
                      placeholder: (context, url) => Container(
                        color: const Color(0xFF333333),
                        child: Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppColors.getPrimaryColor(context),
                            ),
                          ),
                        ),
                      ),
                      memCacheWidth: 300,
                      fadeInDuration: const Duration(milliseconds: 300),
                      errorWidget: (context, url, error) => Container(
                        color: const Color(0xFF333333),
                        child: const Center(
                          child: Icon(
                            Icons.error_outline,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryButton(
    String category, {
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(30),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.getPrimaryColor(context)
                  : const Color(0xFF222222),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: isSelected
                    ? Colors.white.withOpacity(0.5)
                    : Colors.transparent,
                width: 1,
              ),
              boxShadow: [
                if (isSelected)
                  BoxShadow(
                    color: AppColors.getPrimaryColorWithOpacity(context, 0.5),
                    blurRadius: 8,
                    spreadRadius: 1,
                    offset: const Offset(0, 2),
                  ),
              ],
            ),
            child: Text(
              category,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w400,
                fontFamily: 'JosefinSans',
              ),
            ),
          ),
        ),
      ),
    );
  }
}
