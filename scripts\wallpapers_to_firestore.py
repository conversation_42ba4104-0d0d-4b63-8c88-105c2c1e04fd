#!/usr/bin/env python3
"""
Hindupath Wallpaper Processor - Incremental Version

This script processes wallpapers from Cloudflare R2 storage:
1. Efficiently skips already processed wallpapers
2. Only processes new wallpapers added to R2
3. Generates thumbnails and updates Firestore
4. Ensures category names match app requirements

Author: Studio Habre
Date: 2024
"""

import boto3
import firebase_admin
from firebase_admin import credentials, firestore
import os
import io
import time
import urllib.parse
from datetime import datetime
from PIL import Image
import argparse

# Configuration
CONFIG = {
    # R2 Storage Configuration
    "r2": {
        "endpoint_url": "https://3a0098f2f5a153bb2a3de2fba20d674d.r2.cloudflarestorage.com",
        "aws_access_key_id": "70664ebbc81354bce2e32098189e3578",
        "aws_secret_access_key": "d52c62e094eb0e728d083d36909e634aa6c093708bdbe54aaa8e8821ad76f004",
        "bucket_name": "hindupath",
        "region_name": "auto"
    },
    
    # Firebase Configuration 
    "firebase": {
        "service_account_path": r"C:\pyflutter\projects\hindupath\scripts\hindupath-studiohabre-firebase-adminsdk.json"
    },
    
    # CDN Configuration
    "cdn": {
        "base_url": "https://cdn.hindupath.online"
    },
    
    # Thumbnail Configuration
    "thumbnails": {
        "size": (400, 600),  # Width x Height
        "quality": 85,       # JPEG quality (0-100)
        "folder": "thumbnails"
    },
    
    # Category mapping (folder name → proper display name)
    "categories": {
        "lord ganesha": "Lord Ganesha",
        "lord hanuman": "Lord Hanuman", 
        "lord krishna": "Lord Krishna",
        "lord ram": "Lord Ram",
        "lord shiva": "Lord Shiva",
        "lord vishnu": "Lord Vishnu",
        "shri durga": "Shri Durga",
        "shri lakshmi": "Shri Lakshmi",
        "shri saraswati": "Shri Saraswati"
    }
}

class HindupathWallpaperProcessor:
    """Main processor class for handling wallpapers"""
    
    def __init__(self):
        """Initialize connections and clients"""
        self.init_r2_client()
        self.init_firebase()
        self.processed_keys = set()  # Track processed object keys
        
    def init_r2_client(self):
        """Initialize Cloudflare R2 client"""
        print("🔄 Initializing R2 connection...")
        try:
            self.s3_client = boto3.client(
                's3',
                endpoint_url=CONFIG["r2"]["endpoint_url"],
                aws_access_key_id=CONFIG["r2"]["aws_access_key_id"],
                aws_secret_access_key=CONFIG["r2"]["aws_secret_access_key"],
                region_name=CONFIG["r2"]["region_name"]
            )
            print("✅ R2 connection established")
        except Exception as e:
            print(f"❌ Failed to initialize R2 client: {str(e)}")
            exit(1)
            
    def init_firebase(self):
        """Initialize Firebase connection"""
        print("🔄 Initializing Firebase connection...")
        try:
            # Check if service account file exists
            service_account_path = CONFIG["firebase"]["service_account_path"]
            if not os.path.exists(service_account_path):
                print(f"❌ Service account file not found: {service_account_path}")
                exit(1)
                
            # Initialize Firebase
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred)
            self.db = firestore.client()
            print("✅ Firebase connection established")
        except Exception as e:
            print(f"❌ Failed to initialize Firebase: {str(e)}")
            exit(1)
    
    def load_existing_wallpapers(self):
        """Load all existing wallpapers from Firestore to avoid re-processing"""
        print("🔄 Loading existing wallpapers from Firestore...")
        try:
            # Get all wallpapers
            wallpapers = self.db.collection('wallpapers').get()
            
            # Store r2ObjectKey values in set for quick lookup
            for doc in wallpapers:
                wallpaper = doc.to_dict()
                r2_path = wallpaper.get('r2ObjectKey', '')
                if r2_path:
                    # Add both with and without the wallpapers/ prefix
                    self.processed_keys.add(r2_path)
                    self.processed_keys.add(f"wallpapers/{r2_path}")
                    
            print(f"✅ Loaded {len(self.processed_keys)} existing wallpapers")
        except Exception as e:
            print(f"❌ Error loading existing wallpapers: {str(e)}")
            
    def get_wallpaper_files(self):
        """Get list of wallpaper files from R2 storage"""
        print("🔄 Fetching wallpaper list from R2...")
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=CONFIG["r2"]["bucket_name"]
            )
            
            if 'Contents' not in response:
                print("⚠️ No objects found in bucket")
                return []
                
            # Filter for image files in wallpapers folder, exclude thumbnails
            wallpaper_files = [
                obj for obj in response['Contents'] 
                if self.is_wallpaper_file(obj['Key'])
            ]
            
            print(f"✅ Found {len(wallpaper_files)} total wallpaper files")
            return wallpaper_files
            
        except Exception as e:
            print(f"❌ Error fetching wallpaper list: {str(e)}")
            return []
            
    def is_wallpaper_file(self, key):
        """Check if file is a wallpaper image"""
        # Valid image extensions
        image_extensions = ('.jpg', '.jpeg', '.png', '.webp')
        
        # Must be in wallpapers folder but not in thumbnails subfolder
        is_in_wallpapers = key.startswith('wallpapers/')
        is_not_thumbnail = CONFIG["thumbnails"]["folder"] not in key
        is_image = any(key.lower().endswith(ext) for ext in image_extensions)
        
        return is_in_wallpapers and is_not_thumbnail and is_image
            
    def normalize_category(self, folder_name):
        """Get standardized category name from folder name"""
        # Remove "wallpapers/" prefix if present
        if folder_name.startswith("wallpapers/"):
            folder_name = folder_name[len("wallpapers/"):]
            
        # Extract just the folder name (first segment)
        if "/" in folder_name:
            folder_name = folder_name.split("/")[0]
            
        # Look up in mapping
        return CONFIG["categories"].get(folder_name, folder_name.replace('_', ' ').title())
        
    def get_category_from_path(self, key):
        """Extract category from object key"""
        parts = key.split('/')
        if len(parts) >= 2:
            folder = parts[1]
            return self.normalize_category(folder)
        return "Other"
        
    def format_file_size(self, size_in_bytes):
        """Convert file size to human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_in_bytes < 1024:
                return f"{size_in_bytes:.2f} {unit}"
            size_in_bytes /= 1024
        return f"{size_in_bytes:.2f} GB"
            
    def generate_thumbnail(self, object_key):
        """Generate thumbnail for a given wallpaper"""
        print(f"🔄 Generating thumbnail for {object_key}")
        start_time = time.time()
        
        try:
            # Download original image
            response = self.s3_client.get_object(
                Bucket=CONFIG["r2"]["bucket_name"],
                Key=object_key
            )
            image_data = response['Body'].read()
            
            # Create thumbnail
            with Image.open(io.BytesIO(image_data)) as img:
                # Convert to RGB if needed (for PNGs with transparency)
                if img.mode in ('RGBA', 'LA'):
                    bg = Image.new('RGB', img.size, (255, 255, 255))
                    bg.paste(img, mask=img.split()[3])
                    img = bg
                    
                # Resize while maintaining aspect ratio
                img.thumbnail(CONFIG["thumbnails"]["size"])
                
                # Save as optimized JPEG
                buffer = io.BytesIO()
                img.save(buffer, 
                    format='JPEG', 
                    quality=CONFIG["thumbnails"]["quality"], 
                    optimize=True
                )
                buffer.seek(0)
            
            # Generate thumbnail path
            path_parts = object_key.split('/')
            if len(path_parts) >= 3:  # wallpapers/category/filename
                category = path_parts[1]
                filename = path_parts[2]
                filename_base = os.path.splitext(filename)[0]
                thumbnail_key = f"wallpapers/{CONFIG['thumbnails']['folder']}/{category}/{filename_base}.jpg"
            else:
                # Fallback
                filename_base = os.path.splitext(os.path.basename(object_key))[0]
                thumbnail_key = f"wallpapers/{CONFIG['thumbnails']['folder']}/{filename_base}.jpg"
            
            # Upload thumbnail to R2
            self.s3_client.put_object(
                Bucket=CONFIG["r2"]["bucket_name"],
                Key=thumbnail_key,
                Body=buffer,
                ContentType='image/jpeg'
            )
            
            # Generate CDN URL
            thumbnail_path = thumbnail_key.replace('wallpapers/', '', 1)
            thumbnail_url = f"{CONFIG['cdn']['base_url']}/wallpapers/{urllib.parse.quote(thumbnail_path)}"
            
            elapsed_time = time.time() - start_time
            print(f"✅ Created thumbnail in {elapsed_time:.2f}s: {thumbnail_url}")
            
            return thumbnail_url
            
        except Exception as e:
            print(f"❌ Error generating thumbnail: {str(e)}")
            return None
            
    def process_wallpaper(self, wallpaper_obj):
        """Process a single wallpaper object"""
        object_key = wallpaper_obj['Key']
        
        # Skip if already processed
        if object_key in self.processed_keys:
            print(f"⏩ Skipping already processed: {object_key}")
            return True
            
        print(f"\n🔄 Processing new wallpaper: {object_key}")
        
        try:
            # Get object metadata
            metadata = self.s3_client.head_object(
                Bucket=CONFIG["r2"]["bucket_name"],
                Key=object_key
            )
            
            # Extract filename and category
            filename = os.path.basename(object_key)
            category = self.get_category_from_path(object_key)
            
            # Create CDN URL for original image
            object_path = object_key.replace('wallpapers/', '', 1)
            cdn_url = f"{CONFIG['cdn']['base_url']}/wallpapers/{urllib.parse.quote(object_path)}"
            
            # Generate thumbnail
            thumbnail_url = self.generate_thumbnail(object_key)
            
            # Prepare wallpaper data
            wallpaper_data = {
                'filename': filename,
                'category': category,
                'url': cdn_url,
                'thumbnailUrl': thumbnail_url,
                'size': self.format_file_size(wallpaper_obj['Size']),
                'uploadDate': metadata['LastModified'],
                'r2ObjectKey': object_path
            }
            
            # Add new document to Firestore
            doc_ref = self.db.collection('wallpapers').document()
            doc_ref.set(wallpaper_data)
            print(f"✅ Created Firestore document for {filename}")
            
            # Add to processed keys to avoid reprocessing
            self.processed_keys.add(object_key)
                
            return True
            
        except Exception as e:
            print(f"❌ Error processing wallpaper: {str(e)}")
            return False

    def fix_missing_thumbnails(self):
        """Update existing wallpapers with missing thumbnails"""
        print("\n🔄 Checking for wallpapers with missing thumbnails...")
        
        try:
            # Get all wallpapers without thumbnails
            query = self.db.collection('wallpapers').where('thumbnailUrl', '==', None)
            wallpapers = query.get()
            
            if not wallpapers:
                print("✅ No wallpapers need thumbnails")
                return
                
            count = len(wallpapers)
            print(f"🔍 Found {count} wallpapers missing thumbnails")
            
            for i, doc in enumerate(wallpapers, 1):
                try:
                    wallpaper = doc.to_dict()
                    object_key = f"wallpapers/{wallpaper['r2ObjectKey']}"
                    print(f"\n🔄 Processing ({i}/{count}): {object_key}")
                    
                    # Generate thumbnail
                    thumbnail_url = self.generate_thumbnail(object_key)
                    
                    if thumbnail_url:
                        # Update Firestore
                        doc.reference.update({
                            'thumbnailUrl': thumbnail_url
                        })
                        print(f"✅ Updated document with thumbnail")
                    
                except Exception as e:
                    print(f"❌ Error updating wallpaper {doc.id}: {str(e)}")
                    continue
                    
        except Exception as e:
            print(f"❌ Error updating wallpapers: {str(e)}")
            
    def process_new_wallpapers(self):
        """Process only new wallpapers not yet in Firestore"""
        print("\n🚀 Starting incremental wallpaper processing...")
        
        # First load existing wallpapers to avoid reprocessing
        self.load_existing_wallpapers()
        
        # Get all wallpaper files from R2
        wallpaper_files = self.get_wallpaper_files()
        
        if not wallpaper_files:
            print("⚠️ No wallpapers found in R2")
            return
        
        # Count new vs existing wallpapers
        new_wallpapers = [
            wp for wp in wallpaper_files 
            if wp['Key'] not in self.processed_keys
        ]
        
        if not new_wallpapers:
            print("✅ No new wallpapers to process")
            return
            
        # Process each new wallpaper
        total = len(new_wallpapers)
        successful = 0
        
        print(f"🔍 Found {total} new wallpapers to process")
        
        for i, wallpaper in enumerate(new_wallpapers, 1):
            print(f"\n📷 Processing new wallpaper {i}/{total}")
            if self.process_wallpaper(wallpaper):
                successful += 1
                
        print(f"\n✅ Successfully processed {successful} of {total} new wallpapers")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Hindupath Wallpaper Processor - Incremental Version")
    parser.add_argument('--fix-thumbnails', action='store_true', 
                       help='Fix missing thumbnails for existing wallpapers')
    parser.add_argument('--add-new', action='store_true',
                       help='Process only new wallpapers (default if no flags provided)')
    
    args = parser.parse_args()
    
    # Create processor
    processor = HindupathWallpaperProcessor()
    
    # Determine operation mode
    if args.fix_thumbnails:
        processor.fix_missing_thumbnails()
    elif args.add_new or not args.fix_thumbnails:
        processor.process_new_wallpapers()
        
    print("\n✨ Processing complete!")
    
if __name__ == "__main__":
    # Check for PIL
    try:
        import PIL
        print(f"✅ Pillow version {PIL.__version__} is installed")
    except ImportError:
        print("❌ Pillow (PIL) is not installed. Installing...")
        os.system("pip install pillow")
        print("✅ Pillow installed")
        
    main() 