import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import '../providers/name_provider.dart';
import '../providers/wallpaper_provider.dart';
import '../providers/avatar_provider.dart';
import '../providers/ringtone_provider.dart';
import '../services/audio_manager.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  bool _isGuest = false;
  String? _lastError;
  bool _isLoading = false;

  bool get isSignedIn => _authService.isSignedIn;
  bool get isGuest => _isGuest;
  String? get lastError => _lastError;
  bool get isLoading => _isLoading;

  // Reset the last error
  void resetError() {
    _lastError = null;
    notifyListeners();
  }

  Future<bool> signInWithGoogle() async {
    if (_isLoading) return false;

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      // Attempt sign in with Google
      final result = await _authService.signInWithGoogle();

      // Check if sign-in succeeded
      if (result != null) {
        _isGuest = false;
        _isLoading = false;
        notifyListeners();
        return true;
      }

      // Double-check in case result is null but we are actually signed in
      if (_authService.isSignedIn) {
        _isGuest = false;
        _isLoading = false;
        notifyListeners();
        return true;
      }

      _lastError = "Failed to sign in with Google";
      _isLoading = false;
      notifyListeners();
      return false;
    } catch (e) {
      _lastError = "Error: ${e.toString()}";
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  void signInAsGuest() {
    _isGuest = true;
    notifyListeners();
  }

  // Clear favorites without requiring context
  Future<void> clearAllFavorites() async {
    try {
      // Instead of using providers directly, clear user data in a safe way
      debugPrint('Clearing all user favorites without context');

      // You could directly clear data in Firestore or SharedPreferences here
      // This is safer than trying to access other providers which may require context

      // For example, you might do something like:
      // await FirebaseFirestore.instance.collection('userFavorites')
      //     .where('userId', isEqualTo: _authService.currentUser?.uid)
      //     .get()
      //     .then((snapshot) {
      //       for (var doc in snapshot.docs) {
      //         doc.reference.delete();
      //       }
      //     });

      // Or clear shared preferences:
      // final prefs = await SharedPreferences.getInstance();
      // prefs.remove('userFavorites');
    } catch (e) {
      debugPrint('Error clearing favorites: $e');
    }
  }

  Future<bool> signOut(BuildContext context) async {
    if (_isLoading) return false;

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      // Force stop any bhajan playback
      await AudioManager().forceStopAndReleasePlayer(AudioType.bhajan);
      
      // Attempt to sign out
      await _authService.signOut();

      // Clear favorites WITHOUT using context
      await clearAllFavorites();

      // Update local state
      _isGuest = false;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _lastError = "Error during sign out: ${e.toString()}";
      _isLoading = false;
      notifyListeners();
      return false; // Return false to indicate failure
    }
  }

  // Load user's favorites after sign in
  Future<void> loadUserFavorites(BuildContext context) async {
    try {
      if (!_authService.isSignedIn) return;

      final nameProvider = Provider.of<NameProvider>(context, listen: false);
      final wallpaperProvider =
          Provider.of<WallpaperProvider>(context, listen: false);
      final avatarProvider =
          Provider.of<AvatarProvider>(context, listen: false);
      final ringtoneProvider =
          Provider.of<RingtoneProvider>(context, listen: false);

      // Load favorites for all providers
      await nameProvider.loadFavorites();
      await wallpaperProvider.loadFavorites();
      await avatarProvider.loadFavorites();
      await ringtoneProvider.loadFavorites();

      print('User favorites loaded successfully after sign in');
    } catch (e) {
      print('Error loading user favorites: $e');
    }
  }

  // Delete user data for GDPR compliance
  Future<void> deleteUserData(String userId) async {
    if (_isLoading) return;

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      // Delete user favorites and preferences from Firestore
      await _authService.deleteUserData(userId);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _lastError = "Error deleting user data: ${e.toString()}";
      _isLoading = false;
      notifyListeners();
      throw Exception("Failed to delete user data: ${e.toString()}");
    }
  }
}
