import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'dart:io';
import '../utils/color_utils.dart';

class StoriesRowWidget extends StatelessWidget {
  const StoriesRowWidget({Key? key}) : super(key: key);

  // Method to open YouTube link
  Future<void> _launchYoutubePlaylist(String playlistUrl) async {
    // Check if the URL can be launched
    final Uri url = Uri.parse(playlistUrl);
    
    try {
      // Try to launch in YouTube app first
      bool launched = false;
      
      // For Android, try to use the youtube app's URL scheme
      if (Platform.isAndroid) {
        final youtubeAppUrl = Uri.parse('youtube://playlist?list=PLa6CHPhFNfadNcnVZRXa6csHL5sFdkwmV');
        try {
          launched = await launchUrl(youtubeAppUrl);
        } catch (e) {
          debugPrint('Could not launch YouTube app URL: $e');
          // Continue to browser launch
        }
      }
      
      // If not launched yet (iOS or Android app not available), open in browser
      if (!launched) {
        await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      // Fallback to direct string URL launch
      try {
        await launchUrlString(
          playlistUrl,
          mode: LaunchMode.externalApplication,
        );
      } catch (e) {
        debugPrint('All URL launch attempts failed: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Row(
            children: [
              Text(
                'Stories',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 18,
                  fontFamily: 'Prakrta',
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.white.withOpacity(0.7),
              ),
            ],
          ),
        ),
        
        // Stories row - reduced height to fix overflow
        SizedBox(
          height: 160, // Reduced height to fix overflow
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            children: [
              // Mahabharat story item
              _buildStoryItem(
                context: context,
                imagePath: 'assets/images/mahabharat_thumbnail.jpg',
                title: 'Mahabharat',
                playlistUrl: 'https://youtube.com/playlist?list=PLa6CHPhFNfadNcnVZRXa6csHL5sFdkwmV&si=c_6HOCO4evCqfgKS',
              ),
              
              // You can add more story items here
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStoryItem({
    required BuildContext context,
    required String imagePath,
    required String title,
    required String playlistUrl,
  }) {
    // Reduced dimensions to fix overflow
    final double itemWidth = 90; // Reduced width
    final double itemHeight = 130; // Reduced height
    
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: GestureDetector(
        onTap: () => _launchYoutubePlaylist(playlistUrl),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image with 9:16 aspect ratio
            Container(
              width: itemWidth,
              height: itemHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              clipBehavior: Clip.antiAlias,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Stack(
                  children: [
                    // Image
                    Image.asset(
                      imagePath,
                      width: itemWidth,
                      height: itemHeight,
                      fit: BoxFit.cover,
                    ),
                    
                    // Play button overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.7),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.play_circle_fill,
                            color: AppColors.getPrimaryColor(context),
                            size: 50,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Title
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: SizedBox(
                width: itemWidth,
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
