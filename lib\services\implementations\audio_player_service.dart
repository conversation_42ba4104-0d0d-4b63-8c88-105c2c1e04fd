import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:rxdart/rxdart.dart';
import 'dart:async';
import '../../models/bhajan.dart';
import '../interfaces/audio_player_interface.dart';
import '../interfaces/audio_manager_interface.dart';
import '../interfaces/bhajan_repository_interface.dart';
import '../interfaces/audio_cache_interface.dart';
import 'package:cloud_firestore/cloud_firestore.dart' as firestore;

/// Implementation of the audio player service
class AudioPlayerService implements IAudioPlayerService {
  // Dependencies
  final IAudioManager _audioManager;
  final IBhajanRepository _bhajanRepository;
  final IAudioCacheService _cacheService;
  
  // State
  List<Bhajan> _bhajans = [];
  int _currentIndex = -1;
  bool _isInitialized = false;
  Duration? _lastPosition;
  
  // Streams
  final _currentBhajanController = StreamController<Bhajan?>.broadcast();
  
  // Constructor with dependency injection
  AudioPlayerService({
    required IAudioManager audioManager,
    required IBhajanRepository bhajanRepository,
    required IAudioCacheService cacheService,
  }) : _audioManager = audioManager,
       _bhajanRepository = bhajanRepository,
       _cacheService = cacheService {
    
    // Set up listeners
    _setupPlayerStateListener();
  }
  
  void _setupPlayerStateListener() {
    // Listen to audio type changes from the manager
    _audioManager.audioTypeStream.listen((audioType) {
      if (audioType != AudioType.bhajan) {
        // We no longer control the player, reset state
        _currentBhajanController.add(null);
      }
    });
  }
  
  @override
  Stream<Bhajan?> get currentBhajanStream => _currentBhajanController.stream;
  
  @override
  Bhajan? get currentBhajan => getCurrentBhajan();
  
  Bhajan? getCurrentBhajan() {
    if (_currentIndex < 0 || _currentIndex >= _bhajans.length) {
      return null;
    }
    return _bhajans[_currentIndex];
  }
  
  @override
  bool get isPlaying => _audioManager.player?.playing ?? false;
  
  @override
  Stream<PlayerState> get playerStateStream =>
      _audioManager.player?.playerStateStream ??
      Stream.value(PlayerState(false, ProcessingState.idle));
  
  @override
  Stream<PositionData> get positionDataStream {
    final player = _audioManager.player;
    if (player == null) {
      return Stream.value(
          PositionData(Duration.zero, Duration.zero, Duration.zero));
    }

    return Rx.combineLatest3<Duration, Duration, Duration?, PositionData>(
      player.positionStream,
      player.bufferedPositionStream,
      player.durationStream,
      (position, bufferedPosition, duration) =>
          PositionData(position, bufferedPosition, duration ?? Duration.zero),
    );
  }
  
  @override
  LoopMode? get loopMode => _audioManager.player?.loopMode;
  
  @override
  Future<void> init(List<Bhajan> bhajans, {bool emitCurrentBhajan = true}) async {
    debugPrint('AudioPlayerService: Initializing with ${bhajans.length} bhajans');
    
    if (bhajans.isEmpty) {
      debugPrint('AudioPlayerService: Cannot initialize with empty bhajans list');
      throw Exception('Cannot initialize audio service with empty bhajans list');
    }

    try {
      _bhajans = bhajans;

      // Get player from audio manager
      final player = await _audioManager.requestPlayer(AudioType.bhajan);

      if (player == null) {
        throw Exception('Failed to get audio player for bhajans');
      }

      // Set up player state listener
      player.playerStateStream.listen((playerState) {
        if (playerState.processingState == ProcessingState.completed) {
          playNext();
        }
      });

      // Listen to sequence state changes
      player.sequenceStateStream.listen((sequenceState) {
        if (sequenceState != null && _currentIndex != sequenceState.currentIndex) {
          _currentIndex = sequenceState.currentIndex ?? _currentIndex;
          _emitCurrentBhajan();
        }
      });

      // Create audio sources for first page of bhajans (implementing lazy loading)
      // Only preload the first 5 bhajans or fewer if less are available
      final initialBhajansCount = bhajans.length > 5 ? 5 : bhajans.length;
      final initialBhajans = bhajans.sublist(0, initialBhajansCount);

      debugPrint('AudioPlayerService: Creating initial playlist with $initialBhajansCount bhajans');
      
      final audioSources = await Future.wait(initialBhajans.map((bhajan) async {
        // Ensure the URL uses HTTPS
        var audioUrl = bhajan.r2Url;
        if (!audioUrl.startsWith('https://')) {
          audioUrl = audioUrl.replaceFirst('http://', 'https://');
        }

        // Use AudioManager to create AudioSource
        return await _audioManager.createAudioSource(
          url: audioUrl,
          id: bhajan.r2ObjectKey,
          title: bhajan.title,
          artist: bhajan.artist,
          artUri: Uri.parse(bhajan.artworkUrl),
          extras: {
            'duration': bhajan.duration,
            'type': 'bhajan',
          },
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
          },
        );
      }));

      final playlist = ConcatenatingAudioSource(
        children: audioSources,
      );

      // Retry logic for setting audio source
      int maxRetries = 3;
      int currentTry = 0;
      while (currentTry < maxRetries) {
        try {
          await player.setAudioSource(playlist, preload: false);
          break;
        } catch (e) {
          currentTry++;
          debugPrint('AudioPlayerService: Error setting audio source (attempt $currentTry): $e');
          
          if (currentTry == maxRetries) {
            rethrow;
          }
          
          // Exponential backoff
          await Future.delayed(Duration(seconds: 2 * currentTry));
        }
      }

      // Set initial settings
      await player.setLoopMode(LoopMode.off);

      _isInitialized = true;
      _currentIndex = 0;
      
      // Emit current bhajan if requested
      if (emitCurrentBhajan) {
        _emitCurrentBhajan();
      }

      // If we have more bhajans than the initial preloaded ones, schedule adding the rest
      if (bhajans.length > initialBhajansCount) {
        _scheduleLoadRemainingBhajans(bhajans.sublist(initialBhajansCount));
      }

      debugPrint('AudioPlayerService: Successfully initialized');
    } catch (e) {
      debugPrint('AudioPlayerService: Error during initialization: $e');
      _isInitialized = false;
      rethrow;
    }
  }
  
  // Helper method to emit current bhajan update
  void _emitCurrentBhajan() {
    _currentBhajanController.add(getCurrentBhajan());
  }
  
  // Load remaining bhajans in the background
  Future<void> _scheduleLoadRemainingBhajans(List<Bhajan> remainingBhajans) async {
    // Wait a bit to let the UI settle and initial bhajans to be ready
    await Future.delayed(const Duration(seconds: 2));

    // Load the remaining bhajans in small chunks to avoid blocking the UI for too long
    const int chunkSize = 5;
    final chunks = <List<Bhajan>>[];

    for (var i = 0; i < remainingBhajans.length; i += chunkSize) {
      chunks.add(remainingBhajans.sublist(
          i,
          i + chunkSize < remainingBhajans.length
              ? i + chunkSize
              : remainingBhajans.length));
    }

    // Get the ConcatenatingAudioSource
    final concatenatingSource =
        _audioManager.player?.audioSource as ConcatenatingAudioSource?;
    
    if (concatenatingSource == null) {
      debugPrint('AudioPlayerService: Cannot load remaining bhajans, player or audio source is null');
      return;
    }

    // Process each chunk with a delay between chunks
    for (var chunk in chunks) {
      try {
        final chunkAudioSources = await Future.wait(chunk.map((bhajan) async {
          var audioUrl = bhajan.r2Url;
          if (!audioUrl.startsWith('https://')) {
            audioUrl = audioUrl.replaceFirst('http://', 'https://');
          }

          return await _audioManager.createAudioSource(
            url: audioUrl,
            id: bhajan.r2ObjectKey,
            title: bhajan.title,
            artist: bhajan.artist,
            artUri: Uri.parse(bhajan.artworkUrl),
            extras: {
              'duration': bhajan.duration,
              'type': 'bhajan',
            },
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': '*/*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Connection': 'keep-alive',
            },
          );
        }));

        // Add this chunk to the playlist
        await concatenatingSource.addAll(chunkAudioSources);
        
        // Small delay between chunks to not block UI
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        debugPrint('AudioPlayerService: Error adding chunk to playlist: $e');
        // Continue with next chunk even if this one failed
      }
    }
  }
  
  @override
  Future<void> play(int index) async {
    try {
      // Check if the index is valid
      if (index < 0 || index >= _bhajans.length) {
        throw Exception('Invalid bhajan index: $index');
      }
      
      // Check if we're just resuming the same bhajan that was paused
      final isSameBhajan = _currentIndex == index && 
          _audioManager.player != null && 
          !(_audioManager.player?.playing ?? false);
      
      final currentPosition = isSameBhajan ? _audioManager.player?.position : null;
      
      // Set current index and emit immediately to trigger UI updates
      _currentIndex = index;
      _emitCurrentBhajan();
      
      // Get or request player
      final player = _audioManager.player ?? await _audioManager.requestPlayer(AudioType.bhajan);
      
      if (player == null) {
        throw Exception('Failed to get audio player for bhajans');
      }
      
      // If this is a different bhajan or we're initializing, set up the playlist
      if (!isSameBhajan || player.audioSource == null) {
        // Set up playlist with all bhajans
        await _setupPlaylistSource(initialIndex: index);
      } else if (isSameBhajan && currentPosition != null) {
        // If it's the same bhajan and we're just resuming, seek to the right position
        if (currentPosition > const Duration(milliseconds: 500)) {
          await player.seek(currentPosition);
        }
      }
      
      // Start playback
      await player.play();
      
      // Save the bhajan progress when starting playback
      final bhajan = _bhajans[index];
      final progress = await _bhajanRepository.getBhajanProgress(bhajan.id);
      
      if (progress != null && progress > 0.0 && progress < 0.95) {
        // If we have saved progress and it's not at the beginning or end,
        // seek to that position
        final duration = player.duration;
        if (duration != null) {
          final position = Duration(milliseconds: (duration.inMilliseconds * progress).round());
          await player.seek(position);
        }
      }
      
      // Emit to update UI
      _emitCurrentBhajan();
    } catch (e) {
      debugPrint('AudioPlayerService: Error playing bhajan: $e');
      rethrow;
    }
  }
  
  // Helper to set up playlist source
  Future<void> _setupPlaylistSource({int? initialIndex, Duration? initialPosition}) async {
    try {
      final player = _audioManager.player;
      if (player == null) {
        throw Exception('Player is null when setting up playlist source');
      }
      
      final position = initialPosition ?? player.position;
      final targetIndex = initialIndex ?? _currentIndex;
      
      // Create audio sources for each bhajan
      final audioSources = await Future.wait(_bhajans.map((bhajan) async {
        var audioUrl = bhajan.r2Url;
        if (!audioUrl.startsWith('https://')) {
          audioUrl = audioUrl.replaceFirst('http://', 'https://');
        }

        return await _audioManager.createAudioSource(
          url: audioUrl,
          id: bhajan.r2ObjectKey,
          title: bhajan.title,
          artist: bhajan.artist,
          artUri: Uri.parse(bhajan.artworkUrl),
          extras: {
            'duration': bhajan.duration,
            'type': 'bhajan',
          },
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
          },
        );
      }));

      final playlist = ConcatenatingAudioSource(
        children: audioSources,
      );

      // Set the audio source with the correct initial index
      await player.setAudioSource(
        playlist, 
        initialIndex: targetIndex,
        initialPosition: position,
      );
    } catch (e) {
      debugPrint('AudioPlayerService: Error setting up playlist source: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> pause() async {
    final player = _audioManager.player;
    if (player != null) {
      // Save position before pausing
      _lastPosition = player.position;
      
      // Save progress to repository
      final bhajan = getCurrentBhajan();
      if (bhajan != null) {
        final position = player.position;
        final duration = player.duration;
        if (duration != null && position != null) {
          final progress = position.inMilliseconds / duration.inMilliseconds;
          await _bhajanRepository.saveBhajanProgress(bhajan.id, progress);
        }
      }
      
      await player.pause();
    }
  }
  
  @override
  Future<void> resume() async {
    final player = _audioManager.player;
    if (player != null) {
      // If we have a saved position, seek to it before playing
      if (_lastPosition != null) {
        await player.seek(_lastPosition!);
        _lastPosition = null;
      }
      
      await player.play();
      _emitCurrentBhajan();
    } else {
      // If player is null, we need to request a new one and set it up
      final newPlayer = await _audioManager.requestPlayer(AudioType.bhajan);
      
      if (newPlayer != null) {
        // Set up the playlist again
        await _setupPlaylistSource();
        
        // Start playback
        await newPlayer.play();
        _emitCurrentBhajan();
      } else {
        throw Exception('Failed to get audio player for bhajans');
      }
    }
  }
  
  @override
  Future<void> seek(Duration position) async {
    final player = _audioManager.player;
    if (player != null) {
      await player.seek(position);
    }
  }
  
  @override
  Future<void> playNext() async {
    final player = _audioManager.player;
    if (player != null) {
      // Handle loop mode behavior
      if (player.loopMode == LoopMode.one) {
        // Temporarily change loop mode to play next
        final savedLoopMode = player.loopMode;
        await player.setLoopMode(LoopMode.off);
        await player.seekToNext();
        await player.setLoopMode(savedLoopMode);
      } else {
        // If at the end with repeat all, go back to start
        if (player.loopMode == LoopMode.all && 
            _currentIndex >= _bhajans.length - 1) {
          await player.seek(Duration.zero, index: 0);
          
          // Ensure playback continues
          if (player.playing) {
            await player.play();
          }
        } else {
          await player.seekToNext();
        }
      }
      
      // Update current bhajan immediately
      _emitCurrentBhajan();
    }
  }
  
  @override
  Future<void> playPrevious() async {
    final player = _audioManager.player;
    if (player != null) {
      // Handle loop mode behavior
      if (player.loopMode == LoopMode.one) {
        // Temporarily change loop mode to play previous
        final savedLoopMode = player.loopMode;
        await player.setLoopMode(LoopMode.off);
        await player.seekToPrevious();
        await player.setLoopMode(savedLoopMode);
      } else {
        // If at the beginning with repeat all, go to the end
        if (player.loopMode == LoopMode.all && 
            (_currentIndex <= 0 || player.currentIndex == 0)) {
          await player.seek(Duration.zero, index: _bhajans.length - 1);
          
          // Ensure playback continues
          if (player.playing) {
            await player.play();
          }
        } else {
          await player.seekToPrevious();
        }
      }
      
      // Update current bhajan immediately
      _emitCurrentBhajan();
    }
  }
  
  @override
  Future<void> setVolume(double volume) async {
    final player = _audioManager.player;
    if (player != null) {
      await player.setVolume(volume);
    }
  }
  
  @override
  Future<void> toggleRepeatMode() async {
    final player = _audioManager.player;
    if (player != null) {
      // Cycle through repeat modes: none -> all -> one -> none
      switch (player.loopMode) {
        case LoopMode.off:
          await player.setLoopMode(LoopMode.all);
          break;
        case LoopMode.all:
          await player.setLoopMode(LoopMode.one);
          break;
        case LoopMode.one:
          await player.setLoopMode(LoopMode.off);
          break;
      }
    }
  }
  
  @override
  Future<void> updateBhajans(List<Bhajan> newBhajans) async {
    if (newBhajans.isEmpty) {
      debugPrint('AudioPlayerService: Cannot update with empty bhajans list');
      return;
    }

    try {
      _bhajans = newBhajans;
      
      // If player is null, initialize fully
      if (_audioManager.player == null) {
        await init(newBhajans);
        return;
      }
      
      // Set up a new playlist
      await _setupPlaylistSource();
      
      // Reset current index
      _currentIndex = 0;
      _emitCurrentBhajan();
    } catch (e) {
      debugPrint('AudioPlayerService: Error updating bhajans: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> updatePlaylist(List<Bhajan> newBhajans, {bool emitCurrentBhajan = true}) async {
    try {
      if (newBhajans.isEmpty) {
        debugPrint('AudioPlayerService: Cannot update playlist with empty list');
        return;
      }

      // Remember current bhajan and state
      final currentBhajan = getCurrentBhajan();
      final player = _audioManager.player;
      final wasPlaying = player?.playing ?? false;
      final position = player?.position;
      
      // Update bhajans list
      _bhajans = newBhajans;
      
      // If no player, initialize fully
      if (player == null) {
        await init(newBhajans, emitCurrentBhajan: emitCurrentBhajan);
        return;
      }
      
      // Set up new playlist
      await _setupPlaylistSource();
      
      // Try to find the previous bhajan in the new list
      if (currentBhajan != null) {
        final newIndex = _bhajans.indexWhere((b) => b.id == currentBhajan.id);
        if (newIndex >= 0) {
          _currentIndex = newIndex;
          await player.seek(position ?? Duration.zero, index: newIndex);
          
          // Resume if it was playing
          if (wasPlaying) {
            await player.play();
          }
        }
      }
      
      // Emit update if requested
      if (emitCurrentBhajan) {
        _emitCurrentBhajan();
      }
    } catch (e) {
      debugPrint('AudioPlayerService: Error updating playlist: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> createTemporaryPlaylist(List<Bhajan> bhajans, int initialIndex) async {
    try {
      if (bhajans.isEmpty) {
        throw Exception('Cannot create temporary playlist with empty list');
      }
      
      // Make a copy of the bhajans list so we don't modify the original
      final sortedBhajans = List<Bhajan>.from(bhajans);
      
      // Sort the bhajans alphabetically by title for consistency
      sortedBhajans.sort((a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));
      
      // Adjust initialIndex if needed after sorting
      // The bhajan originally at initialIndex might now be at a different position
      Bhajan targetBhajan = bhajans[initialIndex];
      int newIndex = sortedBhajans.indexWhere((b) => b.id == targetBhajan.id);
      if (newIndex == -1) newIndex = 0; // Fallback to first song if not found
      
      if (initialIndex < 0 || initialIndex >= bhajans.length) {
        throw Exception('Invalid initial index for temporary playlist');
      }
      
      // Remember state
      final wasPlaying = _audioManager.player?.playing ?? false;
      
      // Request player if needed
      final player = _audioManager.player ?? await _audioManager.requestPlayer(AudioType.bhajan);
      
      if (player == null) {
        throw Exception('Failed to get audio player');
      }
      
      // Create audio sources for all bhajans in the temporary playlist
      final audioSources = await Future.wait(sortedBhajans.map((bhajan) async {
        var audioUrl = bhajan.r2Url;
        if (!audioUrl.startsWith('https://')) {
          audioUrl = audioUrl.replaceFirst('http://', 'https://');
        }

        return await _audioManager.createAudioSource(
          url: audioUrl,
          id: bhajan.r2ObjectKey,
          title: bhajan.title,
          artist: bhajan.artist,
          artUri: Uri.parse(bhajan.artworkUrl),
          extras: {
            'duration': bhajan.duration,
            'type': 'bhajan',
          },
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
          },
        );
      }));

      final playlist = ConcatenatingAudioSource(
        children: audioSources,
      );
      
      // Set the audio source with the adjusted index after sorting
      await player.setAudioSource(playlist, initialIndex: newIndex);
      
      // Update state with sorted bhajans
      _bhajans = sortedBhajans;
      _currentIndex = newIndex;
      _emitCurrentBhajan();
      
      // Start playback
      await player.play();
    } catch (e) {
      debugPrint('AudioPlayerService: Error creating temporary playlist: $e');
      rethrow;
    }
  }
  
  @override
  void resetCurrentBhajan() {
    _currentIndex = -1;
    _emitCurrentBhajan();
  }
  
  @override
  Future<void> dispose() async {
    // The actual player is managed by the audio manager
    // Just close the stream controller
    await _currentBhajanController.close();
  }
} 