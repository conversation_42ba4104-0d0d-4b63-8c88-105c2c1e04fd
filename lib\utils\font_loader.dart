import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppFontLoader {
  static bool _fontsLoaded = false;

  /// Loads all custom fonts used in the app
  static Future<void> loadFonts() async {
    if (_fontsLoaded) return;

    try {
      debugPrint('🔄 Loading custom fonts...');

      // Load the Prakrta font
      final prakrtaFont = FontLoader('Prakrta');
      try {
        final ByteData data = await rootBundle.load('assets/fonts/Prakrta.ttf');
        prakrtaFont.addFont(Future.value(data));
        await prakrtaFont.load();
        debugPrint('✅ Prakrta font loaded successfully');
      } catch (e) {
        debugPrint('❌ Error loading Prakrta font: $e');
      }

      // Load JosefinSans font
      final josefinFont = FontLoader('JosefinSans');
      try {
        final ByteData data =
            await rootBundle.load('assets/fonts/JosefinSans-Regular.ttf');
        josefinFont.addFont(Future.value(data));
        await josefinFont.load();
        debugPrint('✅ JosefinSans font loaded successfully');
      } catch (e) {
        debugPrint('❌ Error loading JosefinSans font: $e');
      }

      _fontsLoaded = true;
      debugPrint('✅ Custom fonts loading completed');
    } catch (e) {
      debugPrint('❌ Error in font loading process: $e');
    }
  }

  /// Returns a TextStyle with the Prakrta font applied
  static TextStyle getPrakrtaStyle({
    double fontSize = 24.0,
    FontWeight fontWeight = FontWeight.w500,
    Color color = Colors.white,
    double? letterSpacing,
  }) {
    return TextStyle(
      fontFamily: 'Prakrta',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
    );
  }

  /// Returns a TextStyle with the JosefinSans font applied
  static TextStyle getJosefinStyle({
    double fontSize = 16.0,
    FontWeight fontWeight = FontWeight.normal,
    Color color = Colors.white,
    double? letterSpacing,
    double? height,
  }) {
    return TextStyle(
      fontFamily: 'JosefinSans',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
    );
  }
}
