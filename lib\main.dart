import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'config/firebase_config.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'screens/main_screen.dart';
import 'screens/sign_in_screen.dart';
import 'screens/splash_screen.dart';
import 'providers/auth_provider.dart' as app_provider;
import 'providers/audio_provider.dart';
import 'providers/name_provider.dart';
import 'providers/wallpaper_provider.dart';
import 'providers/avatar_provider.dart';
import 'providers/pomodoro_provider.dart';
import 'providers/ringtone_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/ad_provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'utils/font_loader.dart';
import 'utils/navigation_service.dart' as nav_service;
import 'services/audio_manager.dart';
import 'services/image_cache_service.dart' hide NavigationService;
import 'services/permission_service.dart';
import 'di/service_locator.dart';

Future<void> main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize permission service
  final permissionService = PermissionService();
  await permissionService.init();

  // Pre-load fonts
  await AppFontLoader.loadFonts();

  // Initialize Firebase with config
  await Firebase.initializeApp(
    options: FirebaseOptions(
      apiKey: FirebaseConfig.apiKey,
      appId: FirebaseConfig.appId,
      messagingSenderId: FirebaseConfig.messagingSenderId,
      projectId: FirebaseConfig.projectId,
      storageBucket: FirebaseConfig.storageBucket,
    ),
  );

  // Initialize the dependency injection service locator
  await ServiceLocator.instance.initialize();

  // Initialize image cache service
  ImageCacheService().optimizeCacheSize();

  // Initialize Mobile Ads SDK
  await MobileAds.instance.initialize();

  // Initialize audio service
  try {
    await JustAudioBackground.init(
      androidNotificationChannelId: 'com.studiohabre.hindupath.channel.audio',
      androidNotificationChannelName: 'Hindu Path Audio',
      androidNotificationChannelDescription: 'Bhajan playback notification',
      androidNotificationOngoing: true,
      androidShowNotificationBadge: true,
      androidStopForegroundOnPause: true,
      notificationColor: const Color(0xFF1b4332),
      androidNotificationIcon: 'drawable/notification_icon',
      preloadArtwork: true,
      fastForwardInterval: const Duration(seconds: 10),
      rewindInterval: const Duration(seconds: 10),
    );

    // Initialize the shared audio manager
    await AudioManager().initialize();
    debugPrint('Audio manager initialized successfully');
  } catch (e) {
    debugPrint('Error initializing audio service: $e');
  }

  // Reset ad counters on app launch
  final adProvider = AdProvider();
  adProvider.resetAdCounters();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => app_provider.AuthProvider()),
        ChangeNotifierProvider(create: (_) => AudioProvider()),
        ChangeNotifierProvider(create: (_) => NameProvider()),
        ChangeNotifierProvider(create: (_) => WallpaperProvider()),
        ChangeNotifierProvider(create: (_) => AvatarProvider()),
        ChangeNotifierProvider(create: (_) => PomodoroProvider()),
        ChangeNotifierProvider(create: (_) => RingtoneProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AdProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            title: 'Hindu Path',
            theme: themeProvider.getTheme(),
            navigatorKey: nav_service.NavigationService.navigatorKey,
            home: const SplashScreen(),
            routes: {
              '/home': (context) {
                final args = ModalRoute.of(context)?.settings.arguments;
                final initialIndex = args is int ? args : 0;
                return MainScreen(initialIndex: initialIndex);
              },
              '/signin': (context) => const SignInScreen(),
              '/login': (context) => const SignInScreen(),
              '/splash': (context) => const SplashScreen(),
            },
          );
        },
      ),
    );
  }
}
