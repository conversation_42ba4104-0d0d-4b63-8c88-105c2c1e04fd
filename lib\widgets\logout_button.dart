import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../screens/sign_in_screen.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../services/audio_manager.dart';
import '../utils/ui_helpers.dart';
import '../providers/auth_provider.dart' as app_provider;

class LogoutButton extends StatelessWidget {
  const LogoutButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.logout, color: Colors.white),
      title: const Text(
        'Logout',
        style: TextStyle(color: Colors.white),
      ),
      onTap: () async {
        // Show a simple confirmation dialog
        final bool confirmLogout = await showDialog(
              context: context,
              builder: (context) => AlertDialog(
                backgroundColor: const Color(0xFF1A1A1A),
                title:
                    const Text('Logout', style: TextStyle(color: Colors.white)),
                content: const Text('Are you sure you want to logout?',
                    style: TextStyle(color: Colors.white70)),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel',
                        style: TextStyle(color: Colors.white70)),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1B4332),
                    ),
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Logout',
                        style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ) ??
            false;

        if (!confirmLogout) return;

        // Close the drawer FIRST to avoid context issues
        Navigator.pop(context);

        // Show loading dialog
        BuildContext? dialogContext;
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (builderContext) {
            dialogContext = builderContext;
            return const Dialog(
              backgroundColor: Colors.transparent,
              elevation: 0,
              child: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1B4332)),
                ),
              ),
            );
          },
        );

        bool completed = false;

        try {
          // Handle audio
          try {
            final audioProvider =
                Provider.of<AudioProvider>(context, listen: false);
            if (audioProvider.isPlaying) {
              audioProvider.pauseBhajan();
            }

            final audioManager = AudioManager();
            if (audioManager.player?.playing == true) {
              await audioManager.player?.pause();
            }

            try {
              await audioManager.player?.stop();
            } catch (e) {
              debugPrint('Non-critical: Error stopping audio: $e');
            }
          } catch (audioError) {
            debugPrint('Non-critical: Audio error: $audioError');
          }

          // Use AuthProvider to sign out
          try {
            final authProvider =
                Provider.of<app_provider.AuthProvider>(context, listen: false);
            await authProvider.signOut(context);
          } catch (signOutError) {
            debugPrint('Error in auth provider signOut: $signOutError');
          }

          // Dismiss dialog
          if (dialogContext != null && !completed) {
            try {
              Navigator.of(dialogContext!, rootNavigator: true).pop();
            } catch (e) {
              debugPrint('Non-critical: Error dismissing dialog: $e');
            }
          }

          // Navigate to sign-in screen using root navigator
          Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const SignInScreen()),
            (route) => false,
          );

          completed = true;
        } catch (e) {
          debugPrint('Error during logout flow: $e');

          // Dismiss dialog if needed
          if (dialogContext != null && !completed) {
            try {
              Navigator.of(dialogContext!, rootNavigator: true).pop();
            } catch (dialogError) {
              debugPrint('Non-critical: Error dismissing dialog: $dialogError');
            }
          }

          // Navigate to sign-in screen anyway
          if (!completed) {
            Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const SignInScreen()),
              (route) => false,
            );
            completed = true;
          }
        }
      },
    );
  }
}
