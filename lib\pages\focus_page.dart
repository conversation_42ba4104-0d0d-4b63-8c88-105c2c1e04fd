import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/pomodoro_provider.dart';
import '../providers/theme_provider.dart';
import '../utils/font_loader.dart';
import 'package:numberpicker/numberpicker.dart';

class FocusPage extends StatelessWidget {
  const FocusPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      appBar: AppBar(
        title: Text(
          'Focus Timer',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF0D0D0D),
        centerTitle: true,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFFE5E7EB)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Safe<PERSON>rea(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF0D0D0D),
                const Color(0xFF0A0A0A),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const SizedBox(height: 30),
                _buildSessionIndicator(context),
                const Spacer(),
                _buildTimer(context),
                const SizedBox(height: 50),
                _buildControls(context),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSessionIndicator(BuildContext context) {
    return Consumer<PomodoroProvider>(
      builder: (context, provider, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            provider.sessionsBeforeLongBreak,
            (index) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index <
                        provider.completedSessions %
                            provider.sessionsBeforeLongBreak
                    ? ThemeProvider.getGreenColor(context)
                    : const Color(0xFF374151),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTimer(BuildContext context) {
    return Consumer<PomodoroProvider>(
      builder: (context, provider, child) {
        final minutes =
            ((provider.currentDuration - provider.elapsedSeconds) ~/ 60)
                .toString()
                .padLeft(2, '0');
        final seconds =
            ((provider.currentDuration - provider.elapsedSeconds) % 60)
                .toString()
                .padLeft(2, '0');

        Color primaryColor;
        switch (provider.status) {
          case PomodoroStatus.focus:
            primaryColor = ThemeProvider.getGreenColor(context);
            break;
          case PomodoroStatus.shortBreak:
            primaryColor = const Color(0xFF3B82F6);
            break;
          case PomodoroStatus.longBreak:
            primaryColor = const Color(0xFF8B5CF6);
            break;
          default:
            primaryColor = ThemeProvider.getGreenColor(context);
        }

        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: 320,
              height: 320,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF1F2937),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 280,
              height: 280,
              child: CircularProgressIndicator(
                value: provider.progress,
                strokeWidth: 12,
                backgroundColor: const Color(0xFF374151),
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              ),
            ),
            Container(
              width: 260,
              height: 260,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFF1F2937),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '$minutes:$seconds',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 64,
                      fontWeight: FontWeight.w300,
                      letterSpacing: 2,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: primaryColor.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      provider.status == PomodoroStatus.focus
                          ? 'FOCUS'
                          : provider.status == PomodoroStatus.shortBreak
                              ? 'SHORT BREAK'
                              : 'LONG BREAK',
                      style: TextStyle(
                        color: primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildControls(BuildContext context) {
    return Consumer<PomodoroProvider>(
      builder: (context, provider, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildControlButton(
              Icons.refresh_rounded,
              'Reset',
              onPressed: provider.resetTimer,
              color: const Color(0xFF4B5563),
              isTextVisible: false,
            ),
            const SizedBox(width: 24),
            _buildMainButton(context, provider),
            const SizedBox(width: 24),
            _buildControlButton(
              Icons.settings_rounded,
              'Settings',
              onPressed: () => _showSettingsModal(context),
              color: const Color(0xFF4B5563),
              isTextVisible: false,
            ),
          ],
        );
      },
    );
  }

  Widget _buildControlButton(IconData icon, String label,
      {required VoidCallback onPressed,
      required Color color,
      bool isTextVisible = true}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(icon, color: color, size: 24),
            onPressed: onPressed,
          ),
        ),
        if (isTextVisible) ...[
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMainButton(BuildContext context, PomodoroProvider provider) {
    Color primaryColor;
    switch (provider.status) {
      case PomodoroStatus.focus:
        primaryColor = ThemeProvider.getGreenColor(context);
        break;
      case PomodoroStatus.shortBreak:
        primaryColor = const Color(0xFF3B82F6);
        break;
      case PomodoroStatus.longBreak:
        primaryColor = const Color(0xFF8B5CF6);
        break;
      default:
        primaryColor = ThemeProvider.getGreenColor(context);
    }

    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            primaryColor,
            primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(40),
          onTap: provider.isRunning ? provider.pauseTimer : provider.startTimer,
          child: Center(
            child: Icon(
              provider.isRunning
                  ? Icons.pause_rounded
                  : Icons.play_arrow_rounded,
              color: Colors.white,
              size: 36,
            ),
          ),
        ),
      ),
    );
  }

  void _showSettingsModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: const PomodoroSettings(),
        ),
      ),
    );
  }
}

class PomodoroSettings extends StatefulWidget {
  const PomodoroSettings({super.key});

  @override
  State<PomodoroSettings> createState() => _PomodoroSettingsState();
}

class _PomodoroSettingsState extends State<PomodoroSettings> {
  late int focusDuration;
  late int shortBreakDuration;
  late int longBreakDuration;
  late int sessionsBeforeLongBreak;

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<PomodoroProvider>(context, listen: false);
    focusDuration = provider.focusDuration;
    shortBreakDuration = provider.shortBreakDuration;
    longBreakDuration = provider.longBreakDuration;
    sessionsBeforeLongBreak = provider.sessionsBeforeLongBreak;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
      decoration: const BoxDecoration(
        color: Color(0xFF1F2937),
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Timer Settings',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white70, size: 24),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildDurationSetting(
            'Focus Duration',
            focusDuration.toString() + ' min',
            () => _showDurationPicker(
                context, 'Focus Duration', focusDuration, 1, 120, (value) {
              setState(() => focusDuration = value);
            }),
            ThemeProvider.getGreenColor(context),
            Icons.timer,
          ),
          _buildDurationSetting(
            'Short Break',
            shortBreakDuration.toString() + ' min',
            () => _showDurationPicker(
                context, 'Short Break', shortBreakDuration, 1, 30, (value) {
              setState(() => shortBreakDuration = value);
            }),
            const Color(0xFF3B82F6),
            Icons.coffee,
          ),
          _buildDurationSetting(
            'Long Break',
            longBreakDuration.toString() + ' min',
            () => _showDurationPicker(
                context, 'Long Break', longBreakDuration, 5, 60, (value) {
              setState(() => longBreakDuration = value);
            }),
            const Color(0xFF8B5CF6),
            Icons.weekend,
          ),
          _buildDurationSetting(
            'Sessions Before Long Break',
            sessionsBeforeLongBreak.toString() + ' sessions',
            () => _showDurationPicker(context, 'Sessions Before Long Break',
                sessionsBeforeLongBreak, 1, 10, (value) {
              setState(() => sessionsBeforeLongBreak = value);
            }),
            const Color(0xFFEC4899),
            Icons.repeat,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Provider.of<PomodoroProvider>(context, listen: false)
                  .updateSettings(
                newFocusDuration: focusDuration,
                newShortBreakDuration: shortBreakDuration,
                newLongBreakDuration: longBreakDuration,
                newSessionsBeforeLongBreak: sessionsBeforeLongBreak,
              );
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeProvider.getGreenColor(context),
              foregroundColor: Colors.white,
              elevation: 0,
              minimumSize: const Size(double.infinity, 50),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Save Settings',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                letterSpacing: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationSetting(
    String label,
    String value,
    VoidCallback onTap,
    Color accentColor,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: accentColor,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: onTap,
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFF111827),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      value,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down, color: Colors.white),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDurationPicker(
    BuildContext context,
    String title,
    int currentValue,
    int minValue,
    int maxValue,
    Function(int) onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF0D0D0D),
        title: Text(
          'Select $title',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w500,
            fontFamily: 'Roboto',
          ),
          textAlign: TextAlign.center,
        ),
        content: Container(
          width: double.maxFinite,
          height: 300,
          constraints: const BoxConstraints(maxHeight: 300),
          child: ListView.builder(
            itemCount: maxValue - minValue + 1,
            itemExtent: 50.0,
            itemBuilder: (context, index) {
              final value = minValue + index;
              final isSelected = value == currentValue;
              final accentColor = title.contains('Focus')
                  ? ThemeProvider.getGreenColor(context)
                  : title.contains('Short')
                      ? const Color(0xFF3B82F6)
                      : title.contains('Long')
                          ? const Color(0xFF8B5CF6)
                          : const Color(0xFFEC4899);

              return Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    onChanged(value);
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? accentColor : null,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '$value ${title.contains('Sessions') ? 'sessions' : 'min'}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
