import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'dart:math';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../utils/navigation_service.dart' as nav_service;

class ImageCacheService {
  // Singleton pattern
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  // Clear all cached images
  Future<void> clearImageCache() async {
    await DefaultCacheManager().emptyCache();

    // Also clear memory cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  // Preload important images - improved to be more selective and efficient
  Future<void> preloadImages(List<String> urls, {int maxConcurrent = 3}) async {
    // Limit the number of images to preload based on device capability
    int deviceAwareLimit = _getDeviceAwarePreloadLimit();
    if (urls.length > deviceAwareLimit) {
      debugPrint(
          'ImageCacheService: Limiting preload to $deviceAwareLimit images (from ${urls.length})');
      urls = urls.sublist(0, deviceAwareLimit);
    }

    // Preload images concurrently but with a limit to avoid overwhelming the device
    final chunks = _chunkList(urls, maxConcurrent);
    for (var chunk in chunks) {
      await Future.wait(chunk.map((url) => _preloadSingleImage(url)));
    }
  }

  // Preload a single image with error handling
  Future<void> _preloadSingleImage(String url) async {
    try {
      final context = nav_service.NavigationService.navigatorKey.currentContext;
      if (context == null) return;

      // Check if image already exists in cache
      final file = await DefaultCacheManager().getFileFromCache(url);
      if (file != null) {
        debugPrint('ImageCacheService: Image already in cache: $url');
        return;
      }

      // Use lower resolution for preloading to save memory
      await precacheImage(
        CachedNetworkImageProvider(
          url,
          maxWidth: 300, // Limit initial load resolution
          maxHeight: 300,
        ),
        context,
      );
    } catch (e) {
      debugPrint('ImageCacheService: Error preloading image $url: $e');
      // Fail silently to not block the app
    }
  }

  // Split a list into chunks for concurrent processing
  List<List<T>> _chunkList<T>(List<T> list, int chunkSize) {
    final chunks = <List<T>>[];
    for (var i = 0; i < list.length; i += chunkSize) {
      chunks.add(list.sublist(
          i, i + chunkSize < list.length ? i + chunkSize : list.length));
    }
    return chunks;
  }

  // Get device-aware limit for preloading based on device capabilities
  int _getDeviceAwarePreloadLimit() {
    // Low-end devices: 5, Mid-range: 10, High-end: 15
    int defaultLimit = 10;

    try {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final context =
            nav_service.NavigationService.navigatorKey.currentContext;
        if (context == null) return;

        final screenSize = MediaQuery.of(context).size;
        final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

        // Calculate rough estimate of device capability based on screen resolution
        final pixelCount =
            screenSize.width * screenSize.height * pow(devicePixelRatio, 2);

        if (pixelCount > 4000000) {
          // High-end device
          defaultLimit = 15;
        } else if (pixelCount > 2000000) {
          // Mid-range device
          defaultLimit = 10;
        } else {
          // Low-end device
          defaultLimit = 5;
        }
      });

      return defaultLimit;
    } catch (e) {
      debugPrint('ImageCacheService: Error determining preload limit: $e');
      return 5; // Conservative default
    }
  }

  // Optimize cache size based on device capabilities
  void optimizeCacheSize() {
    // Get device memory info if available
    try {
      // Set a reasonable default for most devices
      int maxSize = 100; // Default 100MB

      // Higher end devices can have larger cache
      if (Platform.isAndroid || Platform.isIOS) {
        // This is a simplified approach - ideally you'd use device_info_plus
        // to get actual device capabilities
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final context =
              nav_service.NavigationService.navigatorKey.currentContext;
          if (context == null) return;

          final screenSize = MediaQuery.of(context).size;
          final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

          // Calculate rough estimate of device capability based on screen size and pixel ratio
          final pixelCount =
              screenSize.width * screenSize.height * pow(devicePixelRatio, 2);

          // Adjust cache size based on estimated device capability
          if (pixelCount > 4000000) {
            // High-end device
            maxSize = 200;
          } else if (pixelCount > 2000000) {
            // Mid-range device
            maxSize = 150;
          } else {
            // Low-end device
            maxSize = 80;
          }

          // Set the cache size (converting MB to bytes)
          PaintingBinding.instance.imageCache.maximumSizeBytes =
              maxSize * 1024 * 1024;
          debugPrint('ImageCacheService: Set image cache size to $maxSize MB');
        });
      }
    } catch (e) {
      debugPrint('ImageCacheService: Error optimizing cache size: $e');
      // Set a conservative default
      PaintingBinding.instance.imageCache.maximumSizeBytes =
          80 * 1024 * 1024; // 80MB
    }
  }
}

// Access to the default cache manager
DefaultCacheManager cacheManager() => DefaultCacheManager();
