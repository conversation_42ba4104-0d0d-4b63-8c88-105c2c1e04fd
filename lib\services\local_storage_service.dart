import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorageService {
  static const String _playlistCoversKey = 'playlist_covers';
  
  // Get the application documents directory
  Future<Directory> get _localDir async {
    final directory = await getApplicationDocumentsDirectory();
    final playlistCoversDir = Directory('${directory.path}/playlist_covers');
    if (!await playlistCoversDir.exists()) {
      await playlistCoversDir.create(recursive: true);
    }
    return playlistCoversDir;
  }

  // Save an image file for a playlist
  Future<String> savePlaylistCover(String playlistId, File imageFile) async {
    try {
      final directory = await _localDir;
      final extension = path.extension(imageFile.path);
      final fileName = 'playlist_${playlistId}$extension';
      final savedFile = File('${directory.path}/$fileName');

      // Copy the image file to our app's directory
      await imageFile.copy(savedFile.path);

      // Save the mapping in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final covers = prefs.getStringList(_playlistCoversKey) ?? [];
      if (!covers.contains(fileName)) {
        covers.add(fileName);
        await prefs.setStringList(_playlistCoversKey, covers);
      }

      return savedFile.path;
    } catch (e) {
      print('Error saving playlist cover: $e');
      rethrow;
    }
  }

  // Get the local path for a playlist's cover image
  Future<String?> getPlaylistCoverPath(String playlistId) async {
    try {
      final directory = await _localDir;
      final prefs = await SharedPreferences.getInstance();
      final covers = prefs.getStringList(_playlistCoversKey) ?? [];
      
      // Find the first file that matches the playlist ID
      final fileName = covers.firstWhere(
        (cover) => cover.startsWith('playlist_$playlistId'),
        orElse: () => '',
      );

      if (fileName.isEmpty) return null;

      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      if (await file.exists()) {
        return filePath;
      }
      return null;
    } catch (e) {
      print('Error getting playlist cover path: $e');
      return null;
    }
  }

  // Delete a playlist's cover image
  Future<void> deletePlaylistCover(String playlistId) async {
    try {
      final coverPath = await getPlaylistCoverPath(playlistId);
      if (coverPath == null) return;

      final file = File(coverPath);
      if (await file.exists()) {
        await file.delete();
      }

      // Remove from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final covers = prefs.getStringList(_playlistCoversKey) ?? [];
      covers.removeWhere((cover) => cover.startsWith('playlist_$playlistId'));
      await prefs.setStringList(_playlistCoversKey, covers);
    } catch (e) {
      print('Error deleting playlist cover: $e');
    }
  }
}
