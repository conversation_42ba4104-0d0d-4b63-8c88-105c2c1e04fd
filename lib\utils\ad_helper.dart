import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/ad_provider.dart';

class AdHelper {
  // Singleton instance
  static final AdHelper _instance = AdHelper._internal();
  factory AdHelper() => _instance;
  AdHelper._internal();

  // Track ad display frequency
  int _interstitialAdCounter = 0;
  static const int interstitialAdFrequency = 5; // Show ad every 5 actions
  
  // Show interstitial ad based on frequency
  void showInterstitialAd(BuildContext context) {
    _interstitialAdCounter++;
    
    if (_interstitialAdCounter >= interstitialAdFrequency) {
      final adProvider = Provider.of<AdProvider>(context, listen: false);
      adProvider.showInterstitialAd();
      _interstitialAdCounter = 0; // Reset counter
    }
  }
  
  // Reset counter
  void resetCounter() {
    _interstitialAdCounter = 0;
  }
}