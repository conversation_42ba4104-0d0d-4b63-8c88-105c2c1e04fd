package com.studiohabre.hindupath

import android.content.ContentValues
import android.media.MediaScannerConnection
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

class HindupathGallerySaver(private val activity: MainActivity) {
    private val TAG = "HindupathGallerySaver"

    @Throws(Exception::class)
    fun saveImage(filePath: String): String {
        Log.d(TAG, "Starting saveImage with filePath: $filePath")
        
        val sourceFile = File(filePath)
        if (!sourceFile.exists()) {
            Log.e(TAG, "Source file doesn't exist: $filePath")
            throw IOException("Source file doesn't exist: $filePath")
        }
        
        Log.d(TAG, "Source file exists, size: ${sourceFile.length()} bytes")

        val fileName = sourceFile.name
        val mimeType = when {
            fileName.endsWith(".png", ignoreCase = true) -> "image/png"
            fileName.endsWith(".jpg", ignoreCase = true) || fileName.endsWith(".jpeg", ignoreCase = true) -> "image/jpeg"
            else -> {
                Log.e(TAG, "Unsupported file type: $fileName")
                throw IOException("Unsupported file type: must be PNG or JPEG")
            }
        }
        
        Log.d(TAG, "File name: $fileName, MIME type: $mimeType")
        Log.d(TAG, "Android SDK version: ${Build.VERSION.SDK_INT}")
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                Log.d(TAG, "Using MediaStore API (Android 10+)")
                val values = ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
                    put(MediaStore.Images.Media.MIME_TYPE, mimeType)
                    put(MediaStore.Images.Media.RELATIVE_PATH, "${Environment.DIRECTORY_PICTURES}/HinduPath_Wallpapers")
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }
                
                val contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                Log.d(TAG, "Inserting new MediaStore entry")
                val uri = activity.contentResolver.insert(contentUri, values)
                    ?: throw IOException("Failed to create MediaStore entry")
                
                Log.d(TAG, "MediaStore entry created: $uri")
                
                try {
                    activity.contentResolver.openOutputStream(uri).use { outputStream ->
                        if (outputStream == null) {
                            Log.e(TAG, "Failed to open output stream for URI: $uri")
                            throw IOException("Failed to open output stream")
                        }
                        
                        FileInputStream(sourceFile).use { inputStream ->
                            Log.d(TAG, "Copying file data to MediaStore...")
                            val bytesCopied = inputStream.copyTo(outputStream)
                            Log.d(TAG, "Copied $bytesCopied bytes to MediaStore")
                        }
                    }
                    
                    // Mark the image as not pending to make it visible
                    values.clear()
                    values.put(MediaStore.Images.Media.IS_PENDING, 0)
                    activity.contentResolver.update(uri, values, null, null)
                    
                    Log.d(TAG, "Image saved successfully with MediaStore: $uri")
                    return uri.toString()
                } catch (e: Exception) {
                    Log.e(TAG, "Error writing to MediaStore: ${e.message}", e)
                    activity.contentResolver.delete(uri, null, null)
                    throw e
                }
            } else {
                Log.d(TAG, "Using legacy file storage method (pre-Android 10)")
                val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                val appDir = File(picturesDir, "HinduPath_Wallpapers")
                
                Log.d(TAG, "Creating directory: ${appDir.absolutePath}")
                if (!appDir.exists() && !appDir.mkdirs()) {
                    Log.e(TAG, "Failed to create directory: ${appDir.absolutePath}")
                    throw IOException("Failed to create directory: ${appDir.absolutePath}")
                }
                
                val destFile = File(appDir, fileName)
                Log.d(TAG, "Destination file: ${destFile.absolutePath}")
                
                FileOutputStream(destFile).use { out ->
                    FileInputStream(sourceFile).use { input ->
                        val bytesCopied = input.copyTo(out)
                        Log.d(TAG, "Copied $bytesCopied bytes to destination file")
                    }
                }
                
                // Notify the MediaScanner about the new file
                Log.d(TAG, "Notifying MediaScanner about new file")
                MediaScannerConnection.scanFile(
                    activity,
                    arrayOf(destFile.absolutePath),
                    arrayOf(mimeType)
                ) { path, uri ->
                    Log.d(TAG, "Image scan completed: $path, URI: $uri")
                }
                
                Log.d(TAG, "Image saved successfully with legacy method: ${destFile.absolutePath}")
                return destFile.absolutePath
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in saveImage: ${e.message}", e)
            throw e
        }
    }
}