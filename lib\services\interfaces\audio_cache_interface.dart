import 'package:flutter/foundation.dart';
import 'dart:io';

/// Interface defining the audio caching functionality
abstract class IAudioCacheService {
  /// Get a cached file by its ID
  Future<File?> getCachedFile(String fileId);
  
  /// Cache a file from a URL
  Future<File> cacheFile(String url, String fileId);
  
  /// Check if a file is already cached
  Future<bool> isFileCached(String fileId);
  
  /// Remove a file from cache
  Future<void> removeCachedFile(String fileId);
  
  /// Clear all cached files
  Future<void> clearCache();
  
  /// Get the cache directory
  Future<Directory> getCacheDirectory();
  
  /// Get the size of the cache in bytes
  Future<int> getCacheSize();
  
  /// Prune the cache to keep it under a certain size
  Future<void> pruneCache(int maxSizeBytes);
} 