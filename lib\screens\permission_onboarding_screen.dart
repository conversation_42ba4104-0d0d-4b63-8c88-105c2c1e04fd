import 'package:flutter/material.dart';
// import 'package:lottie/lottie.dart';  // Will require adding this package
import '../services/permission_service.dart';
import '../utils/color_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../utils/font_loader.dart';
import 'main_screen.dart';
import 'sign_in_screen.dart';
import '../services/audio_manager.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionOnboardingScreen extends StatefulWidget {
  const PermissionOnboardingScreen({super.key});

  @override
  State<PermissionOnboardingScreen> createState() => _PermissionOnboardingScreenState();
}

class _PermissionOnboardingScreenState extends State<PermissionOnboardingScreen> {
  final PageController _pageController = PageController();
  final PermissionService _permissionService = PermissionService();
  
  int _currentPage = 0;
  bool _isLoading = false;
  
  // Status for each permission
  bool _isStorageGranted = false;
  bool _isNotificationGranted = false;
  bool _isLocationGranted = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentPermissions();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _checkCurrentPermissions() async {
    await _permissionService.init();
    
    if (mounted) {
      setState(() {
        _isStorageGranted = _permissionService.hasStoragePermission;
        _isNotificationGranted = _permissionService.hasNotificationPermission;
        _isLocationGranted = _permissionService.hasLocationPermission;
      });
    }
  }

  Future<void> _requestStoragePermission() async {
    setState(() {
      _isLoading = true;
    });

    debugPrint('Requesting storage permission from onboarding screen');
    
    try {
      bool granted = false;
      
      // Get Android SDK version
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;
      debugPrint('Android SDK version: $sdkVersion');
      
      // For Android 13+ (API 33+), directly request Photos and Videos permissions
      if (sdkVersion >= 33) {
        debugPrint('Requesting Android 13+ specific permissions directly');
        
        // Request photos permission first
        final photosStatus = await Permission.photos.request();
        debugPrint('Photos permission result: $photosStatus');
        
        // Then request videos permission
        final videosStatus = await Permission.videos.request();
        debugPrint('Videos permission result: $videosStatus');
        
        granted = photosStatus.isGranted && videosStatus.isGranted;
      } 
      // For Android 11-12 (API 30-32)
      else if (sdkVersion >= 30) {
        debugPrint('Requesting Android 11-12 specific permissions directly');
        
        // Request storage permission
        final storageStatus = await Permission.storage.request();
        debugPrint('Storage permission result: $storageStatus');
        
        if (!storageStatus.isGranted) {
          // For Android 11+, we might need MANAGE_EXTERNAL_STORAGE
          final manageExternalStatus = await Permission.manageExternalStorage.request();
          debugPrint('Manage external storage permission result: $manageExternalStatus');
          
          granted = storageStatus.isGranted || manageExternalStatus.isGranted;
        } else {
          granted = true;
        }
      } 
      // Below Android 11
      else {
        debugPrint('Requesting legacy storage permission directly');
        final storageStatus = await Permission.storage.request();
        debugPrint('Legacy storage permission result: $storageStatus');
        granted = storageStatus.isGranted;
      }
      
      // Now use the permission service to record the result
      final result = await _permissionService.requestStoragePermission();
      debugPrint('Permission service result: ${result.isGranted}');
      
      if (mounted) {
        setState(() {
          // Use the directly obtained result as it's more accurate
          _isStorageGranted = granted;
          _isLoading = false;
        });
        
        if (_isStorageGranted) {
          _nextPage();
        }
      }
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // Show error dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Permission Error'),
            content: Text('Failed to request storage permission: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _requestNotificationPermission() async {
    setState(() {
      _isLoading = true;
    });

    final result = await _permissionService.requestNotificationPermission();
    
    if (mounted) {
      setState(() {
        _isNotificationGranted = result.isGranted;
        _isLoading = false;
      });
      
      _nextPage(); // Move to next page regardless of permission state
    }
  }

  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoading = true;
    });

    final result = await _permissionService.requestLocationPermission();
    
    if (mounted) {
      setState(() {
        _isLocationGranted = result.isGranted;
        _isLoading = false;
      });
      
      _completeOnboarding();
    }
  }

  void _skipPermission() {
    if (_currentPage < 2) {
      _nextPage();
    } else {
      _completeOnboarding();
    }
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  Future<void> _completeOnboarding() async {
    setState(() {
      _isLoading = true;
    });
    
    // Mark onboarding as completed
    await _permissionService.setOnboardingCompleted();
    
    // Stop any playing bhajan before navigating to sign-in
    await AudioManager().forceStopAndReleasePlayer(AudioType.bhajan);
    
    if (mounted) {
      // Navigate to SignInScreen instead of MainScreen
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const SignInScreen()),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Align(
                alignment: Alignment.topRight,
                child: TextButton(
                  onPressed: _skipPermission,
                  child: Text(
                    'Skip',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
            
            // Page indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                3,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  height: 8,
                  width: _currentPage == index ? 24 : 8,
                  decoration: BoxDecoration(
                    color: _currentPage == index
                        ? AppColors.getPrimaryColor(context)
                        : Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            
            // Main content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  // Storage permission page
                  _buildPermissionPage(
                    title: 'Storage Access',
                    description:
                        'This permission allows Hindu Path to download and save bhajans for offline playback and save your personalizations like wallpapers.',
                    icon: Icons.folder_open,
                    iconBackground: Colors.amber.shade700,
                    isGranted: _isStorageGranted,
                    onRequestPermission: _requestStoragePermission,
                    permissionType: PermissionType.storage,
                  ),
                  
                  // Notification permission page
                  _buildPermissionPage(
                    title: 'Stay Updated',
                    description:
                        'Receive notifications about upcoming Hindu festivals, daily tithi changes, and new content for your spiritual journey.',
                    icon: Icons.notifications_active,
                    iconBackground: Colors.green.shade700,
                    isGranted: _isNotificationGranted,
                    onRequestPermission: _requestNotificationPermission,
                    permissionType: PermissionType.notification,
                  ),
                  
                  // Location permission page
                  _buildPermissionPage(
                    title: 'Accurate Tithi',
                    description:
                        'Your location helps us calculate the correct tithi (lunar day) based on your timezone, ensuring accuracy for your spiritual practices.',
                    icon: Icons.location_on,
                    iconBackground: Colors.blue.shade700,
                    isGranted: _isLocationGranted,
                    onRequestPermission: _requestLocationPermission,
                    permissionType: PermissionType.location,
                    isLastPage: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionPage({
    required String title,
    required String description,
    required IconData icon,
    required Color iconBackground,
    required bool isGranted,
    required Function onRequestPermission,
    required PermissionType permissionType,
    bool isLastPage = false,
  }) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon with background
          Expanded(
            child: Center(
              child: Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: MediaQuery.of(context).size.width * 0.5,
                decoration: BoxDecoration(
                  color: iconBackground.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    icon,
                    size: MediaQuery.of(context).size.width * 0.25,
                    color: iconBackground,
                  ),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Title
          Text(
            title,
            style: AppFontLoader.getPrakrtaStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Description
          Text(
            description,
            style: AppFontLoader.getJosefinStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.8),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // Permission button
          _isLoading
              ? const CircularProgressIndicator()
              : _buildPermissionButton(
                  isGranted: isGranted,
                  onRequestPermission: onRequestPermission,
                  permissionType: permissionType,
                  isLastPage: isLastPage,
                ),
                
          const SizedBox(height: 16),
          
          // "I'll do this later" option
          if (!isGranted && !isLastPage)
            TextButton(
              onPressed: _skipPermission,
              child: Text(
                'I\'ll do this later',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPermissionButton({
    required bool isGranted,
    required Function onRequestPermission,
    required PermissionType permissionType,
    bool isLastPage = false,
  }) {
    if (isGranted) {
      return ElevatedButton.icon(
        onPressed: _nextPage,
        icon: const Icon(Icons.check_circle, color: Colors.white),
        label: Text(
          isLastPage ? 'Complete Setup' : 'Continue',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green.shade700,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          elevation: 5,
        ),
      );
    } else {
      // Define button text based on permission type
      String buttonText;
      IconData iconData;
      
      switch (permissionType) {
        case PermissionType.storage:
          buttonText = 'Grant Storage Access';
          iconData = Icons.folder_open;
          break;
        case PermissionType.notification:
          buttonText = 'Enable Notifications';
          iconData = Icons.notifications;
          break;
        case PermissionType.location:
          buttonText = 'Allow Location';
          iconData = Icons.location_on;
          break;
        default:
          buttonText = 'Grant Permission';
          iconData = Icons.check_circle;
      }
      
      if (isLastPage) {
        buttonText = 'Allow & Complete Setup';
      }
      
      return ElevatedButton.icon(
        onPressed: () => onRequestPermission(),
        icon: Icon(iconData, color: Colors.white),
        label: Text(
          buttonText,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.getPrimaryColor(context),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          elevation: 5,
        ),
      );
    }
  }
} 