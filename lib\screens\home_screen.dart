import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../widgets/mini_player.dart';
import '../widgets/bhajan_list_item.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeAudio();
  }

  Future<void> _initializeAudio() async {
    try {
      final audioProvider = context.read<AudioProvider>();
      // Only load if not already initialized
      if (!audioProvider.isInitialized) {
        await audioProvider.loadBhajans();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
      debugPrint('Error loading bhajans: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Prevent going back to init screen
        return false;
      },
      child: Scaffold(
        backgroundColor:
            const Color(0xFF0D0D0D), // Set background color explicitly
        body: Consumer<AudioProvider>(
          builder: (context, audioProvider, child) {
            if (_hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Color(0xFF1b4332),
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Failed to load bhajans',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                        });
                        _initializeAudio();
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            if (audioProvider.isLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading Bhajans...',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ],
                ),
              );
            }

            if (audioProvider.bhajans.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.music_off,
                      size: 48,
                      color: Color(0xFF0D0D0D),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No bhajans available',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemCount: audioProvider.bhajans.length,
                    itemBuilder: (context, index) {
                      final bhajan = audioProvider.bhajans[index];
                      return BhajanListItem(
                        bhajan: bhajan,
                        onTap: () => audioProvider.playBhajan(index),
                      );
                    },
                  ),
                ),
                const MiniPlayer(),
              ],
            );
          },
        ),
      ),
    );
  }
}
