import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'dart:async';
import 'dart:io';
import '../interfaces/audio_manager_interface.dart';
import '../interfaces/audio_cache_interface.dart';
import '../../di/service_locator.dart';

/// Implementation of the audio manager service
class AudioManagerService implements IAudioManager {
  // Singleton instance
  static final AudioManagerService _instance = AudioManagerService._internal();
  factory AudioManagerService() => _instance;
  
  // The single AudioPlayer instance shared across the app
  AudioPlayer? _player;
  
  // Track which type of audio is currently active
  AudioType _activeAudioType = AudioType.none;
  
  // Stream controllers for notifications
  final _audioTypeController = StreamController<AudioType>.broadcast();
  
  // Dependencies
  late final IAudioCacheService _cacheService;
  
  // Constructor
  AudioManagerService._internal() {
    _cacheService = serviceLocator.get<IAudioCacheService>();
    debugPrint('AudioManagerService: Initialized');
  }
  
  @override
  AudioPlayer? get player => _player;
  
  @override
  AudioType get activeAudioType => _activeAudioType;
  
  @override
  Stream<AudioType> get audioTypeStream => _audioTypeController.stream;
  
  @override
  Future<void> initialize() async {
    debugPrint('AudioManagerService: Initializing');
    if (_player != null) {
      await _player!.dispose();
      _player = null;
    }

    _activeAudioType = AudioType.none;
    _audioTypeController.add(_activeAudioType);
    debugPrint('AudioManagerService: Initialized with no active audio');
  }
  
  @override
  Future<AudioPlayer?> requestPlayer(AudioType requestedType) async {
    debugPrint('AudioManagerService: Requested player for $requestedType');

    // If we already have this type active, return the existing player
    if (_activeAudioType == requestedType && _player != null) {
      debugPrint('AudioManagerService: Returning existing player for $requestedType');
      return _player;
    }

    // Clean up existing player if any
    if (_player != null) {
      debugPrint(
          'AudioManagerService: Disposing existing player for $_activeAudioType');
      await _player!.stop();
      await _player!.dispose();
      _player = null;
    }

    // Create a new player
    try {
      _player = AudioPlayer();
      _activeAudioType = requestedType;
      _audioTypeController.add(_activeAudioType);

      debugPrint('AudioManagerService: Created new player for $requestedType');
      return _player;
    } catch (e) {
      debugPrint('AudioManagerService: Error creating player: $e');
      _activeAudioType = AudioType.none;
      _audioTypeController.add(_activeAudioType);
      return null;
    }
  }
  
  @override
  Future<void> releasePlayer(AudioType type) async {
    debugPrint('AudioManagerService: Releasing player for $type');

    // Only release if this type is currently active
    if (_activeAudioType == type && _player != null) {
      await _player!.stop();
      await _player!.dispose();
      _player = null;
      _activeAudioType = AudioType.none;
      _audioTypeController.add(_activeAudioType);
      debugPrint('AudioManagerService: Player released');
    }
  }
  
  @override
  Future<AudioSource> createAudioSource({
    required String url,
    required String id,
    required String title,
    String? artist,
    Uri? artUri,
    Map<String, dynamic>? extras,
    Map<String, String>? headers,
  }) async {
    // Check if file is already cached
    final cachedFile = await _cacheService.getCachedFile(id);
    
    if (cachedFile != null) {
      debugPrint('AudioManagerService: Using cached file for $title');
      return AudioSource.uri(
        Uri.file(cachedFile.path),
        tag: MediaItem(
          id: id,
          title: title,
          artist: artist ?? 'Hindu Path',
          artUri: artUri,
          extras: extras,
        ),
      );
    }

    // If not cached, use LockCachingAudioSource for automatic caching
    final cacheFile = File('${(await _cacheService.getCacheDirectory()).path}/$id.mp3');
    
    return LockCachingAudioSource(
      Uri.parse(url),
      tag: MediaItem(
        id: id,
        title: title,
        artist: artist ?? 'Hindu Path',
        artUri: artUri,
        extras: extras,
      ),
      headers: headers,
      cacheFile: cacheFile,
    );
  }
  
  @override
  bool isAudioTypeActive(AudioType type) {
    return _activeAudioType == type && _player != null && _player!.playing;
  }
  
  @override
  Future<void> forceStopAndReleasePlayer(AudioType type) async {
    debugPrint('AudioManagerService: Force stopping and releasing player for $type');

    // Only release if this type is currently active
    if (_activeAudioType == type && _player != null) {
      try {
        // First stop playback
        await _player!.stop();

        // Then dispose the player
        await _player!.dispose();

        // Set player to null and reset active type
        _player = null;
        _activeAudioType = AudioType.none;
        _audioTypeController.add(_activeAudioType);

        debugPrint('AudioManagerService: Player forcefully stopped and released');
      } catch (e) {
        debugPrint('AudioManagerService: Error force stopping player: $e');
        // Still reset the state even if there was an error
        _player = null;
        _activeAudioType = AudioType.none;
        _audioTypeController.add(_activeAudioType);
      }
    }
  }
  
  @override
  Future<void> dispose() async {
    if (_player != null) {
      await _player!.dispose();
      _player = null;
    }
    await _audioTypeController.close();
  }
} 