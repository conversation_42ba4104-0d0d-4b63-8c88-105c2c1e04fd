import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/avatar.dart';
import '../services/auth_service.dart';
import '../services/user_preferences_service.dart';

class AvatarProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<Avatar> _avatars = [];
  Set<String> _favoriteAvatars = {};
  bool _isLoading = false;
  String? _error;
  final AuthService _authService = AuthService();
  final UserPreferencesService _preferencesService = UserPreferencesService();

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<Avatar> get avatars => _avatars;
  Set<String> get favoriteAvatars => _favoriteAvatars;

  AvatarProvider() {
    _loadFavorites();
    loadAvatars();
  }

  // Load favorites from Firebase if user is authenticated
  Future<void> _loadFavorites() async {
    try {
      final user = _authService.currentUser;
      // Only load favorites if a user is authenticated
      if (user != null) {
        final favorites =
            await _preferencesService.getFavoriteAvatars(user.uid);
        _favoriteAvatars = Set<String>.from(favorites);
        notifyListeners();
      } else {
        // Clear favorites when no user is authenticated
        _favoriteAvatars = {};
        notifyListeners();
      }
    } catch (e) {
      print('Error loading avatar favorites: $e');
    }
  }

  // Toggle favorite status and save to Firebase for authenticated users
  Future<void> toggleFavorite(String avatarId) async {
    final user = _authService.currentUser;
    // Return early if not authenticated
    if (user == null) {
      print('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      // Optimistic update - immediately update UI without waiting for server
      final wasAlreadyFavorite = _favoriteAvatars.contains(avatarId);

      // Toggle the state locally first
      if (wasAlreadyFavorite) {
        _favoriteAvatars.remove(avatarId);
      } else {
        _favoriteAvatars.add(avatarId);
      }

      // Notify listeners immediately for responsive UI
      notifyListeners();

      // Then perform the server operation without blocking UI
      if (wasAlreadyFavorite) {
        // Run in microtask to not block UI thread
        Future.microtask(() async {
          try {
            await _preferencesService.removeFavoriteAvatar(user.uid, avatarId);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            print('Error removing avatar from favorites: $e');
            _favoriteAvatars.add(avatarId);
            notifyListeners();
          }
        });
      } else {
        Future.microtask(() async {
          try {
            await _preferencesService.addFavoriteAvatar(user.uid, avatarId);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            print('Error adding avatar to favorites: $e');
            _favoriteAvatars.remove(avatarId);
            notifyListeners();
          }
        });
      }
    } catch (e) {
      print('Error toggling avatar favorite: $e');
    }
  }

  // Check if avatar is in favorites
  bool isFavorite(String avatarId) {
    // Check authentication first
    if (_authService.currentUser == null) return false;
    return _favoriteAvatars.contains(avatarId);
  }

  // Get all favorite avatars
  List<Avatar> getFavoriteAvatars() {
    // Check if user is authenticated
    if (_authService.currentUser == null) {
      return []; // Return empty list if not authenticated
    }
    return _avatars
        .where((avatar) => _favoriteAvatars.contains(avatar.id))
        .toList();
  }

  // Clear favorites when user logs out
  void clearFavorites() {
    _favoriteAvatars = {};
    notifyListeners();
  }

  // Load favorites after user signs in
  Future<void> loadFavorites() async {
    // Check authentication before loading
    if (_authService.currentUser == null) {
      // Clear favorites if no user is authenticated
      _favoriteAvatars = {};
      notifyListeners();
      return;
    }
    await _loadFavorites();
  }

  // Rest of implementation...
  Future<void> loadAvatars() async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final QuerySnapshot snapshot = await _firestore
          .collection('avatars')
          .orderBy('uploadDate', descending: true)
          .get();

      _avatars = snapshot.docs
          .map((doc) => Avatar.fromFirestore(doc))
          .map((avatar) => Avatar.withFixedUrl(avatar))
          .toList();

      print('Loaded ${_avatars.length} avatars');
      if (_avatars.isNotEmpty) {
        print('First avatar URL: ${_avatars.first.url}');
      }

      // Reload favorites after loading avatars
      await _loadFavorites();

      _error = null;
    } catch (e) {
      print('Error loading avatars: $e');
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> retry() async {
    _error = null;
    notifyListeners();
    await loadAvatars();
  }
}
