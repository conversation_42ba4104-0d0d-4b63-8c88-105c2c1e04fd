import 'package:get_it/get_it.dart';
import 'package:flutter/foundation.dart';
import '../services/interfaces/audio_player_interface.dart';
import '../services/interfaces/audio_manager_interface.dart';
import '../services/interfaces/bhajan_repository_interface.dart';
import '../services/interfaces/audio_cache_interface.dart';
import '../services/implementations/audio_player_service.dart';
import '../services/implementations/audio_manager_service.dart';
import '../services/implementations/bhajan_repository.dart';
import '../services/implementations/audio_cache_service.dart';

/// Service locator singleton for dependency injection
class ServiceLocator {
  // Private constructor
  ServiceLocator._();
  
  // Singleton instance
  static final ServiceLocator _instance = ServiceLocator._();
  
  // Getter for the instance
  static ServiceLocator get instance => _instance;
  
  // GetIt instance
  final GetIt _getIt = GetIt.instance;
  
  /// Initialize the service locator and register all services
  Future<void> initialize() async {
    debugPrint('ServiceLocator: Initializing services');
    
    // Register services in order of dependencies
    // First the services that don't depend on others
    _getIt.registerSingleton<IAudioCacheService>(AudioCacheService());
    debugPrint('ServiceLocator: Registered AudioCacheService');
    
    _getIt.registerSingleton<IAudioManager>(AudioManagerService());
    debugPrint('ServiceLocator: Registered AudioManagerService');
    
    _getIt.registerSingleton<IBhajanRepository>(BhajanRepository());
    debugPrint('ServiceLocator: Registered BhajanRepository');
    
    // Then register services that depend on other services
    _getIt.registerSingleton<IAudioPlayerService>(
      AudioPlayerService(
        audioManager: _getIt<IAudioManager>(),
        bhajanRepository: _getIt<IBhajanRepository>(),
        cacheService: _getIt<IAudioCacheService>(),
      ),
    );
    debugPrint('ServiceLocator: Registered AudioPlayerService');
    
    debugPrint('ServiceLocator: All services registered successfully');
  }
  
  /// Register a service with the service locator
  void register<T extends Object>(T service) {
    if (!isRegistered<T>()) {
      _getIt.registerSingleton<T>(service);
    }
  }
  
  /// Get a service from the service locator
  T get<T extends Object>() {
    return _getIt<T>();
  }
  
  /// Check if a service is registered with the service locator
  bool isRegistered<T extends Object>() {
    return _getIt.isRegistered<T>();
  }
  
  /// Reset the service locator (for testing)
  void reset() {
    _getIt.reset();
    debugPrint('ServiceLocator: All services cleared');
  }
}

// Global instance for easy access
final serviceLocator = ServiceLocator.instance; 