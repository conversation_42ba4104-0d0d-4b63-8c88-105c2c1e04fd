import 'package:flutter/material.dart';
import '../utils/color_utils.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

/// A themed button that uses the current theme's primary color
class ThemedButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final double? width;
  final double? height;
  final double borderRadius;
  final bool isOutlined;
  final EdgeInsetsGeometry? padding;
  final Widget? icon;

  const ThemedButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.width,
    this.height = 45,
    this.borderRadius = 8.0,
    this.isOutlined = false,
    this.padding,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = isOutlined
        ? OutlinedButton.styleFrom(
            side: BorderSide(color: AppColors.getPrimaryColor(context)),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          )
        : AppColors.getPrimaryButtonStyle(
            context,
            minimumSize: width != null ? Size(width!, height!) : null,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          );

    // Create either a standard button or an icon button
    if (icon != null) {
      return isOutlined
          ? OutlinedButton.icon(
              onPressed: onPressed,
              style: buttonStyle,
              icon: icon!,
              label: Text(text),
            )
          : ElevatedButton.icon(
              onPressed: onPressed,
              style: buttonStyle,
              icon: icon!,
              label: Text(text),
            );
    } else {
      return isOutlined
          ? OutlinedButton(
              onPressed: onPressed,
              style: buttonStyle,
              child: Text(text),
            )
          : ElevatedButton(
              onPressed: onPressed,
              style: buttonStyle,
              child: Text(text),
            );
    }
  }
}

/// A themed container with border using the primary color
class ThemedBorderContainer extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final double borderWidth;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final Color? backgroundColor;

  const ThemedBorderContainer({
    super.key,
    required this.child,
    this.borderRadius = 8.0,
    this.borderWidth = 1.0,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = EdgeInsets.zero,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: AppColors.getPrimaryColor(context),
          width: borderWidth,
        ),
      ),
      child: child,
    );
  }
}

/// A tab indicator that uses the primary color
class ThemedTabIndicator extends Decoration {
  final double radius;
  final double indicatorHeight;
  final EdgeInsetsGeometry insets;
  final BuildContext context;

  const ThemedTabIndicator({
    required this.context,
    this.radius = 2,
    this.indicatorHeight = 3,
    this.insets = const EdgeInsets.symmetric(horizontal: 12),
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ThemedPainter(
      this,
      onChanged,
      radius,
      indicatorHeight,
      insets,
      context,
    );
  }
}

class _ThemedPainter extends BoxPainter {
  final ThemedTabIndicator decoration;
  final double radius;
  final double indicatorHeight;
  final EdgeInsetsGeometry insets;
  final BuildContext context;

  _ThemedPainter(
    this.decoration,
    VoidCallback? onChanged,
    this.radius,
    this.indicatorHeight,
    this.insets,
    this.context,
  ) : super(onChanged);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration.size != null);

    final rect = offset & configuration.size!;
    final insetRect = insets.resolve(TextDirection.ltr).deflateRect(rect);

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = ThemeProvider.currentGreen
      ..strokeWidth = indicatorHeight;

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          insetRect.left,
          insetRect.bottom - indicatorHeight,
          insetRect.width,
          indicatorHeight,
        ),
        Radius.circular(radius),
      ),
      paint,
    );
  }
}
