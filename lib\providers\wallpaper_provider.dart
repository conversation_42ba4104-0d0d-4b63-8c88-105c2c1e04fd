import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/wallpaper.dart';
import 'package:firebase_core/firebase_core.dart';
import '../services/auth_service.dart';
import '../services/user_preferences_service.dart';

class WallpaperProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<Wallpaper> _wallpapers = [];
  List<String> _favoriteWallpapers = [];
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;
  final AuthService _authService = AuthService();
  final UserPreferencesService _preferencesService = UserPreferencesService();

  // Pagination variables
  DocumentSnapshot? _lastDocument;
  static const int _pageSize = 20; // Number of wallpapers to load at once
  bool _hasMoreWallpapers = true;

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<Wallpaper> get wallpapers => _wallpapers;
  bool get isInitialized => _isInitialized;
  List<String> get favoriteWallpapers => _favoriteWallpapers;
  bool get hasMoreWallpapers => _hasMoreWallpapers;

  // Get favorite wallpapers as Wallpaper objects
  List<Wallpaper> get favoriteWallpaperData {
    // Check if user is authenticated
    if (_authService.currentUser == null) {
      return []; // Return empty list if not authenticated
    }
    return _wallpapers
        .where((wp) => _favoriteWallpapers.contains(wp.id))
        .toList();
  }

  WallpaperProvider() {
    // Initialize wallpapers when provider is created
    _initializeFirestore();
    _loadFavoriteWallpapers();
  }

  Future<void> _loadFavoriteWallpapers() async {
    final user = _authService.currentUser;
    // Only load favorites if a user is authenticated
    if (user != null) {
      try {
        _favoriteWallpapers =
            await _preferencesService.getFavoriteWallpapers(user.uid);
        notifyListeners();
      } catch (e) {
        print('Error loading favorite wallpapers: $e');
      }
    } else {
      // Clear favorites when no user is authenticated
      _favoriteWallpapers = [];
      notifyListeners();
    }
  }

  Future<void> _initializeFirestore() async {
    try {
      // Check if Firebase is initialized
      if (!Firebase.apps.isNotEmpty) {
        throw Exception('Firebase not initialized');
      }

      await loadWallpapers();
    } catch (e) {
      _error = 'Failed to initialize Firestore: $e';
      notifyListeners();
    }
  }

  // Get wallpapers by category
  List<Wallpaper> getWallpapersByCategory(String category) {
    return _wallpapers.where((w) => w.category == category).toList();
  }

  // Load wallpapers for a specific category
  Future<void> loadWallpapersByCategory(String category) async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Create query for specific category without limit to get all wallpapers
      final QuerySnapshot snapshot = await _firestore
          .collection('wallpapers')
          .where('category', isEqualTo: category)
          .orderBy('uploadDate', descending: true)
          .get();

      // Process results
      if (snapshot.docs.isNotEmpty) {
        // Convert documents to Wallpaper objects
        final categoryWallpapers =
            snapshot.docs.map((doc) => Wallpaper.fromFirestore(doc)).toList();

        // Create a set of existing wallpaper IDs for efficient lookup
        final existingIds = Set<String>.from(_wallpapers.map((w) => w.id));
        
        // Create a list of new wallpapers that aren't already in the collection
        final newWallpapers = categoryWallpapers.where((w) => !existingIds.contains(w.id)).toList();
        
        // Add only the new wallpapers
        _wallpapers.addAll(newWallpapers);
        
        // Print stats for debugging
        print('Added ${newWallpapers.length} new wallpapers for category $category');
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('Error loading $category wallpapers: $e');
      _error = 'Failed to load $category wallpapers: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get newly added wallpapers (latest 50)
  List<Wallpaper> getNewlyAddedWallpapers() {
    // Sort wallpapers by upload date in descending order
    final sortedWallpapers = List<Wallpaper>.from(_wallpapers)
      ..sort((a, b) => b.uploadDate.compareTo(a.uploadDate));

    // Return only the latest 50 wallpapers
    return sortedWallpapers.take(50).toList();
  }

  // Load all wallpapers
  Future<void> loadWallpapers({bool refresh = false}) async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      _error = null;

      // If refreshing, clear existing data
      if (refresh) {
        _wallpapers = [];
        _lastDocument = null;
        _hasMoreWallpapers = true;
      }

      notifyListeners();

      // Create base query with larger limit and order by upload date
      Query query = _firestore
          .collection('wallpapers')
          .orderBy('uploadDate', descending: true)
          .limit(100); // Increased limit to 100 wallpapers

      // If we have a last document and we're not refreshing, start after it
      if (_lastDocument != null && !refresh) {
        query = query.startAfterDocument(_lastDocument!);
      }

      // Execute query
      final QuerySnapshot snapshot = await query.get();

      // Process results
      if (snapshot.docs.isNotEmpty) {
        // Save the last document for pagination
        _lastDocument = snapshot.docs.last;
        
        // Convert documents to Wallpaper objects
        final loadedWallpapers =
            snapshot.docs.map((doc) => Wallpaper.fromFirestore(doc)).toList();
            
        // Create a set of existing wallpaper IDs for efficient lookup
        final existingIds = Set<String>.from(_wallpapers.map((w) => w.id));
        
        // Filter out any duplicates
        final newWallpapers = loadedWallpapers.where((w) => !existingIds.contains(w.id)).toList();

        // Update the wallpapers list
        _wallpapers.addAll(newWallpapers);

        // Sort by upload date
        _wallpapers.sort((a, b) => b.uploadDate.compareTo(a.uploadDate));

        // Check if we have more wallpapers to load
        _hasMoreWallpapers = snapshot.docs.length >= 100;
        
        print('Loaded ${newWallpapers.length} new wallpapers, total: ${_wallpapers.length}');
      } else {
        _hasMoreWallpapers = false;
      }

      _isInitialized = true;
      _isLoading = false;

      // Reload favorite wallpapers after loading all wallpapers
      await _loadFavoriteWallpapers();

      notifyListeners();
    } catch (e) {
      print('Error loading wallpapers: $e');
      _error = 'Failed to load wallpapers: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load more wallpapers (pagination)
  Future<void> loadMoreWallpapers() async {
    // Skip if already loading or no more wallpapers to load
    if (_isLoading || !_hasMoreWallpapers) return;
    
    try {
      _isLoading = true;
      notifyListeners();
      
      // Create query with pagination
      Query query = _firestore
          .collection('wallpapers')
          .orderBy('uploadDate', descending: true)
          .limit(_pageSize);
          
      // Start after the last document if we have one
      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }
      
      // Execute query
      final QuerySnapshot snapshot = await query.get();
      
      // Process results
      if (snapshot.docs.isNotEmpty) {
        // Save the last document for pagination
        _lastDocument = snapshot.docs.last;
        
        // Convert documents to Wallpaper objects
        final loadedWallpapers = 
            snapshot.docs.map((doc) => Wallpaper.fromFirestore(doc)).toList();
            
        // Create a set of existing wallpaper IDs for efficient lookup
        final existingIds = Set<String>.from(_wallpapers.map((w) => w.id));
        
        // Filter out any duplicates
        final newWallpapers = loadedWallpapers.where((w) => !existingIds.contains(w.id)).toList();
        
        // Add to wallpapers list
        _wallpapers.addAll(newWallpapers);
        
        // Sort by upload date
        _wallpapers.sort((a, b) => b.uploadDate.compareTo(a.uploadDate));
        
        // Check if we have more wallpapers to load
        _hasMoreWallpapers = snapshot.docs.length >= _pageSize;
        
        print('Loaded ${newWallpapers.length} more wallpapers, total: ${_wallpapers.length}');
      } else {
        _hasMoreWallpapers = false;
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('Error loading more wallpapers: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh wallpapers (clear and reload)
  Future<void> refreshWallpapers() async {
    try {
      // First, load the main wallpapers with refresh=true to clear existing data
      await loadWallpapers(refresh: true);
      
      // Track the categories we've already loaded
      Set<String> loadedCategories = <String>{};
      
      // Create a copy of the wallpapers list to avoid concurrent modification
      final List<Wallpaper> wallpapersCopy = List<Wallpaper>.from(_wallpapers);
      
      // Find distinct categories in the current wallpapers to reload them
      for (var wallpaper in wallpapersCopy) {
        if (!loadedCategories.contains(wallpaper.category)) {
          loadedCategories.add(wallpaper.category);
          // Reload this category
          await loadWallpapersByCategory(wallpaper.category);
        }
      }
      
      // Load other common categories that might not be in the current set
      List<String> commonCategories = [
        'Lord Krishna',
        'Lord Shiva',
        'Lord Hanuman',
        'Lord Ram',
        'Lord Ganesha',
        'Lord Vishnu',
        'Shri Durga',
        'Shri Lakshmi',
        'Shri Saraswati',
      ];
      
      for (var category in commonCategories) {
        if (!loadedCategories.contains(category)) {
          await loadWallpapersByCategory(category);
        }
      }
      
      notifyListeners();
    } catch (e) {
      print('Error refreshing wallpapers: $e');
      // Notify listeners even in case of error
      notifyListeners();
    }
  }

  // Retry loading wallpapers
  Future<void> retry() async {
    _error = null;
    notifyListeners();
    await _initializeFirestore();
  }

  // Check if a wallpaper is favorited
  bool isWallpaperFavorite(String wallpaperId) {
    // Check authentication first
    if (_authService.currentUser == null) return false;
    return _favoriteWallpapers.contains(wallpaperId);
  }

  // Toggle favorite status and save to Firebase for authenticated users
  Future<void> toggleFavorite(String wallpaperId) async {
    final user = _authService.currentUser;
    // Return early if not authenticated
    if (user == null) {
      debugPrint('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      // Optimistic update - immediately update UI without waiting for server
      final wasAlreadyFavorite = _favoriteWallpapers.contains(wallpaperId);

      // Toggle the state locally first
      if (wasAlreadyFavorite) {
        _favoriteWallpapers.remove(wallpaperId);
      } else {
        _favoriteWallpapers.add(wallpaperId);
      }

      // Notify listeners immediately for responsive UI
      notifyListeners();

      // Then perform the server operation without blocking UI
      if (wasAlreadyFavorite) {
        // Run in microtask to not block UI thread
        Future.microtask(() async {
          try {
            await _preferencesService.removeFavoriteWallpaper(
                user.uid, wallpaperId);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            debugPrint('Error removing wallpaper from favorites: $e');
            _favoriteWallpapers.add(wallpaperId);
            notifyListeners();
          }
        });
      } else {
        Future.microtask(() async {
          try {
            await _preferencesService.addFavoriteWallpaper(
                user.uid, wallpaperId);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            debugPrint('Error adding wallpaper to favorites: $e');
            _favoriteWallpapers.remove(wallpaperId);
            notifyListeners();
          }
        });
      }
    } catch (e) {
      debugPrint('Error toggling wallpaper favorite: $e');
    }
  }

  // Clear favorites when user logs out
  void clearFavorites() {
    _favoriteWallpapers = [];
    notifyListeners();
  }

  // Load favorites after user signs in
  Future<void> loadFavorites() async {
    // Check authentication before loading
    if (_authService.currentUser == null) {
      // Clear favorites if no user is authenticated
      _favoriteWallpapers = [];
      notifyListeners();
      return;
    }
    await _loadFavoriteWallpapers();
  }
}
