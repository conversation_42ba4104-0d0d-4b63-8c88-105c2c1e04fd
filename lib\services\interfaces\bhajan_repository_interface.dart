import 'package:flutter/foundation.dart';
import '../../models/bhajan.dart';

/// Interface defining the bhajan repository functionality
abstract class IBhajanRepository {
  /// Get all bhajans
  Future<List<Bhajan>> getAllBhajans();
  
  /// Get a page of bhajans with pagination
  Future<List<Bhajan>> getBhajanPage(int pageSize, {FirestoreDocumentSnapshot? lastDocument});
  
  /// Get a specific bhajan by ID
  Future<Bhajan?> getBhajanById(String id);
  
  /// Get favorite bhajans for the current user
  Future<List<Bhajan>> getFavoriteBhajans();
  
  /// Add a bhajan to favorites
  Future<void> addToFavorites(String bhajanId);
  
  /// Remove a bhajan from favorites
  Future<void> removeFromFavorites(String bhajanId);
  
  /// Check if a bhajan is a favorite
  Future<bool> isFavorite(String bhajanId);
  
  /// Save listening progress for a bhajan
  Future<void> saveBhajanProgress(String bhajanId, double progress);
  
  /// Get saved listening progress for a bhajan
  Future<double?> getBhajanProgress(String bhajanId);
  
  /// Get bhajans by search query
  Future<List<Bhajan>> searchBhajans(String query);
}

// This is a placeholder for FirebaseFirestore's DocumentSnapshot
// In your actual implementation, you'd import the real type
class FirestoreDocumentSnapshot {
  final String id;
  FirestoreDocumentSnapshot(this.id);
} 