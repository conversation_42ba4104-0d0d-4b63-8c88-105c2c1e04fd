import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/color_utils.dart';
import '../utils/theme_widgets.dart';
import '../providers/name_provider.dart';
import '../providers/wallpaper_provider.dart';
import '../models/name_data.dart';
import '../models/wallpaper.dart';
import '../screens/wallpaper_viewer_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/auth_provider.dart';
import '../services/auth_service.dart';
import '../utils/ui_helpers.dart';
import '../utils/font_loader.dart';

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load favorites when the page is first displayed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFavorites();
    });
  }

  Future<void> _loadFavorites() async {
    try {
      final nameProvider = Provider.of<NameProvider>(context, listen: false);
      final wallpaperProvider =
          Provider.of<WallpaperProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Only load favorites if user is signed in (not in guest mode)
      if (authProvider.isSignedIn && !authProvider.isGuest) {
        debugPrint('Loading favorites for authenticated user in FavoritesPage');
        // Reload favorites for both providers
        await nameProvider.loadFavorites();
        await wallpaperProvider.loadFavorites();
      } else {
        debugPrint(
            'User not authenticated or in guest mode - clearing favorites in FavoritesPage');
        // Clear favorites if user is not authenticated or in guest mode
        nameProvider.clearFavorites();
        wallpaperProvider.clearFavorites();
      }
    } catch (e) {
      debugPrint('Error handling favorites in FavoritesPage: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      appBar: AppBar(
        title: const Text('Favorites'),
        backgroundColor: const Color(0xFF0D0D0D),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 14,
          ),
          indicator: ThemedTabIndicator(
            context: context,
            radius: 4,
            indicatorHeight: 3,
            insets: const EdgeInsets.symmetric(horizontal: 20),
          ),
          tabs: const [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Wallpapers'),
                  SizedBox(width: 8),
                  Icon(Icons.wallpaper, size: 20),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Names'),
                  SizedBox(width: 8),
                  Icon(Icons.person, size: 20),
                ],
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Wallpapers Tab
          _buildWallpapersTab(),
          // Names Tab
          _buildNamesTab(),
        ],
      ),
    );
  }

  Widget _buildWallpapersTab() {
    return Consumer<WallpaperProvider>(
      builder: (context, wallpaperProvider, child) {
        final favoriteWallpapers = wallpaperProvider.favoriteWallpaperData;
        final authProvider = Provider.of<AuthProvider>(context);

        // Show sign in message for guest users
        if (authProvider.isGuest || _authService.currentUser == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.wallpaper,
                  size: 64,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Please Sign In to view your Wallpapers',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                    fontFamily: 'JosefinSans',
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.getPrimaryColor(context),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text(
                    'Sign In',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        if (favoriteWallpapers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.wallpaper,
                  size: 64,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No favorite wallpapers yet',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 16,
                    fontFamily: 'Metropolis',
                  ),
                ),
              ],
            ),
          );
        }

        return GridView.builder(
          padding: const EdgeInsets.all(12),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 0.7,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: favoriteWallpapers.length,
          itemBuilder: (context, index) {
            final wallpaper = favoriteWallpapers[index];
            return GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WallpaperViewerScreen(
                      wallpapers: favoriteWallpapers,
                      initialIndex: index,
                    ),
                  ),
                );
              },
              child: Stack(
                children: [
                  // Wallpaper image
                  Hero(
                    tag: 'wallpaper-${wallpaper.id}',
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: CachedNetworkImage(
                        imageUrl: wallpaper.url,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        placeholder: (context, url) => Container(
                          color: const Color(0xFF222222),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: const Color(0xFF222222),
                          child: const Icon(Icons.error, color: Colors.white70),
                        ),
                      ),
                    ),
                  ),
                  // Favorite button
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.favorite,
                          color: Colors.red,
                          size: 20,
                        ),
                        onPressed: () {
                          wallpaperProvider.toggleFavorite(wallpaper.id);
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildNamesTab() {
    return Consumer<NameProvider>(
      builder: (context, nameProvider, child) {
        final favoriteNames = nameProvider.favoriteNameData;
        final authProvider = Provider.of<AuthProvider>(context);

        // Show sign in message for guest users
        if (authProvider.isGuest || _authService.currentUser == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person,
                  size: 64,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Please Sign In to view your Names',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                    fontFamily: 'JosefinSans',
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.getPrimaryColor(context),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text(
                    'Sign In',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        if (favoriteNames.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person,
                  size: 64,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No favorite names yet',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 16,
                    fontFamily: 'Metropolis',
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: favoriteNames.length,
          itemBuilder: (context, index) {
            final name = favoriteNames[index];
            return ListTile(
              title: Text(
                name.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              subtitle: Text(
                name.meaning != 'nan' ? name.meaning : '',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              trailing: IconButton(
                icon: const Icon(
                  Icons.favorite,
                  color: Colors.red,
                ),
                onPressed: () {
                  nameProvider.toggleFavorite(name.name);
                },
              ),
              onTap: () {
                // Show name details in a dialog
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    backgroundColor: const Color(0xFF0D0D0D),
                    title: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          name.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                        IconButton(
                          icon: const Icon(
                            Icons.favorite,
                            color: Colors.red,
                          ),
                          onPressed: () {
                            Navigator.pop(context);
                            nameProvider.toggleFavorite(name.name);
                          },
                        ),
                      ],
                    ),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (name.nameInDevanagari.isNotEmpty) ...[
                          Text(
                            'देवनागरी: ${name.nameInDevanagari}',
                            style: const TextStyle(color: Colors.white),
                          ),
                          const SizedBox(height: 8),
                        ],
                        if (name.meaning != 'nan') ...[
                          Text(
                            'Meaning: ${name.meaning}',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Close'),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}
