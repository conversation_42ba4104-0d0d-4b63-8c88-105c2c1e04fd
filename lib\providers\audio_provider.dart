import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/bhajan.dart';
import '../services/audio_service.dart';
import '../services/firebase_service.dart';
import '../services/audio_manager.dart' show AudioManager, AudioType;
import '../utils/ui_helpers.dart';
import '../utils/navigation_service.dart';
import 'package:provider/provider.dart';
import '../providers/ad_provider.dart';

class AudioProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AudioPlayerService _audioService = AudioPlayerService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<Bhajan> _bhajans = [];
  int _currentIndex = 0;
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isInitialized = false;
  bool _isRefreshing = false;
  String? _error;
  bool _isAuthenticated = false;
  bool _isMiniPlayerDismissed = false;

  // Debug flag to track mini player visibility problems
  bool _debugMiniPlayer = true;

  // Pagination variables
  static const int _pageSize = 15;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // Track whether user explicitly dismissed the mini player
  bool _userExplicitlyDismissed = false;

  // Getters for pagination
  bool get hasMoreData => _hasMoreData;
  bool get isLoadingMore => _isLoadingMore;

  Future<void> _initializeFirebase() async {
    try {
      // Only attempt to sign in anonymously, Firebase is already initialized in main.dart
      final userCredential = await FirebaseService.signInAnonymously();
      _isAuthenticated = userCredential != null;
    } catch (e) {
      debugPrint('Error initializing Firebase auth: $e');
      _isAuthenticated = false;
      // Don't throw the error, just log it and continue
    }
  }

  Future<void> _saveProgress(String bhajanId, double progress) async {
    if (_isAuthenticated) {
      await FirebaseService.saveBhajanProgress(bhajanId, progress);
    }
  }

  Future<double?> _loadProgress(String bhajanId) async {
    if (_isAuthenticated) {
      final progress = await FirebaseService.getBhajanProgress(bhajanId);
      return progress?['progress'] as double?;
    }
    return null;
  }

  // Remote URL for bhajans metadata
  static const String _bhajansUrl =
      'https://cdn.hindupath.online/bhajan/bhajans_metadata.json';

  bool get isLoading => _isLoading;
  bool get isRefreshing => _isRefreshing;
  bool get isInitialized => _isInitialized;
  String? get error => _error;
  AudioPlayerService get audioService => _audioService;
  List<Bhajan> get bhajans => _bhajans;
  bool get isMiniPlayerDismissed => _isMiniPlayerDismissed;
  bool get userExplicitlyDismissed => _userExplicitlyDismissed;
  Bhajan? get currentBhajan =>
      _bhajans.isNotEmpty ? _bhajans[_currentIndex] : null;
  bool get isPlaying => _isPlaying;
  User? get currentUser => _auth.currentUser;

  AudioProvider() {
    // Listen to player state changes to notify listeners and save progress
    _audioService.playerStateStream.listen((_) async {
      notifyListeners();
      if (_audioService.currentBhajan != null) {
        final position = await _audioService.player?.position;
        final duration = _audioService.player?.duration;
        if (duration != null && position != null) {
          final progress = position.inMilliseconds / duration.inMilliseconds;
          await _saveProgress(_audioService.currentBhajan!.id, progress);
        }
      }
    });

    // Initialize Firebase
    _initializeFirebase();
  }

  Future<void> loadBhajans() async {
    if (_isInitialized) {
      debugPrint('AudioProvider: Already initialized, skipping load');
      return;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      debugPrint(
          'AudioProvider: Starting to load all bhajans from Firestore...');

      // Initialize Firebase first
      await _initializeFirebase();
      debugPrint('AudioProvider: Firebase initialized');

      // Test Firestore connection
      try {
        final testQuery = await _firestore.collection('bhajans').limit(1).get();
        debugPrint(
            'AudioProvider: Successfully connected to Firestore. Found ${testQuery.docs.length} test document(s)');
      } catch (e) {
        debugPrint('AudioProvider: Failed to connect to Firestore: $e');
        throw Exception(
            'Failed to connect to Firestore. Please check your internet connection and try again.');
      }

      // Load ALL bhajans at once instead of using pagination for initial load
      try {
        debugPrint('AudioProvider: Loading all bhajans at once...');

        // Get all bhajans without pagination
        final querySnapshot = await _firestore
            .collection('bhajans')
            .orderBy('uploadDate', descending: true)
            .get();

        debugPrint(
            'AudioProvider: Retrieved ${querySnapshot.docs.length} total bhajans');

        // Process all bhajans
        List<String> processingErrors = [];
        for (var doc in querySnapshot.docs) {
          try {
            final bhajan = Bhajan.fromFirestore(doc);

            // Validate the bhajan URLs
            if (!Uri.parse(bhajan.r2Url).isAbsolute) {
              throw Exception('Invalid audio URL format');
            }
            if (!Uri.parse(bhajan.artworkUrl).isAbsolute) {
              throw Exception('Invalid artwork URL format');
            }

            _bhajans.add(bhajan);
          } catch (e) {
            debugPrint(
                'AudioProvider: Error processing bhajan document ${doc.id}: $e');
            processingErrors.add('Error processing bhajan ${doc.id}: $e');
            continue;
          }
        }

        // Set pagination variables for future loads
        _hasMoreData = false; // All data loaded already

        if (processingErrors.isNotEmpty) {
          debugPrint(
              'AudioProvider: Some bhajans failed to process: ${processingErrors.length} errors');
        }
      } catch (e) {
        debugPrint('AudioProvider: Error loading all bhajans: $e');
        throw Exception(
            'Failed to load bhajans. Please check your connection and try again.');
      }

      // Initialize audio service with the loaded bhajans
      try {
        debugPrint('AudioProvider: Initializing audio service...');
        await _audioService.init(_bhajans);
        debugPrint('AudioProvider: Audio service initialized with bhajans');
      } catch (e) {
        debugPrint('AudioProvider: Error initializing audio service: $e');
        throw Exception(
            'Failed to initialize audio player. Please check your internet connection and try again.');
      }

      _isInitialized = true;
      _isLoading = false;
      _error = null;
      notifyListeners();
      debugPrint(
          'AudioProvider: Successfully completed initialization with ${_bhajans.length} bhajans');
    } catch (e) {
      debugPrint('AudioProvider: Error during initialization: $e');
      _error = e.toString();
      _isLoading = false;
      _isInitialized = false;
      notifyListeners();
      throw e; // Re-throw to let callers handle the error
    }
  }

  // New method to load a page of bhajans
  Future<void> _loadBhajanPage() async {
    if (!_hasMoreData || _isLoadingMore) return;

    try {
      _isLoadingMore = true;
      notifyListeners();

      // Create query with pagination
      Query query = _firestore
          .collection('bhajans')
          .orderBy('uploadDate', descending: true)
          .limit(_pageSize);

      // If we have a last document from previous query, start after it
      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }

      // Execute query
      final QuerySnapshot snapshot = await query.get();
      debugPrint(
          'AudioProvider: Retrieved ${snapshot.docs.length} bhajans for this page');

      // Check if we've reached the end
      if (snapshot.docs.length < _pageSize) {
        _hasMoreData = false;
      }

      // Update last document for next pagination query
      if (snapshot.docs.isNotEmpty) {
        _lastDocument = snapshot.docs.last;
      }

      // Process bhajans
      List<String> processingErrors = [];
      for (var doc in snapshot.docs) {
        try {
          debugPrint('AudioProvider: Processing bhajan document ${doc.id}');
          final bhajan = Bhajan.fromFirestore(doc);

          // Validate the bhajan URLs
          if (!Uri.parse(bhajan.r2Url).isAbsolute) {
            throw Exception('Invalid audio URL format');
          }
          if (!Uri.parse(bhajan.artworkUrl).isAbsolute) {
            throw Exception('Invalid artwork URL format');
          }

          _bhajans.add(bhajan);
        } catch (e) {
          debugPrint(
              'AudioProvider: Error processing bhajan document ${doc.id}: $e');
          processingErrors.add('Error processing bhajan ${doc.id}: $e');
          continue;
        }
      }

      if (processingErrors.isNotEmpty) {
        debugPrint(
            'AudioProvider: Some bhajans failed to process: ${processingErrors.length} errors');
      }

      _isLoadingMore = false;
      notifyListeners();
    } catch (e) {
      debugPrint('AudioProvider: Error loading bhajan page: $e');
      _isLoadingMore = false;
      notifyListeners();
      throw e;
    }
  }

  // New method to load more bhajans when user reaches end of list
  Future<void> loadMoreBhajans() async {
    if (!_isInitialized || !_hasMoreData || _isLoadingMore) return;

    try {
      // Remember mini player state before loading more content
      final wasMiniPlayerDismissed = _isMiniPlayerDismissed;

      await _loadBhajanPage();

      // Update audio service with new bhajans but preserve mini player state
      if (_bhajans.isNotEmpty) {
        await _audioService.updatePlaylist(_bhajans,
            emitCurrentBhajan: !wasMiniPlayerDismissed);

        // Restore mini player state
        _isMiniPlayerDismissed = wasMiniPlayerDismissed;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('AudioProvider: Error loading more bhajans: $e');
      // Don't rethrow here to avoid crashing the UI
    }
  }

  Future<bool> refreshBhajans() async {
    if (_isRefreshing) return false;

    try {
      _isRefreshing = true;
      _error = null;
      // Remember mini player state
      final wasMiniPlayerDismissed = _isMiniPlayerDismissed;

      // Reset data and pagination variables
      _lastDocument = null;
      _hasMoreData = false; // Will load all at once
      _bhajans = [];
      notifyListeners();

      // Load ALL bhajans at once
      try {
        debugPrint('AudioProvider: Refreshing all bhajans at once...');

        // Get all bhajans without pagination
        final querySnapshot = await _firestore
            .collection('bhajans')
            .orderBy('uploadDate', descending: true)
            .get();

        debugPrint(
            'AudioProvider: Refresh retrieved ${querySnapshot.docs.length} total bhajans');

        // Process all bhajans
        List<String> processingErrors = [];
        for (var doc in querySnapshot.docs) {
          try {
            final bhajan = Bhajan.fromFirestore(doc);

            // Validate the bhajan URLs
            if (!Uri.parse(bhajan.r2Url).isAbsolute) {
              throw Exception('Invalid audio URL format');
            }
            if (!Uri.parse(bhajan.artworkUrl).isAbsolute) {
              throw Exception('Invalid artwork URL format');
            }

            _bhajans.add(bhajan);
          } catch (e) {
            debugPrint(
                'AudioProvider: Error processing bhajan document ${doc.id}: $e');
            processingErrors.add('Error processing bhajan ${doc.id}: $e');
            continue;
          }
        }

        if (processingErrors.isNotEmpty) {
          debugPrint(
              'AudioProvider: Some bhajans failed to process during refresh: ${processingErrors.length} errors');
        }
      } catch (e) {
        debugPrint('AudioProvider: Error refreshing all bhajans: $e');
        throw Exception(
            'Failed to refresh bhajans. Please check your connection and try again.');
      }

      // Update audio service but preserve the mini player state
      await _audioService.init(_bhajans,
          emitCurrentBhajan: !wasMiniPlayerDismissed);

      // Restore mini player state
      _isMiniPlayerDismissed = wasMiniPlayerDismissed;

      _isRefreshing = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Error refreshing bhajans: $e';
      debugPrint(_error);
      _isRefreshing = false;
      notifyListeners();
      return false;
    }
  }

  // Method to completely reset the audio provider state when returning from sign-in
  void resetAudioState() {
    _isMiniPlayerDismissed = true;

    // Reset the current state in audio service
    audioService.resetCurrentBhajan();

    debugPrint(
        'AudioProvider: Audio state completely reset after returning from sign-in');
    notifyListeners();
  }

  // Method to dismiss the mini player without stopping playback
  void dismissMiniPlayer() {
    if (_debugMiniPlayer) {
      debugPrint(
          'AudioProvider: Dismissing mini player, stack: ${StackTrace.current}');
    }

    _isMiniPlayerDismissed = true;
    _userExplicitlyDismissed = true;

    // Notify audio service about dismissal
    _audioService.onDismiss();

    // Make sure to pause playback first to stop sound
    if (_audioService.isPlaying) {
      _audioService.pause();
    }

    // Hide notification by calling the appropriate system method
    if (_audioService.player != null) {
      try {
        // Hide notification but don't release the player's resources
        _audioService.hideNotification();
        debugPrint('AudioProvider: Audio notification hidden');
      } catch (e) {
        debugPrint('AudioProvider: Error hiding notification: $e');
      }
    }

    debugPrint('AudioProvider: Mini player dismissed and notification hidden');

    notifyListeners();
  }

  // Method to completely stop playback and release resources
  void stopPlaybackCompletely() {
    if (_debugMiniPlayer) {
      debugPrint(
          'AudioProvider: Completely stopping playback, stack: ${StackTrace.current}');
    }

    _isMiniPlayerDismissed = true;

    // Stop playback if player exists
    if (_audioService.isInitialized) {
      try {
        // First make sure the notification is hidden
        _audioService.hideNotification();

        // Then stop the playback if player exists
        if (_audioService.player != null) {
          _audioService.player!.stop();
        }

        // Reset the current bhajan to null (this will update the UI)
        _audioService.resetCurrentBhajan();

        // Request audio manager to release the player
        final audioManager = AudioManager();
        audioManager.forceStopAndReleasePlayer(AudioType.bhajan);

        debugPrint(
            'AudioProvider: Audio player fully stopped, notification hidden, and resources released');
      } catch (e) {
        debugPrint('AudioProvider: Error during complete playback stop: $e');
      }
    }

    notifyListeners();
  }

  // Method to show the mini player (called when a new bhajan is played)
  void showMiniPlayer() {
    // Only update state if mini player was previously dismissed AND
    // not explicitly dismissed by user action
    if (_isMiniPlayerDismissed && !_userExplicitlyDismissed) {
      if (_debugMiniPlayer) {
        debugPrint(
            'AudioProvider: Showing mini player, bhajan playing: ${audioService.isPlaying}, stack: ${StackTrace.current}');
      }

      // Reset dismissal state in audio service
      _audioService.resetDismissState();

      _isMiniPlayerDismissed = false;
      notifyListeners();
      debugPrint('AudioProvider: Mini player visibility restored');
    } else if (_userExplicitlyDismissed) {
      // User explicitly dismissed it, do not show
      debugPrint(
          'AudioProvider: Not showing mini player because user explicitly dismissed it');
    } else if (audioService.getCurrentBhajan() != null) {
      // Force a state update if we have a current bhajan but mini player state might be inconsistent
      // This handles cases where we switch between tabs and the mini player doesn't appear
      notifyListeners();
      debugPrint(
          'AudioProvider: Mini player state refreshed due to active bhajan');
    }
  }

  // Explicitly show mini player and reset the explicit dismissal flag
  void forceShowMiniPlayer() {
    if (_debugMiniPlayer) {
      debugPrint(
          'AudioProvider: Force showing mini player and resetting user dismissal flag');
    }

    _userExplicitlyDismissed = false;
    _isMiniPlayerDismissed = false;
    notifyListeners();
  }

  // Make sure the mini player state is consistent with the current audio state
  // Call this method when navigating between tabs to ensure proper mini player visibility
  void refreshMiniPlayerState() {
    // If mini player was manually dismissed, respect that state
    // Do not force visibility if user explicitly dismissed the mini player

    // If there's no bhajan playing but mini player is showing, hide it
    if (audioService.getCurrentBhajan() == null && !_isMiniPlayerDismissed) {
      debugPrint(
          'AudioProvider: Fixing inconsistent mini player state - should be hidden with no active bhajan');
      _isMiniPlayerDismissed = true;
      notifyListeners();
    }
  }

  void playBhajan(int index) {
    // First, ensure mini player is visible and force UI update immediately
    _isMiniPlayerDismissed = false;
    // Reset the user explicit dismissal flag when playing a specific bhajan
    _userExplicitlyDismissed = false;

    if (_debugMiniPlayer) {
      debugPrint(
          'AudioProvider: Playing bhajan at index $index, forcing mini player visibility');
    }

    // Important: Notify listeners immediately to show mini player BEFORE audio starts
    notifyListeners();

    // Then play the bhajan
    audioService.play(index);

    // Notify again after playback is requested for good measure
    notifyListeners();
    debugPrint('AudioProvider: Playing bhajan and showing mini player');
  }

  // Play multiple bhajans starting from a specific index in the provided list
  void playMultipleBhajans(List<Bhajan> bhajanList, int startIndex) {
    // Don't replace the main bhajans list, just temporarily use this playlist
    // First, ensure mini player is visible
    _isMiniPlayerDismissed = false;
    // Reset user dismissal flag since user is explicitly starting playback
    _userExplicitlyDismissed = false;

    notifyListeners();

    debugPrint(
        'AudioProvider: Playing from custom playlist with ${bhajanList.length} bhajans');

    // Create a temporary playlist in the audio service without replacing the main list
    audioService.createTemporaryPlaylist(bhajanList, startIndex);

    notifyListeners();
  }

  void pauseBhajan() {
    audioService.pause();
    notifyListeners();
  }

  // Method to handle seeking during active scrubbing
  Future<void> seekImmediate(Duration position) async {
    debugPrint('AudioProvider: Immediate seek to position $position');

    // Reset dismissal flags since user is interacting
    _userExplicitlyDismissed = false;
    _isMiniPlayerDismissed = false;

    // Use immediate seek for responsive scrubbing
    await _audioService.seekImmediate(position);

    notifyListeners();
  }

  // Method to handle seeking when scrubbing ends
  Future<void> seekToPosition(Duration position) async {
    debugPrint('AudioProvider: Seeking to final position $position');

    // Reset dismissal flags since user is interacting
    _userExplicitlyDismissed = false;
    _isMiniPlayerDismissed = false;

    // Use regular seek for final position
    await _audioService.seek(position);

    notifyListeners();
  }

  void resumeBhajan() {
    // If user explicitly dismissed the mini player, don't force visibility
    if (!_userExplicitlyDismissed) {
      // First, ensure mini player is visible
      _isMiniPlayerDismissed = false;
    } else {
      // User dismissed it intentionally, so just log this
      debugPrint(
          'AudioProvider: Resuming without showing mini player due to user dismissal');
    }

    // Log the action
    debugPrint('AudioProvider: Resuming bhajan playback');

    // Resume playback using the audio service
    audioService.resume();

    // Notify listeners for UI update
    notifyListeners();
  }

  Future<void> playNextBhajan() async {
    try {
      await _audioService.playNext();
      notifyListeners();
    } catch (e) {
      debugPrint('Error playing next bhajan: $e');
      rethrow;
    }
  }

  Future<void> playPreviousBhajan() async {
    try {
      await _audioService.playPrevious();
      notifyListeners();
    } catch (e) {
      debugPrint('Error playing previous bhajan: $e');
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    await _audioService.dispose();
    super.dispose();
  }

  // Helper method to show snackbars that works across screens
  void showGlobalSnackBar({
    required String message,
    bool isSuccess = true,
    bool isError = false,
    Duration duration = const Duration(seconds: 3),
  }) {
    // Get the global navigator key's context
    final context = NavigationService.navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).clearSnackBars();

      SnackBar snackBar;
      if (isError) {
        snackBar =
            UIHelpers.getErrorSnackBar(message: message, duration: duration);
      } else if (isSuccess) {
        snackBar =
            UIHelpers.getSuccessSnackBar(message: message, duration: duration);
      } else {
        snackBar =
            UIHelpers.getInfoSnackBar(message: message, duration: duration);
      }

      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    }
  }
}
