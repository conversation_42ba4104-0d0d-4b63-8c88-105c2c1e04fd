import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdProvider extends ChangeNotifier {
  // Production ad unit IDs
  static const String _bannerAdUnitIdAndroid =
      'ca-app-pub-4661791287735662/**********';
  static const String _interstitialAdUnitIdAndroid =
      'ca-app-pub-4661791287735662/**********';
  static const String _appOpenAdUnitIdAndroid =
      'ca-app-pub-4661791287735662/**********';

  // Getters for ad unit IDs
  String get bannerAdUnitId => Platform.isAndroid ? _bannerAdUnitIdAndroid : '';
  String get interstitialAdUnitId =>
      Platform.isAndroid ? _interstitialAdUnitIdAndroid : '';
  String get appOpenAdUnitId =>
      Platform.isAndroid ? _appOpenAdUnitIdAndroid : '';

  // Banner ad
  BannerAd? _bannerAd;
  bool _isBannerAdLoaded = false;

  // Interstitial ad
  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdLoaded = false;
  int _interstitialLoadAttempts = 0;
  static const int maxFailedLoadAttempts = 3;

  // App Open Ad
  AppOpenAd? _appOpenAd;
  bool _isAppOpenAdLoaded = false;

  // Getters
  BannerAd? get bannerAd => _bannerAd;
  bool get isBannerAdLoaded => _isBannerAdLoaded;
  bool get isInterstitialAdLoaded => _isInterstitialAdLoaded;
  bool get isAppOpenAdLoaded => _isAppOpenAdLoaded;

  // Initialize ads
  Future<void> initialize() async {
    // Initialize the Mobile Ads SDK
    await MobileAds.instance.initialize();

    // Load banner ad
    loadBannerAd();

    // Load interstitial ad
    loadInterstitialAd();

    // Load App Open Ad
    loadAppOpenAd();
  }

  // Load banner ad
  void loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          debugPrint('Banner ad loaded successfully');
          _isBannerAdLoaded = true;
          notifyListeners();
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Banner ad failed to load: ${error.message}');
          ad.dispose();
          _bannerAd = null;
          _isBannerAdLoaded = false;
          notifyListeners();

          // Retry loading after a delay
          Future.delayed(const Duration(minutes: 1), () {
            if (_bannerAd == null) {
              loadBannerAd();
            }
          });
        },
      ),
    );

    _bannerAd?.load();
  }

  // Load interstitial ad
  void loadInterstitialAd() {
    if (_interstitialAdUnitIdAndroid.isEmpty) {
      debugPrint('Interstitial ad unit ID not set. Skipping load.');
      return;
    }
    InterstitialAd.load(
      adUnitId: interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          _interstitialAd = ad;
          _interstitialLoadAttempts = 0;
          _isInterstitialAdLoaded = true;
          notifyListeners();

          // Set callback for ad closing
          _interstitialAd?.fullScreenContentCallback =
              FullScreenContentCallback(
            onAdDismissedFullScreenContent: (ad) {
              ad.dispose();
              _isInterstitialAdLoaded = false;
              notifyListeners();
              loadInterstitialAd(); // Load a new ad
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              ad.dispose();
              _isInterstitialAdLoaded = false;
              notifyListeners();
              loadInterstitialAd(); // Try to load a new ad
            },
          );
        },
        onAdFailedToLoad: (LoadAdError error) {
          debugPrint('Interstitial ad failed to load: ${error.message}');
          _interstitialLoadAttempts += 1;
          _interstitialAd = null;
          _isInterstitialAdLoaded = false;
          notifyListeners();

          if (_interstitialLoadAttempts < maxFailedLoadAttempts) {
            Future.delayed(const Duration(minutes: 1), () {
              loadInterstitialAd();
            });
          }
        },
      ),
    );
  }

  // Show interstitial ad
  void showInterstitialAd() {
    if (_interstitialAdUnitIdAndroid.isEmpty) {
      debugPrint('Interstitial ad unit ID not set. Cannot show ad.');
      return;
    }
    if (_interstitialAd != null && _isInterstitialAdLoaded) {
      _interstitialAd?.show();
    } else {
      debugPrint('Interstitial ad not ready yet');
      loadInterstitialAd();
    }
  }

  // Show interstitial ad only if audio is not playing
  void showInterstitialAdIfNoAudioPlaying(bool isAudioPlaying) {
    if (!isAudioPlaying) {
      showInterstitialAd();
    } else {
      debugPrint('Audio is playing, not showing interstitial ad.');
    }
  }

  // Bhajan play counter
  int _bhajanPlayCount = 0;
  void registerBhajanPlay({required bool isAudioPlaying}) {
    if (isAudioPlaying) return;
    _bhajanPlayCount++;
    if (_bhajanPlayCount >= 2) {
      showInterstitialAd();
      _bhajanPlayCount = 0;
    }
  }

  // Timer for 1 minute surfing
  DateTime? _lastAdShownTime;
  void startSurfingTimer({required bool isAudioPlaying}) {
    Future.delayed(const Duration(minutes: 1), () {
      if (!isAudioPlaying) {
        final now = DateTime.now();
        if (_lastAdShownTime == null ||
            now.difference(_lastAdShownTime!).inMinutes >= 1) {
          showInterstitialAd();
          _lastAdShownTime = now;
        }
      }
    });
  }

  // Wallpaper download counter
  int _wallpaperDownloadCount = 0;
  void registerWallpaperDownload({required bool isAudioPlaying}) {
    if (isAudioPlaying) return;
    _wallpaperDownloadCount++;
    if (_wallpaperDownloadCount >= 3) {
      showInterstitialAd();
      _wallpaperDownloadCount = 0;
    }
  }

  // Reset ad counters (bhajan play count and surfing timer)
  void resetAdCounters() {
    _bhajanPlayCount = 0;
    _lastAdShownTime = null;
    _wallpaperDownloadCount = 0;
  }

  // Load App Open Ad
  void loadAppOpenAd() {
    AppOpenAd.load(
      adUnitId: appOpenAdUnitId,
      request: const AdRequest(),
      adLoadCallback: AppOpenAdLoadCallback(
        onAdLoaded: (ad) {
          _appOpenAd = ad;
          _isAppOpenAdLoaded = true;
        },
        onAdFailedToLoad: (error) {
          _isAppOpenAdLoaded = false;
          _appOpenAd = null;
        },
      ),
      orientation: AppOpenAd.orientationPortrait,
    );
  }

  // Show App Open Ad
  void showAppOpenAd() {
    if (_isAppOpenAdLoaded && _appOpenAd != null) {
      _appOpenAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          ad.dispose();
          _isAppOpenAdLoaded = false;
          loadAppOpenAd();
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          ad.dispose();
          _isAppOpenAdLoaded = false;
          loadAppOpenAd();
        },
      );
      _appOpenAd!.show();
      _appOpenAd = null;
      _isAppOpenAdLoaded = false;
    } else {
      loadAppOpenAd();
    }
  }

  // Dispose ads
  @override
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _appOpenAd?.dispose();
    super.dispose();
  }
}
