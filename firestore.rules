rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow any authenticated user to read public collections
    match /{collection}/{document=**} {
      allow read: if collection == 'bhajans' || 
                  collection == 'metadata' || 
                  collection == 'wallpapers' || 
                  collection == 'ringtones' ||
                  collection == 'names' ||
                  collection == 'avatars';
    }
    
    // Allow authenticated users to read/write their own user data and preferences
    match /users/{userId}/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow specific write operations for user preferences
    match /users/{userId}/preferences/user_preferences {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read/write their own playlists
    match /playlists/{playlistId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
    }
    
    // Allow anonymous users to read/write their own progress data
    match /user_progress/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Default deny for anything not explicitly allowed
    match /{document=**} {
      allow read, write: if false;
    }
  }
}