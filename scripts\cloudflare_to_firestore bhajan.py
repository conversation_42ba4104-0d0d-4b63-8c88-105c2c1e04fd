import boto3
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
import os
from mutagen import File
from mutagen.mp3 import MP3
import urllib.request
import urllib.parse  # Add this import for URL encoding

# Service account path (updated)
SERVICE_ACCOUNT_PATH = r"C:\pyflutter\projects\hindupath\scripts\hindupath-studiohabre-firebase-adminsdk.json"

# Cloudflare R2 configuration
R2_CONFIG = {
    'endpoint_url': 'https://3a0098f2f5a153bb2a3de2fba20d674d.r2.cloudflarestorage.com',
    'aws_access_key_id': '70664ebbc81354bce2e32098189e3578',
    'aws_secret_access_key': 'd52c62e094eb0e728d083d36909e634aa6c093708bdbe54aaa8e8821ad76f004',
    'region_name': 'auto'
}

# CDN base URLs
CDN_BASE_URL = 'https://cdn.hindupath.online/bhajan/bhajans'
ARTWORK_BASE_URL = 'https://cdn.hindupath.online/bhajan/artwork'

# Artwork mapping
ARTWORK_MAPPING = {
    'universe': f"{ARTWORK_BASE_URL}/Universe.png",
    'ganesha': f"{ARTWORK_BASE_URL}/lord_ganesha.png",
    'krishna': f"{ARTWORK_BASE_URL}/lord_krishna.png",
    'shiva': f"{ARTWORK_BASE_URL}/lord_shiva.png",
    'durga': f"{ARTWORK_BASE_URL}/shri_durga.png",
    'lakshmi': f"{ARTWORK_BASE_URL}/shri_lakshmi.png",
    'hanuman': f"{ARTWORK_BASE_URL}/lord_hanuman.png",
    'saraswati': f"{ARTWORK_BASE_URL}/shri_saraswati.png",
    'ram': f"{ARTWORK_BASE_URL}/lord_ram.png"
}

# Initialize R2 client
s3_client = boto3.client('s3', **R2_CONFIG)

# Check if service account file exists
if not os.path.exists(SERVICE_ACCOUNT_PATH):
    print(f"Error: Service account file not found at: {SERVICE_ACCOUNT_PATH}")
    exit(1)

# Initialize Firebase Admin SDK
try:
    cred = credentials.Certificate(SERVICE_ACCOUNT_PATH)
    firebase_admin.initialize_app(cred)
    db = firestore.client()
    print(f"Successfully initialized Firebase with service account from: {SERVICE_ACCOUNT_PATH}")
except Exception as e:
    print(f"Error initializing Firebase: {str(e)}")
    exit(1)

def get_audio_duration(obj_key):
    """Get audio duration using mutagen"""
    try:
        # Download the file to a temporary location
        temp_file = f"temp_{os.path.basename(obj_key)}"
        s3_client.download_file('hindupath', obj_key, temp_file)
        
        # Get duration
        audio = MP3(temp_file)
        duration = int(audio.info.length)
        
        # Clean up temp file
        os.remove(temp_file)
        
        # Format duration as mm:ss
        minutes = duration // 60
        seconds = duration % 60
        return f"{minutes:02d}:{seconds:02d}"
    except Exception as e:
        print(f"Warning: Could not get duration for {obj_key}: {str(e)}")
        return "00:00"

def get_artwork_url(title):
    """Get artwork URL based on title"""
    title_lower = title.lower()
    
    # Check each keyword in the title
    for keyword, url in ARTWORK_MAPPING.items():
        if keyword in title_lower:
            return url
            
    # Default to Universe artwork if no match found
    return f"{ARTWORK_BASE_URL}/Universe.png"

def format_file_size(size_in_bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_in_bytes < 1024:
            return f"{size_in_bytes:.2f} {unit}"
        size_in_bytes /= 1024
    return f"{size_in_bytes:.2f} GB"

def is_bhajan_file(key):
    """Check if the file is a bhajan (in bhajan/bhajans folder and is mp3)"""
    return key.startswith('bhajan/bhajans/') and key.lower().endswith('.mp3')

def get_existing_bhajan_keys():
    """Fetch all r2ObjectKey values from Firestore to skip already added bhajans"""
    existing_keys = set()
    try:
        docs = db.collection('bhajans').stream()
        for doc in docs:
            data = doc.to_dict()
            if 'r2ObjectKey' in data:
                existing_keys.add(data['r2ObjectKey'])
    except Exception as e:
        print(f"Error fetching existing bhajans from Firestore: {str(e)}")
    return existing_keys

def upload_bhajans_to_firestore_skip_existing():
    try:
        # First, test if Firestore is accessible
        try:
            db.collection('bhajans').limit(1).get()
        except Exception as firebase_error:
            if 'SERVICE_DISABLED' in str(firebase_error):
                print("\n❌ Error: Firestore API is not enabled")
                print("Please follow these steps to enable it:")
                print("1. Go to Firebase Console: https://console.firebase.google.com")
                print("2. Select project: hindupathstudiohabre")
                print("3. Click on 'Firestore Database' in the left sidebar")
                print("4. Click 'Create Database'")
                print("5. Choose mode (Production or Test)")
                print("6. Select a location")
                print("7. Click 'Enable'")
                print("\nAfter enabling, wait a few minutes and try again.")
                return
            else:
                raise firebase_error

        # List all objects in the R2 bucket
        response = s3_client.list_objects_v2(Bucket='hindupath')
        
        if 'Contents' not in response:
            print("No objects found in the bucket")
            return

        # Filter only bhajan files from the bhajans folder
        bhajan_files = [obj for obj in response['Contents'] if is_bhajan_file(obj['Key'])]
        total_files = len(bhajan_files)
        
        if total_files == 0:
            print("No bhajan files found in the bhajans folder")
            return

        # Fetch existing bhajan keys from Firestore
        existing_keys = get_existing_bhajan_keys()
        print(f"Found {len(existing_keys)} existing bhajans in Firestore. Skipping these.")

        processed_files = 0
        new_files = 0

        for obj in bhajan_files:
            try:
                processed_files += 1
                print(f"\nProcessing file {processed_files}/{total_files}: {obj['Key']}")

                # Skip if already in Firestore
                if obj['Key'] in existing_keys:
                    print(f"   Skipping (already exists in Firestore): {obj['Key']}")
                    continue

                # Get the object metadata
                metadata = s3_client.head_object(
                    Bucket='hindupath',
                    Key=obj['Key']
                )

                # Extract filename without extension and folder path
                filename = os.path.splitext(os.path.basename(obj['Key']))[0]
                
                # Create the CDN URL
                cdn_url = f"{CDN_BASE_URL}/{urllib.parse.quote(filename)}.mp3"

                # Get duration
                duration = get_audio_duration(obj['Key'])

                # Get artwork URL
                artwork_url = get_artwork_url(filename)

                # Prepare bhajan document with minimal metadata
                bhajan_data = {
                    'title': filename,
                    'r2ObjectKey': obj['Key'],
                    'r2Url': cdn_url,
                    'size': format_file_size(obj['Size']),
                    'duration': duration,
                    'artworkUrl': artwork_url,
                    'uploadDate': metadata['LastModified']
                }

                # Add to Firestore
                doc_ref = db.collection('bhajans').document()
                doc_ref.set(bhajan_data)
                new_files += 1
                
                print(f"✅ Successfully uploaded metadata for: {filename}")
                print(f"   CDN URL: {cdn_url}")
                print(f"   Duration: {duration}")
                print(f"   Artwork: {artwork_url}")

            except Exception as e:
                print(f"❌ Error processing {obj['Key']}: {str(e)}")
                continue

        print(f"\n✨ Upload complete! {new_files} new bhajans added. {total_files - new_files} skipped.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting bhajan metadata upload (skip existing)...")
    upload_bhajans_to_firestore_skip_existing()
    print("\n✨ Script finished!") 