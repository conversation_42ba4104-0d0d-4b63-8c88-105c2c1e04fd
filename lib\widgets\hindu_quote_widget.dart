import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';

class HinduQuoteWidget extends StatefulWidget {
  const HinduQuoteWidget({super.key});

  @override
  State<HinduQuoteWidget> createState() => _HinduQuoteWidgetState();
}

class _HinduQuoteWidgetState extends State<HinduQuoteWidget> {
  List<Map<String, String>> quotes = [];
  int currentQuoteIndex = 0;
  Timer? _quoteTimer;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadQuotes();
    _setupQuoteTimer();
  }

  Future<void> _loadQuotes() async {
    try {
      debugPrint("Loading Hindu quotes...");
      final jsonString = await rootBundle
          .loadString('assets/hindu_quotes/hindupath_quotes.json');
      debugPrint("JSON loaded successfully");

      final jsonData = jsonDecode(jsonString);
      final quotesData = jsonData['Sheet1'] as List;

      setState(() {
        quotes = quotesData
            .map<Map<String, String>>((quote) => {
                  "text": quote['Quote'].toString(),
                  "source": quote['Source'].toString()
                })
            .toList();

        debugPrint("Quotes loaded: ${quotes.length}");

        if (quotes.isNotEmpty) {
          currentQuoteIndex = _getRandomIndex();
        }

        _isLoading = false;
        _error = null;
      });
    } catch (e) {
      debugPrint("Error loading quotes: $e");
      setState(() {
        _isLoading = false;
        _error = e.toString();
        quotes = [
          {
            "text": 'Error loading quotes. Please try again later.',
            "source": "System"
          }
        ];
        currentQuoteIndex = 0;
      });
    }
  }

  int _getRandomIndex() {
    if (quotes.isEmpty) return 0;
    final random = Random();
    return random.nextInt(quotes.length);
  }

  void _setupQuoteTimer() {
    // Change quote every 8 hours
    _quoteTimer = Timer.periodic(const Duration(hours: 8), (timer) {
      if (mounted && quotes.isNotEmpty) {
        setState(() {
          currentQuoteIndex = _getRandomIndex();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenHeight = MediaQuery.of(context).size.height;
        final double minHeight = 80;
        final double maxHeight = 0.4 * screenHeight;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final screenWidth = MediaQuery.of(context).size.width;
              final containerWidth = screenWidth - 32.0; // Account for padding

              return Container(
                width: constraints.maxWidth,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.getPrimaryColorWithOpacity(context, 0.8),
                      AppColors.getPrimaryColorWithOpacity(context, 0.4),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Stack(
                    children: [
                      // Background temple silhouette pattern
                      Positioned(
                        right: -20,
                        bottom: -30,
                        child: Opacity(
                          opacity: 0.1,
                          child: Image.asset(
                            'assets/images/hindu_temples.png',
                            width: 220,
                            height: 200,
                            fit: BoxFit.contain,
                            color: Colors.white,
                          ),
                        ),
                      ),

                      // Decorative elements
                      Positioned(
                        left: 15,
                        top: 15,
                        child: Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.format_quote_rounded,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),

                      // Quote content with container
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 35, 20, 15),
                        child: Center(
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 800),
                            transitionBuilder:
                                (Widget child, Animation<double> animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: SlideTransition(
                                  position: Tween<Offset>(
                                    begin: const Offset(0.0, 0.05),
                                    end: Offset.zero,
                                  ).animate(CurvedAnimation(
                                    parent: animation,
                                    curve: Curves.easeOutCubic,
                                  )),
                                  child: child,
                                ),
                              );
                            },
                            child: _isLoading
                                ? CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  )
                                : _error != null
                                    ? Text(
                                        'Error loading quotes',
                                        style: AppFontLoader.getJosefinStyle(
                                          color: Colors.white.withOpacity(0.7),
                                        ),
                                      )
                                    : Container(
                                        key: ValueKey<int>(currentQuoteIndex),
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(
                                            minHeight: minHeight,
                                            maxHeight: maxHeight,
                                          ),
                                          child: SingleChildScrollView(
                                            physics:
                                                const BouncingScrollPhysics(),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  '"${quotes[currentQuoteIndex]["text"]}"',
                                                  textAlign: TextAlign.center,
                                                  style: AppFontLoader
                                                      .getJosefinStyle(
                                                    fontSize: 17,
                                                    color: Colors.white,
                                                    letterSpacing: 0.3,
                                                    fontWeight: FontWeight.w500,
                                                  ).copyWith(
                                                    height: 1.4,
                                                  ),
                                                ),
                                                if (quotes[currentQuoteIndex]
                                                            ["source"]
                                                        ?.isNotEmpty ??
                                                    false) ...[
                                                  const SizedBox(height: 12),
                                                  Text(
                                                    '— ${quotes[currentQuoteIndex]["source"]}',
                                                    textAlign: TextAlign.center,
                                                    style: AppFontLoader
                                                        .getPrakrtaStyle(
                                                      fontSize: 14,
                                                      color: Colors.white
                                                          .withOpacity(0.8),
                                                      letterSpacing: 0.5,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                          ),
                        ),
                      ),

                      // Share & change quote buttons
                      Positioned(
                        bottom: 12,
                        left: 12,
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                if (!_isLoading && quotes.isNotEmpty) {
                                  setState(() {
                                    currentQuoteIndex = _getRandomIndex();
                                  });
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Icon(
                                  Icons.refresh_rounded,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: () {
                                final quote = quotes[currentQuoteIndex]["text"];
                                final source =
                                    quotes[currentQuoteIndex]["source"];
                                Clipboard.setData(ClipboardData(
                                  text: '$quote\n— $source',
                                ));
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Quote copied to clipboard'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Icon(
                                  Icons.share_outlined,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _quoteTimer?.cancel();
    super.dispose();
  }
}
