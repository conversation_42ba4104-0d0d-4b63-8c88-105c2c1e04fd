import boto3
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
import os
import urllib.parse

# Service account path
SERVICE_ACCOUNT_PATH = r"C:\pyflutter\projects\hindupath\scripts\hindupath-studiohabre-firebase-adminsdk.json"

# Cloudflare R2 configuration
R2_CONFIG = {
    'endpoint_url': 'https://3a0098f2f5a153bb2a3de2fba20d674d.r2.cloudflarestorage.com',
    'aws_access_key_id': '70664ebbc81354bce2e32098189e3578',
    'aws_secret_access_key': 'd52c62e094eb0e728d083d36909e634aa6c093708bdbe54aaa8e8821ad76f004',
    'region_name': 'auto'
}

# CDN base URL for ringtones
CDN_BASE_URL = 'https://cdn.hindupath.online'

# Initialize R2 client
s3_client = boto3.client('s3', **R2_CONFIG)

# Check if service account file exists
if not os.path.exists(SERVICE_ACCOUNT_PATH):
    print(f"Error: Service account file not found at: {SERVICE_ACCOUNT_PATH}")
    exit(1)

# Initialize Firebase Admin SDK
try:
    cred = credentials.Certificate(SERVICE_ACCOUNT_PATH)
    firebase_admin.initialize_app(cred)
    db = firestore.client()
    print(f"Successfully initialized Firebase with service account from: {SERVICE_ACCOUNT_PATH}")
except Exception as e:
    print(f"Error initializing Firebase: {str(e)}")
    exit(1)

def format_file_size(size_in_bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_in_bytes < 1024:
            return f"{size_in_bytes:.2f} {unit}"
        size_in_bytes /= 1024
    return f"{size_in_bytes:.2f} GB"

def is_ringtone_file(key):
    """Check if the file is a ringtone (in ringtones folder and is an audio file)"""
    audio_extensions = ('.mp3', '.wav', '.m4a', '.ogg', '.aac')
    return (
        key.startswith('ringtones/') and 
        any(key.lower().endswith(ext) for ext in audio_extensions)
    )

def get_title_from_filename(filename):
    """Extract a clean title from the filename"""
    # Remove extension
    name_without_ext = os.path.splitext(filename)[0]
    
    # Replace underscores and hyphens with spaces
    clean_name = name_without_ext.replace('_', ' ').replace('-', ' ')
    
    # Title case the name
    return clean_name.title()

def upload_ringtones_to_firestore():
    try:
        # First, test if Firestore is accessible
        try:
            db.collection('ringtones').limit(1).get()
        except Exception as firebase_error:
            if 'SERVICE_DISABLED' in str(firebase_error):
                print("\n❌ Error: Firestore API is not enabled")
                print("Please enable Firestore in your Firebase Console")
                return
            else:
                raise firebase_error

        # List all objects in the R2 bucket
        response = s3_client.list_objects_v2(Bucket='hindupath')
        
        if 'Contents' not in response:
            print("No objects found in the bucket")
            return

        # Filter only ringtone files
        ringtone_files = [obj for obj in response['Contents'] if is_ringtone_file(obj['Key'])]
        total_files = len(ringtone_files)
        
        if total_files == 0:
            print("No ringtone files found")
            return

        processed_files = 0

        for obj in ringtone_files:
            try:
                processed_files += 1
                print(f"\nProcessing file {processed_files}/{total_files}: {obj['Key']}")

                # Get the object metadata
                metadata = s3_client.head_object(
                    Bucket='hindupath',
                    Key=obj['Key']
                )

                # Extract filename
                filename = os.path.basename(obj['Key'])
                
                # Get title from filename
                title = get_title_from_filename(filename)
                
                # Create the CDN URL - remove the initial 'ringtones/' from the key since it's already in the CDN path
                object_path = obj['Key'].replace('ringtones/', '', 1)
                cdn_url = f"{CDN_BASE_URL}/ringtones/{urllib.parse.quote(object_path)}"

                # Prepare ringtone document
                ringtone_data = {
                    'title': title,
                    'filename': filename,
                    'url': cdn_url,
                    'size': format_file_size(obj['Size']),
                    'uploadDate': metadata['LastModified'],
                    'r2ObjectKey': object_path  # Store without the initial 'ringtones/'
                }

                # Add to Firestore
                doc_ref = db.collection('ringtones').document()
                doc_ref.set(ringtone_data)
                
                print(f"✅ Successfully uploaded metadata for: {title}")
                print(f"   Filename: {filename}")
                print(f"   CDN URL: {cdn_url}")
                print(f"   Size: {ringtone_data['size']}")

            except Exception as e:
                print(f"❌ Error processing {obj['Key']}: {str(e)}")
                continue

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting ringtone metadata upload...")
    upload_ringtones_to_firestore()
    print("\n✨ Upload complete!") 