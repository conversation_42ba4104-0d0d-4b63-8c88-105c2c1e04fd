import 'package:flutter/material.dart';
import '../utils/color_utils.dart';

// Loading indicator sizes
enum LoadingSize {
  small,
  medium,
  large,
}

// Loading types
enum LoadingType {
  circular,
  linear,
}

class LoadingIndicator extends StatelessWidget {
  final String? message;
  final LoadingSize size;
  final LoadingType type;
  final bool withBackground;
  final bool centered;
  final double opacity;

  const LoadingIndicator({
    super.key,
    this.message,
    this.size = LoadingSize.medium,
    this.type = LoadingType.circular,
    this.withBackground = false,
    this.centered = true,
    this.opacity = 0.8,
  });

  @override
  Widget build(BuildContext context) {
    // Get the appropriate size for the spinner
    double spinnerSize;
    double fontSize;
    switch (size) {
      case LoadingSize.small:
        spinnerSize = 24.0;
        fontSize = 12.0;
        break;
      case LoadingSize.large:
        spinnerSize = 48.0;
        fontSize = 16.0;
        break;
      case LoadingSize.medium:
      default:
        spinnerSize = 36.0;
        fontSize = 14.0;
        break;
    }

    // Get the theme and primary color
    final color = AppColors.getPrimaryColor(context);

    // Create the loading indicator
    Widget loadingWidget;
    if (type == LoadingType.circular) {
      loadingWidget = SizedBox(
        width: spinnerSize,
        height: spinnerSize,
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(color),
          strokeWidth: size == LoadingSize.small ? 2.0 : 3.0,
        ),
      );
    } else {
      loadingWidget = SizedBox(
        width: 200,
        child: LinearProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(color),
          backgroundColor: color.withOpacity(0.2),
        ),
      );
    }

    // Create the loading content with optional message
    Widget content;
    if (message != null) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          loadingWidget,
          const SizedBox(height: 16),
          Text(
            message!,
            style: TextStyle(
              fontSize: fontSize,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    } else {
      content = loadingWidget;
    }

    // Add background if requested
    if (withBackground) {
      content = Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(opacity),
          borderRadius: BorderRadius.circular(12),
        ),
        child: content,
      );
    }

    // Center if requested
    if (centered) {
      return Center(child: content);
    }

    return content;
  }
}
