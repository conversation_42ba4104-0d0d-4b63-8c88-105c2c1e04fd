"""
CL<PERSON><PERSON><PERSON><PERSON><PERSON> TO FIRESTORE SCRIPT FOR WALLPAPERS (SKIP EXISTING VERSION)
This script uploads wallpaper metadata from Cloudflare R2 to Firestore.
Unlike the original version, this script:
1. Checks if wallpapers already exist in Firestore (by r2ObjectKey or filename)
2. Skips uploading metadata for wallpapers that already exist
3. Only adds new wallpapers not yet in the database
4. Provides a summary of skipped and added files

Usage: python cloudflare_to_firestore_wallpapers_skip_existing.py
"""

import boto3
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
import os
import urllib.parse

# Service account path
SERVICE_ACCOUNT_PATH = r"C:\pyflutter\projects\hindupath\scripts\hindupath-studiohabre-firebase-adminsdk.json"

# Cloudflare R2 configuration
R2_CONFIG = {
    'endpoint_url': 'https://3a0098f2f5a153bb2a3de2fba20d674d.r2.cloudflarestorage.com',
    'aws_access_key_id': '70664ebbc81354bce2e32098189e3578',
    'aws_secret_access_key': 'd52c62e094eb0e728d083d36909e634aa6c093708bdbe54aaa8e8821ad76f004',
    'region_name': 'auto'
}

# CDN base URL for wallpapers
CDN_BASE_URL = 'https://cdn.hindupath.online'

# Initialize R2 client
s3_client = boto3.client('s3', **R2_CONFIG)

# Check if service account file exists
if not os.path.exists(SERVICE_ACCOUNT_PATH):
    print(f"Error: Service account file not found at: {SERVICE_ACCOUNT_PATH}")
    exit(1)

# Initialize Firebase Admin SDK
try:
    cred = credentials.Certificate(SERVICE_ACCOUNT_PATH)
    firebase_admin.initialize_app(cred)
    db = firestore.client()
    print(f"Successfully initialized Firebase with service account from: {SERVICE_ACCOUNT_PATH}")
except Exception as e:
    print(f"Error initializing Firebase: {str(e)}")
    exit(1)

def get_category_from_path(key):
    """Extract category from the file path"""
    # The path format is: wallpapers/category_name/filename
    parts = key.split('/')
    if len(parts) >= 2:
        return parts[1].replace('_', ' ').title()
    return "Unknown"

def format_file_size(size_in_bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_in_bytes < 1024:
            return f"{size_in_bytes:.2f} {unit}"
        size_in_bytes /= 1024
    return f"{size_in_bytes:.2f} GB"

def is_wallpaper_file(key):
    """Check if the file is a wallpaper (in wallpapers folder and is an image)"""
    image_extensions = ('.jpg', '.jpeg', '.png', '.webp')
    return (
        key.startswith('wallpapers/') and 
        any(key.lower().endswith(ext) for ext in image_extensions)
    )

def upload_wallpapers_to_firestore():
    try:
        # First, test if Firestore is accessible
        try:
            db.collection('wallpapers').limit(1).get()
        except Exception as firebase_error:
            if 'SERVICE_DISABLED' in str(firebase_error):
                print("\n❌ Error: Firestore API is not enabled")
                print("Please enable Firestore in your Firebase Console")
                return
            else:
                raise firebase_error

        # List all objects in the R2 bucket
        response = s3_client.list_objects_v2(Bucket='hindupath')
        
        if 'Contents' not in response:
            print("No objects found in the bucket")
            return

        # Filter only wallpaper files
        wallpaper_files = [obj for obj in response['Contents'] if is_wallpaper_file(obj['Key'])]
        total_files = len(wallpaper_files)
        
        if total_files == 0:
            print("No wallpaper files found")
            return

        processed_files = 0
        skipped_files = 0
        added_files = 0

        # Get all existing wallpapers from Firestore to avoid re-adding
        print("Fetching existing wallpapers from Firestore...")
        existing_wallpapers = {}
        existing_wallpapers_by_filename = {}
        existing_wallpapers_query = db.collection('wallpapers').stream()
        for doc in existing_wallpapers_query:
            wallpaper_data = doc.to_dict()
            if 'r2ObjectKey' in wallpaper_data:
                existing_wallpapers[wallpaper_data['r2ObjectKey']] = doc.id
            if 'filename' in wallpaper_data:
                existing_wallpapers_by_filename[wallpaper_data['filename']] = doc.id
        
        print(f"Found {len(existing_wallpapers)} existing wallpapers in Firestore")

        for obj in wallpaper_files:
            try:
                processed_files += 1
                print(f"\nProcessing file {processed_files}/{total_files}: {obj['Key']}")

                # Extract filename, category, and object path
                filename = os.path.basename(obj['Key'])
                category = get_category_from_path(obj['Key'])
                object_path = obj['Key'].replace('wallpapers/', '', 1)

                # Check if wallpaper already exists in Firestore by object path
                if object_path in existing_wallpapers:
                    print(f"⏭️ Skipping {obj['Key']} - already exists in Firestore (doc ID: {existing_wallpapers[object_path]})")
                    skipped_files += 1
                    continue

                # Also check if a wallpaper with this filename already exists
                if filename in existing_wallpapers_by_filename:
                    print(f"⏭️ Skipping {filename} - already exists in Firestore with filename (doc ID: {existing_wallpapers_by_filename[filename]})")
                    skipped_files += 1
                    continue

                # Get the object metadata
                metadata = s3_client.head_object(
                    Bucket='hindupath',
                    Key=obj['Key']
                )
                
                # Create the CDN URL
                cdn_url = f"{CDN_BASE_URL}/wallpapers/{urllib.parse.quote(object_path)}"

                # Prepare wallpaper document
                wallpaper_data = {
                    'filename': filename,
                    'category': category,
                    'url': cdn_url,
                    'size': format_file_size(obj['Size']),
                    'uploadDate': metadata['LastModified'],
                    'r2ObjectKey': object_path  # Store without the initial 'wallpapers/'
                }

                # Add to Firestore
                doc_ref = db.collection('wallpapers').document()
                doc_ref.set(wallpaper_data)
                added_files += 1
                
                print(f"✅ Successfully uploaded metadata for: {filename}")
                print(f"   Category: {category}")
                print(f"   CDN URL: {cdn_url}")
                print(f"   Size: {wallpaper_data['size']}")

            except Exception as e:
                print(f"❌ Error processing {obj['Key']}: {str(e)}")
                continue

        print("\n📊 Summary:")
        print(f"Total wallpaper files found: {total_files}")
        print(f"Files processed: {processed_files}")
        print(f"Files skipped (already exist): {skipped_files}")
        print(f"New files added: {added_files}")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting wallpaper metadata upload (skipping existing wallpapers)...")
    upload_wallpapers_to_firestore()
    print("\n✨ Upload complete!") 