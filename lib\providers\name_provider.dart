import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/name_data.dart';
import '../services/auth_service.dart';
import '../services/user_preferences_service.dart';
import 'package:firebase_auth/firebase_auth.dart';

class NameProvider extends ChangeNotifier {
  List<NameData> _names = [];
  List<String> _favoriteNames = [];
  bool _isLoading = true;
  String? _error;
  final UserPreferencesService _preferencesService = UserPreferencesService();
  final AuthService _authService = AuthService();

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<NameData> get boyNames =>
      _names.where((name) => name.gender == 'Boy').toList();
  List<NameData> get girlNames =>
      _names.where((name) => name.gender == 'Girl').toList();
  List<String> get favoriteNames => _favoriteNames;

  // Get favorite names as NameData objects
  List<NameData> get favoriteNameData {
    // Check if user is authenticated
    if (_authService.currentUser == null) {
      return []; // Return empty list if not authenticated
    }
    return _names.where((name) => _favoriteNames.contains(name.name)).toList();
  }

  NameProvider() {
    _loadFavoriteNames();
  }

  Future<void> _loadFavoriteNames() async {
    final user = _authService.currentUser;
    // Only load favorites if a user is authenticated
    if (user != null) {
      try {
        _favoriteNames = await _preferencesService.getFavoriteNames(user.uid);
        notifyListeners();
      } catch (e) {
        print('Error loading favorite names: $e');
      }
    } else {
      // Clear favorites when no user is authenticated
      _favoriteNames = [];
      notifyListeners();
    }
  }

  Future<void> loadNames() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final String jsonString = await rootBundle
          .loadString('assets/naamkaran/namakaran_database.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> namesList = jsonData['Sheet1'];

      _names = namesList.map((json) => NameData.fromJson(json)).toList();
      _names.sort((a, b) => a.name.compareTo(b.name)); // Sort alphabetically

      // Reload favorite names
      await _loadFavoriteNames();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Error loading names: $e';
      print(_error);
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  bool isNameFavorite(String name) {
    // Check authentication first
    if (_authService.currentUser == null) return false;
    return _favoriteNames.contains(name);
  }

  // Toggle favorite status and save to Firebase for authenticated users
  Future<void> toggleFavorite(String name) async {
    final user = _authService.currentUser;
    // Return early if not authenticated
    if (user == null) {
      debugPrint('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      // Optimistic update - immediately update UI without waiting for server
      final wasAlreadyFavorite = _favoriteNames.contains(name);

      // Toggle the state locally first
      if (wasAlreadyFavorite) {
        _favoriteNames.remove(name);
      } else {
        _favoriteNames.add(name);
      }

      // Notify listeners immediately for responsive UI
      notifyListeners();

      // Then perform the server operation without blocking UI
      if (wasAlreadyFavorite) {
        // Run in microtask to not block UI thread
        Future.microtask(() async {
          try {
            await _preferencesService.removeFavoriteName(user.uid, name);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            debugPrint('Error removing name from favorites: $e');
            _favoriteNames.add(name);
            notifyListeners();
          }
        });
      } else {
        Future.microtask(() async {
          try {
            await _preferencesService.addFavoriteName(user.uid, name);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            debugPrint('Error adding name to favorites: $e');
            _favoriteNames.remove(name);
            notifyListeners();
          }
        });
      }
    } catch (e) {
      debugPrint('Error toggling name favorite: $e');
    }
  }

  // Clear favorites when user logs out
  void clearFavorites() {
    _favoriteNames = [];
    notifyListeners();
  }

  // Load favorites after user signs in
  Future<void> loadFavorites() async {
    // Check authentication before loading
    if (_authService.currentUser == null) {
      // Clear favorites if no user is authenticated
      _favoriteNames = [];
      notifyListeners();
      return;
    }
    await _loadFavoriteNames();
  }
}
