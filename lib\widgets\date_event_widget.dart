import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nepali_utils/nepali_utils.dart';
import '../services/event_service.dart';
import '../utils/font_loader.dart';
import '../pages/hindu_calendar_page.dart';
import '../utils/color_utils.dart';
import '../utils/tithi_calculator.dart';
import '../utils/navigation_service.dart';

class DateEventWidget extends StatefulWidget {
  const DateEventWidget({super.key});

  @override
  State<DateEventWidget> createState() => _DateEventWidgetState();
}

class _DateEventWidgetState extends State<DateEventWidget>
    with TickerProviderStateMixin {
  List<HinduEvent> _upcomingEvents = [];
  bool _isLoading = true;
  String? _error;
  EventCountry _selectedCountry = EventCountry.india;
  int _currentEventIndex = 0;

  // Tithi information
  String _tithiText = 'Loading...';
  bool _isLocationBased = false;
  bool _isTithiLoading = true;

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  // Animation controllers

  @override
  void initState() {
    super.initState();

    // Setup animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _slideAnimation = Tween<double>(
      begin: 20.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuart,
    ));

    // Initialize animations

    // Register as a listener for country changes
    EventService.addCountryChangeListener(_onCountryChanged);

    // Load events immediately when widget is initialized
    _loadEvents();

    // Load tithi information separately
    _loadTithi();
  }

  // Load tithi information
  Future<void> _loadTithi() async {
    if (!mounted) return;

    setState(() {
      _isTithiLoading = true;
    });

    try {
      final DateTime gregorianNow = DateTime.now();

      // Use enhanced TithiCalculator that has application-level caching
      // This will avoid recalculation when returning to the home page
      final Map<String, String> tithiInfo =
          await TithiCalculator.calculateTithiWithLocation(gregorianNow);

      if (mounted) {
        setState(() {
          _tithiText =
              '${tithiInfo['name']!} (${tithiInfo['paksha']!.substring(0, 1)})';
          _isLocationBased = tithiInfo['isLocationBased'] == 'true';
          _isTithiLoading = false;
        });
      }
    } catch (e) {
      // Use fallback calculation on error
      final DateTime gregorianNow = DateTime.now();

      // This also uses the enhanced caching system
      final Map<String, String> fallbackTithiInfo =
          TithiCalculator.calculateTithi(gregorianNow);

      if (mounted) {
        setState(() {
          _tithiText =
              '${fallbackTithiInfo['name']!} (${fallbackTithiInfo['paksha']!.substring(0, 1)})';
          _isLocationBased = false;
          _isTithiLoading = false;
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if country has changed since last build
    if (_selectedCountry != EventService.selectedCountry) {
      _loadEvents();
    }
  }

  @override
  void dispose() {
    // Remove listener when widget is disposed
    EventService.removeCountryChangeListener(_onCountryChanged);
    _animationController.dispose();
    super.dispose();
  }

  // Callback function for country changes
  void _onCountryChanged() {
    if (mounted && _selectedCountry != EventService.selectedCountry) {
      _loadEvents();
    }
  }

  Future<void> _loadEvents() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Initialize EventService to load events
      await EventService.initialize();

      // Get the currently selected country from EventService
      _selectedCountry = EventService.selectedCountry;

      // Get the upcoming events after initialization
      _upcomingEvents = EventService.getAllUpcomingEvents();

      // If we have upcoming events, get all events for the first upcoming date
      if (_upcomingEvents.isNotEmpty) {
        final firstEventDate = _upcomingEvents.first.date;
        _upcomingEvents = EventService.getAllEventsForDate(firstEventDate);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _currentEventIndex =
              0; // Reset to first event when loading new events
        });

        // Start animation after data is loaded
        _animationController.forward(from: 0.0);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load events: $e';
          _isLoading = false;
        });
      }
    }
  }

  void _nextEvent() {
    if (_upcomingEvents.length <= 1) return;

    setState(() {
      _currentEventIndex = (_currentEventIndex + 1) % _upcomingEvents.length;
    });
  }

  void _previousEvent() {
    if (_upcomingEvents.length <= 1) return;

    setState(() {
      _currentEventIndex = (_currentEventIndex - 1 + _upcomingEvents.length) %
          _upcomingEvents.length;
    });
  }

  // Add a new method to check if events are today
  bool _areEventsToday() {
    if (_upcomingEvents.isEmpty) return false;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final firstEventDate = DateTime(
      _upcomingEvents.first.date.year,
      _upcomingEvents.first.date.month,
      _upcomingEvents.first.date.day,
    );

    return today.isAtSameMomentAs(firstEventDate);
  }

  @override
  Widget build(BuildContext context) {
    final bool isToday = _areEventsToday();
    // Get current date in Nepali calendar
    final NepaliDateTime now = NepaliDateTime.now();
    final String dayName = _getEnglishDayName(now.weekday);
    final String dayNumber = now.day.toString();
    final String monthYear = '${_getEnglishMonthName(now.month)} ${now.year}';

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isNarrow = screenWidth < 360; // Adjust for extra small screens
        final horizontalPadding = isNarrow ? 8.0 : 16.0;

        return Padding(
          padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding, vertical: 8.0),
          child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value,
                  child: Transform.translate(
                    offset: Offset(0, _slideAnimation.value),
                    child: _buildCardContent(
                      dayName: dayName,
                      dayNumber: dayNumber,
                      monthYear: monthYear,
                      isNarrow: isNarrow,
                      isToday: isToday,
                    ),
                  ),
                );
              }),
        );
      },
    );
  }

  Widget _buildCardContent({
    required String dayName,
    required String dayNumber,
    required String monthYear,
    required bool isNarrow,
    required bool isToday,
  }) {
    final bool isSmallScreen = MediaQuery.of(context).size.width < 360;

    // Clean modern design with smooth gradient background
    return Card(
      elevation: 4,
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8.0 : 0),
      shadowColor: Colors.black.withOpacity(0.2),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () {
          final navigator = NavigationService.navigatorKey.currentState;
          if (navigator != null) {
            navigator.push(
              MaterialPageRoute(
                builder: (context) => const HinduCalendarPage(),
              ),
            );
          }
        },
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.getPrimaryColor(context).withOpacity(0.8),
                AppColors.getPrimaryColor(context).withOpacity(0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.getPrimaryColor(context).withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.getPrimaryColor(context).withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: isNarrow
              ? _buildNarrowLayout(
                  dayName: dayName,
                  dayNumber: dayNumber,
                  monthYear: monthYear,
                  isToday: isToday,
                )
              : _buildRegularLayout(
                  dayName: dayName,
                  dayNumber: dayNumber,
                  monthYear: monthYear,
                  isToday: isToday,
                ),
        ),
      ),
    );
  }

  // For regular-sized screens
  Widget _buildRegularLayout({
    required String dayName,
    required String dayNumber,
    required String monthYear,
    required bool isToday,
  }) {
    return Stack(
      children: [
        // Main content (removed event count indicator from top right)
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Date column (left side)
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Day name
                  Text(
                    dayName,
                    style: AppFontLoader.getPrakrtaStyle(
                      fontSize: 15,
                      letterSpacing: 0.8,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                  const SizedBox(height: 2),

                  // Day number (large)
                  Text(
                    dayNumber,
                    style: AppFontLoader.getJosefinStyle(
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),

                  // Month and year
                  Text(
                    monthYear,
                    style: AppFontLoader.getJosefinStyle(
                      fontSize: 15,
                      letterSpacing: 0.5,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),

                  // Tithi info
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Tithi: ',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      Text(
                        _tithiText,
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      if (_isLocationBased) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 1,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'Local',
                            style: TextStyle(
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),

              const SizedBox(width: 16),

              // Vertical divider
              Container(
                height: 90,
                width: 1,
                color: Colors.white.withOpacity(0.3),
              ),

              const SizedBox(width: 16),

              // Event info (right side)
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final screenHeight = MediaQuery.of(context).size.height;
                    final double minHeight = 60;
                    final double maxHeight = 0.4 * screenHeight;
                    return ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: minHeight,
                        maxHeight: maxHeight,
                      ),
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header with region - separate row to isolate the title from badge
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    isToday
                                        ? "Today's Events"
                                        : "Upcoming Events",
                                    style: AppFontLoader.getPrakrtaStyle(
                                      fontSize:
                                          MediaQuery.of(context).size.width <
                                                  350
                                              ? 14
                                              : 17,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                    maxLines: 2,
                                    softWrap: true,
                                    overflow: TextOverflow.visible,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  margin: const EdgeInsets.only(left: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    _selectedCountry == EventCountry.india
                                        ? 'India'
                                        : 'Nepal',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            // Event name - ensure full width and proper overflow handling
                            _upcomingEvents.isNotEmpty
                                ? Text(
                                    _upcomingEvents[_currentEventIndex].name,
                                    style: AppFontLoader.getJosefinStyle(
                                      fontSize:
                                          MediaQuery.of(context).size.width <
                                                  350
                                              ? 14
                                              : 17,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                    maxLines: 3,
                                    softWrap: true,
                                    overflow: TextOverflow.visible,
                                  )
                                : Text(
                                    'No upcoming events',
                                    style: AppFontLoader.getJosefinStyle(
                                      fontSize:
                                          MediaQuery.of(context).size.width <
                                                  350
                                              ? 13
                                              : 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white.withOpacity(0.7),
                                    ),
                                    maxLines: 3,
                                    softWrap: true,
                                    overflow: TextOverflow.visible,
                                  ),
                            const SizedBox(height: 6),
                            // Event date and navigation
                            if (_upcomingEvents.isNotEmpty)
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  // Date
                                  Expanded(
                                    child: Text(
                                      DateFormat('MMMM d, yyyy').format(
                                          _upcomingEvents[_currentEventIndex]
                                              .date),
                                      style: AppFontLoader.getJosefinStyle(
                                        fontSize: 13,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                      maxLines: 2,
                                      softWrap: true,
                                      overflow: TextOverflow.visible,
                                    ),
                                  ),
                                  // Navigation controls (only if multiple events)
                                  if (_upcomingEvents.length > 1)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          left: 4.0, top: 2.0),
                                      child: IconButton(
                                        onPressed: _nextEvent,
                                        icon: const Icon(
                                            Icons.arrow_forward_ios,
                                            size: 18,
                                            color: Colors.white),
                                        tooltip: 'Next Event',
                                      ),
                                    ),
                                ],
                              ),
                            const SizedBox(height: 6),
                            // Countdown badge
                            if (_upcomingEvents.isNotEmpty)
                              _buildCountdownBadge(),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // Event image at bottom right corner with spin animation
        Positioned(
          right: 10,
          bottom: 10,
          child: Opacity(
            opacity: 0.9,
            child: Image.asset(
              'assets/images/event.png',
              width: 50, // Increased by 20% from 42
              height: 50, // Increased by 20% from 42
            ),
          ),
        ),
      ],
    );
  }

  // For narrow screens
  Widget _buildNarrowLayout({
    required String dayName,
    required String dayNumber,
    required String monthYear,
    required bool isToday,
  }) {
    // Check if screen is extra small (based on font render size)
    final screenWidth = MediaQuery.of(context).size.width;
    final isExtraSmall = screenWidth < 320;

    return Stack(
      children: [
        Padding(
          padding:
              const EdgeInsets.all(12.0), // Reduced padding for small screens
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date row (top)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start, // Align to top
                children: [
                  // Day number
                  Text(
                    dayNumber,
                    style: AppFontLoader.getJosefinStyle(
                      fontSize: isExtraSmall
                          ? 32
                          : 36, // Smaller for extra small screens
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Day, month info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          dayName,
                          style: AppFontLoader.getPrakrtaStyle(
                            fontSize: 14,
                            letterSpacing: 0.5,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                        Text(
                          monthYear,
                          style: AppFontLoader.getJosefinStyle(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Region badge - moved to separate row for better spacing
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _selectedCountry == EventCountry.india
                          ? 'India'
                          : 'Nepal',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              // Tithi info - better spacing and layout for small screens
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Wrap(
                  spacing: 4,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Text(
                      'Tithi: ',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                    Text(
                      _tithiText.split('(')[0].trim(), // Just the tithi name
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '(${_tithiText.split('(')[1]}', // The paksha part
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    if (_isLocationBased)
                      Container(
                        margin: const EdgeInsets.only(left: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 1,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'Local',
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              const Divider(color: Colors.white30, height: 16),

              // Event section heading with better spacing
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      isToday ? "Today's Events" : "Upcoming Events",
                      style: AppFontLoader.getPrakrtaStyle(
                        fontSize: isExtraSmall
                            ? 16
                            : 18, // Smaller for extra small screens
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 6),

              // Event name with adjusted font size
              _upcomingEvents.isNotEmpty
                  ? Flexible(
                      child: Text(
                        _upcomingEvents[_currentEventIndex].name,
                        style: AppFontLoader.getJosefinStyle(
                          fontSize: isExtraSmall
                              ? 16
                              : 18, // Smaller for extra small screens
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 3,
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                    )
                  : Text(
                      'No upcoming events',
                      style: AppFontLoader.getJosefinStyle(
                        fontSize: isExtraSmall
                            ? 14
                            : 16, // Smaller for extra small screens
                        fontWeight: FontWeight.bold,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),

              const SizedBox(height: 6),

              // Event date, countdown and navigation with better spacing for small screens
              if (_upcomingEvents.isNotEmpty)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Date with adjusted width
                    Expanded(
                      flex: 3,
                      child: Text(
                        DateFormat(
                                isExtraSmall ? 'MMM d, yyyy' : 'MMMM d, yyyy')
                            .format(_upcomingEvents[_currentEventIndex].date),
                        style: AppFontLoader.getJosefinStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        maxLines: 2,
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                    ),

                    // Countdown badge with flex to ensure proper sizing
                    Expanded(
                      flex: 2,
                      child: _buildCountdownBadge(),
                    ),

                    // Navigation controls
                    if (_upcomingEvents.length > 1)
                      IconButton(
                        onPressed: _nextEvent,
                        icon: const Icon(Icons.arrow_forward_ios,
                            size: 18, color: Colors.white),
                        tooltip: 'Next Event',
                      ),
                  ],
                ),
            ],
          ),
        ),

        // Event image at bottom right corner with spin animation
        Positioned(
          right: 10,
          bottom: 10,
          child: Opacity(
            opacity: 0.9,
            child: Image.asset(
              'assets/images/event.png',
              width: 43, // Increased by 20% from 36
              height: 43, // Increased by 20% from 36
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        shape: BoxShape.circle,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          customBorder: const CircleBorder(),
          child: Center(
            child: Icon(
              icon,
              size: 12,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCountdownBadge() {
    final String daysText = _getDaysRemaining();
    final bool isToday = daysText == 'Today';
    final bool isSmallScreen = MediaQuery.of(context).size.width < 360;

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 6 : 8, vertical: isSmallScreen ? 2 : 3),
      decoration: BoxDecoration(
        color: isToday
            ? Colors.white.withOpacity(0.25)
            : Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isToday ? Colors.white.withOpacity(0.3) : Colors.transparent,
          width: 1,
        ),
      ),
      child: Text(
        daysText,
        style: AppFontLoader.getJosefinStyle(
          fontSize: isSmallScreen ? 10 : 11,
          fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  // Get days remaining until the upcoming event
  String _getDaysRemaining() {
    if (_upcomingEvents.isEmpty) return '';

    final now = DateTime.now();
    final difference =
        _upcomingEvents[_currentEventIndex].date.difference(now).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Tomorrow';
    } else {
      return '$difference days left';
    }
  }

  // Helper methods for English date formatting
  String _getEnglishDayName(int weekday) {
    const List<String> englishDayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday'
    ];

    int index = (weekday % 7) - 1;
    if (index < 0) index = 6;

    return englishDayNames[index];
  }

  String _getEnglishMonthName(int month) {
    const List<String> englishMonthNames = [
      'Baishakh',
      'Jestha',
      'Ashadh',
      'Shrawan',
      'Bhadra',
      'Ashwin',
      'Kartik',
      'Mangsir',
      'Poush',
      'Magh',
      'Falgun',
      'Chaitra'
    ];

    return englishMonthNames[month - 1];
  }

  // Helper to determine Nepali month name for a Gregorian date
  String _getNepaliMonthForGregorianDate(DateTime gregorianDate) {
    try {
      const List<String> nepaliMonthNames = [
        'Baishakh',
        'Jestha',
        'Ashadh',
        'Shrawan',
        'Bhadra',
        'Ashwin',
        'Kartik',
        'Mangsir',
        'Poush',
        'Magh',
        'Falgun',
        'Chaitra'
      ];

      final nepaliDate = NepaliDateTime.fromDateTime(gregorianDate);
      final int nepaliMonth = nepaliDate.month;
      final monthName = nepaliMonthNames[nepaliMonth - 1];

      return monthName;
    } catch (e) {
      return 'Unknown';
    }
  }
}
