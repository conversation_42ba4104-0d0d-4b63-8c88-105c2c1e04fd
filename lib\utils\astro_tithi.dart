// Accurate Tithi Calculation using Sun and Moon Ecliptic Longitudes
// Ported and simplified from SunCalc and basic Meeus algorithms (JS/Python)
// No external API required

import 'dart:math';

class AstroTithi {
  // Constants
  static const double deg2rad = pi / 180.0;
  static const double rad2deg = 180.0 / pi;

  // Julian Day calculation
  static double _toJ<PERSON>an(DateTime date) {
    return date.toUtc().millisecondsSinceEpoch / 86400000.0 + 2440587.5;
  }

  // Sun ecliptic longitude (approximate, good for tithi)
  static double sunEclipticLongitude(DateTime date) {
    final double d = _toJulian(date) - 2451545.0;
    // Mean anomaly
    final double g = (357.529 + 0.98560028 * d) % 360;
    // Mean longitude
    final double q = (280.459 + 0.98564736 * d) % 360;
    // Ecliptic longitude
    final double L =
        (q + 1.915 * sin(g * deg2rad) + 0.020 * sin(2 * g * deg2rad)) % 360;
    return (L < 0) ? L + 360 : L;
  }

  // Moon ecliptic longitude (simplified, good for tithi)
  static double moonEclipticLongitude(DateTime date) {
    final double d = _to<PERSON><PERSON>an(date) - 2451545.0;
    final double L0 = (218.316 + 13.176396 * d) % 360; // Moon's mean longitude
    final double M = (134.963 + 13.064993 * d) % 360; // Moon's mean anomaly
    final double F =
        (93.272 + 13.229350 * d) % 360; // Moon's argument of latitude
    // Ecliptic longitude (simplified)
    double lon = L0 +
        6.289 * sin(M * deg2rad) -
        1.274 * sin((2 * (L0) - M) * deg2rad) +
        0.658 * sin(2 * L0 * deg2rad) -
        0.214 * sin(2 * M * deg2rad) -
        0.186 * sin(sunMeanAnomaly(date) * deg2rad) +
        0.059 * sin((2 * (L0) - 2 * M) * deg2rad) +
        0.057 * sin((2 * (L0) - M - sunMeanAnomaly(date)) * deg2rad) +
        0.053 * sin((2 * (L0) + M) * deg2rad) +
        0.046 * sin((2 * (L0) - sunMeanAnomaly(date)) * deg2rad) +
        0.041 * sin((M - sunMeanAnomaly(date)) * deg2rad);
    lon = lon % 360;
    return (lon < 0) ? lon + 360 : lon;
  }

  static double sunMeanAnomaly(DateTime date) {
    final double d = _toJulian(date) - 2451545.0;
    return (357.529 + 0.98560028 * d) % 360;
  }

  // Calculate tithi (1-30)
  static int calculateTithi(DateTime date) {
    final double sunLon = sunEclipticLongitude(date);
    final double moonLon = moonEclipticLongitude(date);
    double diff = (moonLon - sunLon) % 360;
    if (diff < 0) diff += 360;
    int tithi = (diff / 12).floor() + 1;
    if (tithi > 30) tithi = tithi % 30;
    return tithi;
  }

  // Get tithi name and paksha
  static Map<String, String> getTithiInfo(DateTime date) {
    int tithiNum = calculateTithi(date);
    const tithiNames = [
      'Pratipada',
      'Dwitiya',
      'Tritiya',
      'Chaturthi',
      'Panchami',
      'Shashthi',
      'Saptami',
      'Ashtami',
      'Navami',
      'Dashami',
      'Ekadashi',
      'Dwadashi',
      'Trayodashi',
      'Chaturdashi',
      'Purnima',
      'Pratipada',
      'Dwitiya',
      'Tritiya',
      'Chaturthi',
      'Panchami',
      'Shashthi',
      'Saptami',
      'Ashtami',
      'Navami',
      'Dashami',
      'Ekadashi',
      'Dwadashi',
      'Trayodashi',
      'Chaturdashi',
      'Amavasya'
    ];
    String paksha = tithiNum <= 15 ? 'Shukla' : 'Krishna';
    String tithiName = tithiNames[tithiNum - 1];
    return {
      'number': tithiNum.toString(),
      'name': tithiName,
      'paksha': paksha,
    };
  }
}
