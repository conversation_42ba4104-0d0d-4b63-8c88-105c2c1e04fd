import 'package:cloud_firestore/cloud_firestore.dart';

class Ringtone {
  final String id;
  final String title;
  final String filename;
  final String url;
  final String size;
  final String r2ObjectKey;
  final DateTime uploadDate;

  Ringtone({
    required this.id,
    required this.title,
    required this.filename,
    required this.url,
    required this.size,
    required this.r2ObjectKey,
    required this.uploadDate,
  });

  factory Ringtone.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return Ringtone(
      id: doc.id,
      title: data['title'] ?? '',
      filename: data['filename'] ?? '',
      url: data['url'] ?? '',
      size: data['size'] ?? '',
      r2ObjectKey: data['r2ObjectKey'] ?? '',
      uploadDate: (data['uploadDate'] as Timestamp).toDate(),
    );
  }

  @override
  String toString() {
    return 'Ringtone{id: $id, title: $title, filename: $filename, url: $url}';
  }
}
