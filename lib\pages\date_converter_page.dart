import 'package:flutter/material.dart';
import '../utils/bs_date_utils.dart';
import 'package:intl/intl.dart';
import '../utils/color_utils.dart';
import '../utils/theme_widgets.dart';

class DateConverterPage extends StatefulWidget {
  const DateConverterPage({super.key});

  @override
  State<DateConverterPage> createState() => _DateConverterPageState();
}

class _DateConverterPageState extends State<DateConverterPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Date conversion variables
  late DateTime _selectedADDate;
  late DateTime _selectedBSDate;

  // BS date selection
  late int _bsDay;
  late int _bsMonth;
  late int _bsYear;

  // AD date selection
  late int _adDay;
  late int _adMonth;
  late int _adYear;

  // Lists for dropdowns
  final List<String> _nepaliMonths = [
    '<PERSON>sha<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>ush',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>'
  ];

  final List<String> _englishMonths = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];

  // Lists will be dynamically updated based on month/year selection
  late List<int> _bsDays;
  late List<int> _adDays;
  late List<int> _bsYears;
  late List<int> _adYears;

  // Conversion results
  String _bsResult = '';
  String _adResult = '';
  String _dayOfWeek = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    print('Initializing date converter page'); // Debug info

    // Initialize with current date
    final now = DateTime.now();
    _selectedADDate = now;
    _selectedBSDate = BSDateUtils.toBSDate(now);

    print('Current AD date: $now'); // Debug info
    print('Converted BS date: $_selectedBSDate'); // Debug info

    // Initialize years lists
    _bsYears = List.generate(100, (index) => 2000 + index);
    _adYears = List.generate(100, (index) => 1970 + index);

    // Set initial BS date values
    _bsDay = _selectedBSDate.day;
    _bsMonth = _selectedBSDate.month;
    _bsYear = _selectedBSDate.year;

    // Initialize day lists with safe values before calculations
    _bsDays = List.generate(31, (index) => index + 1);
    _adDays = List.generate(31, (index) => index + 1);

    // Set initial AD date values
    _adDay = _selectedADDate.day;
    _adMonth = _selectedADDate.month;
    _adYear = _selectedADDate.year;

    print('Initial BS date: $_bsDay/$_bsMonth/$_bsYear'); // Debug info
    print('Initial AD date: $_adDay/$_adMonth/$_adYear'); // Debug info

    // Initialize days lists based on selected month/year
    _updateBSDaysForMonth();
    _updateADDaysForMonth();

    // Initialize conversion results
    _updateBSToADResult();
    _updateADToBSResult();

    print('Initialization complete'); // Debug info

    // Schedule an extra update after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateBSDaysForMonth();
        _updateADDaysForMonth();
        setState(() {}); // Force refresh
      }
    });
  }

  // Update BS days based on month and year
  void _updateBSDaysForMonth() {
    int daysInMonth = BSDateUtils.getDaysInBSMonth(_bsYear, _bsMonth);
    print(
        'BS days in month $_bsMonth of year $_bsYear: $daysInMonth'); // Debug info

    // Make sure we have at least 28 days (minimum reasonable value)
    if (daysInMonth < 28) {
      print(
          'WARNING: Unusually low days count ($daysInMonth). Using fallback value.');
      daysInMonth = 30; // Fallback to a reasonable value
    }

    setState(() {
      _bsDays = List.generate(daysInMonth, (index) => index + 1);
      print('BS days list updated: ${_bsDays.length} days'); // Debug info

      // Adjust day if it exceeds the number of days in the month
      if (_bsDay > daysInMonth) {
        _bsDay = daysInMonth;
      } else if (_bsDay < 1) {
        _bsDay = 1;
      }
      print('Current BS day set to: $_bsDay'); // Debug info
    });
  }

  // Update AD days based on month and year
  void _updateADDaysForMonth() {
    int daysInMonth = DateTime(_adYear, _adMonth + 1, 0).day;
    setState(() {
      _adDays = List.generate(daysInMonth, (index) => index + 1);
      // Adjust day if it exceeds the number of days in the month
      if (_adDay > daysInMonth) {
        _adDay = daysInMonth;
      } else if (_adDay < 1) {
        _adDay = 1;
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Update BS to AD result display
  void _updateBSToADResult() {
    if (BSDateUtils.isValidBSDate(_bsYear, _bsMonth, _bsDay)) {
      try {
        // Create BS date
        final bsDate = DateTime(_bsYear, _bsMonth, _bsDay);
        _selectedBSDate = bsDate;

        // Convert to AD
        _selectedADDate = BSDateUtils.toADDate(bsDate);

        // Update result strings
        setState(() {
          _bsResult =
              '${_bsDay.toString().padLeft(2, '0')} ${_nepaliMonths[_bsMonth - 1]}, $_bsYear';
          _adResult =
              '${_selectedADDate.day.toString().padLeft(2, '0')} ${DateFormat('MMMM').format(_selectedADDate)}, ${_selectedADDate.year}';
          _dayOfWeek = DateFormat('EEEE').format(_selectedADDate);
        });
      } catch (e) {
        _showInvalidDateError("Error converting date: ${e.toString()}");
      }
    } else {
      _showInvalidDateError("Invalid Nepali Date");
    }
  }

  // Update AD to BS result display
  void _updateADToBSResult() {
    if (BSDateUtils.isValidADDate(_adYear, _adMonth, _adDay)) {
      try {
        // Create AD date
        final adDate = DateTime(_adYear, _adMonth, _adDay);
        _selectedADDate = adDate;

        // Convert to BS
        _selectedBSDate = BSDateUtils.toBSDate(adDate);

        // Update result strings
        setState(() {
          _adResult =
              '${_adDay.toString().padLeft(2, '0')} ${_englishMonths[_adMonth - 1]}, $_adYear';
          _bsResult =
              '${_selectedBSDate.day.toString().padLeft(2, '0')} ${_nepaliMonths[_selectedBSDate.month - 1]}, ${_selectedBSDate.year}';
          _dayOfWeek = DateFormat('EEEE').format(_selectedADDate);
        });
      } catch (e) {
        _showInvalidDateError("Error converting date: ${e.toString()}");
      }
    } else {
      _showInvalidDateError("Invalid English Date");
    }
  }

  // Show error dialog for invalid dates
  void _showInvalidDateError(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF0D0D0D),
        title: const Text('Invalid Date',
            style: TextStyle(color: Colors.white, fontFamily: 'JosefinSans')),
        content: Text(message,
            style: const TextStyle(
                color: Colors.white, fontFamily: 'JosefinSans')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK',
                style: TextStyle(
                    color: AppColors.getPrimaryColor(context),
                    fontFamily: 'JosefinSans')),
          ),
        ],
      ),
    );
  }

  // Custom date selection widget

  Widget _buildSelectionButton({
    required String label,
    required String value,
    required VoidCallback onTap,
  }) {
    // Make UI responsive based on screen size
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Container(
          height: isSmallScreen ? 40 : 48,
          decoration: BoxDecoration(
            color: const Color(0xFF111111),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 14 : 16,
                    fontFamily: 'JosefinSans',
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
              Icon(Icons.arrow_drop_down, color: Colors.white, size: isSmallScreen ? 20 : 24),
            ],
          ),
        ),
      ),
    );
  }

  // Show day picker dialog
  void _showDayPicker(BuildContext context, bool isBS) {
    // Update days list before showing the picker to ensure it's current
    if (isBS) {
      int daysInMonth = BSDateUtils.getDaysInBSMonth(_bsYear, _bsMonth);
      print(
          'Showing BS day picker for month $_bsMonth of year $_bsYear with $daysInMonth days');
      if (daysInMonth < 28) {
        print('WARNING: Low BS days count. Using default value.');
        daysInMonth = 30;
      }
      setState(() {
        _bsDays = List.generate(daysInMonth, (index) => index + 1);
      });
    } else {
      _updateADDaysForMonth();
    }

    final days = isBS ? _bsDays : _adDays;
    final currentDay = isBS ? _bsDay : _adDay;

    print(
        'Opening day picker: isBS=$isBS, days=${days.length}, currentDay=$currentDay'); // Debug info

    // Ensure we have valid days list
    if (days.isEmpty || days.length < 28) {
      print('Warning: Days list is empty or too short! Creating backup list');
      final int backupDays = isBS ? 30 : 31;
      final backupList = List.generate(backupDays, (index) => index + 1);

      if (isBS) {
        setState(() {
          _bsDays = backupList;
        });
      } else {
        setState(() {
          _adDays = backupList;
        });
      }

      // Show error and return
      _showInvalidDateError("Please try again. Fixing calendar data...");
      return;
    }

    // Create a fixed height for each day item for consistent scrolling
    const double itemHeight = 50.0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF0D0D0D),
        title: Text(
          isBS ? 'Select BS Day' : 'Select AD Day',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'JosefinSans',
          ),
          textAlign: TextAlign.center,
        ),
        content: Container(
          width: double.maxFinite,
          height: 300,
          constraints: const BoxConstraints(maxHeight: 300),
          child: ListView.builder(
            itemCount: days.length,
            itemExtent: itemHeight, // Fixed height for each item
            itemBuilder: (context, index) {
              final day = days[index];
              final isSelected = day == currentDay;

              return Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    print('Day ${days[index]} selected'); // Debug info
                    setState(() {
                      if (isBS) {
                        _bsDay = day;
                        print('Selected BS day: $_bsDay'); // Debug info
                      } else {
                        _adDay = day;
                        print('Selected AD day: $_adDay'); // Debug info
                      }
                    });
                    Navigator.pop(context);

                    // Force update after selection
                    if (isBS) {
                      _updateBSToADResult();
                    } else {
                      _updateADToBSResult();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.getPrimaryColor(context)
                          : null,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      day.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'JosefinSans',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'JosefinSans',
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show month picker dialog
  void _showMonthPicker(BuildContext context, bool isBS) {
    final months = isBS ? _nepaliMonths : _englishMonths;
    final currentMonth = isBS ? _bsMonth : _adMonth;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF0D0D0D),
        title: const Text(
          'Select Month',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'JosefinSans',
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: months.length,
            itemBuilder: (context, index) {
              final month = index + 1;
              final isSelected = month == currentMonth;

              return InkWell(
                onTap: () {
                  setState(() {
                    if (isBS) {
                      _bsMonth = month;
                      _updateBSDaysForMonth();
                    } else {
                      _adMonth = month;
                      _updateADDaysForMonth();
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color:
                        isSelected ? AppColors.getPrimaryColor(context) : null,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    months[index],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'JosefinSans',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // Show year picker dialog
  void _showYearPicker(BuildContext context, bool isBS) {
    final years = isBS ? _bsYears : _adYears;
    final currentYear = isBS ? _bsYear : _adYear;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF0D0D0D),
        title: const Text(
          'Select Year',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'JosefinSans',
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: years.length,
            itemBuilder: (context, index) {
              final year = years[index];
              final isSelected = year == currentYear;

              return InkWell(
                onTap: () {
                  setState(() {
                    if (isBS) {
                      _bsYear = year;
                      _updateBSDaysForMonth();
                    } else {
                      _adYear = year;
                      _updateADDaysForMonth();
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color:
                        isSelected ? AppColors.getPrimaryColor(context) : null,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    year.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'JosefinSans',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // Emergency reset method in case day selection is not working
  void _emergencyResetDates() {
    // Reset to today's date
    final now = DateTime.now();
    setState(() {
      _selectedADDate = now;
      _selectedBSDate = BSDateUtils.toBSDate(now);

      // Reset BS date values
      _bsDay = _selectedBSDate.day;
      _bsMonth = _selectedBSDate.month;
      _bsYear = _selectedBSDate.year;

      // Reset AD date values
      _adDay = _selectedADDate.day;
      _adMonth = _selectedADDate.month;
      _adYear = _selectedADDate.year;

      // Reset day lists with safe values
      _bsDays = List.generate(30, (index) => index + 1);
      _adDays = List.generate(31, (index) => index + 1);
    });

    // Force update days lists
    _updateBSDaysForMonth();
    _updateADDaysForMonth();

    // Update the results
    _updateBSToADResult();
    _updateADToBSResult();
  }

  @override
  Widget build(BuildContext context) {
    // Check if date lists are properly initialized
    if (_bsDays.isEmpty || _adDays.isEmpty) {
      print('WARNING: Empty day lists detected in build, resetting...');
      _emergencyResetDates();
    }

    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      appBar: AppBar(
        title: const Text('Date Converter'),
        backgroundColor: const Color(0xFF0D0D0D),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [],
        bottom: TabBar(
          controller: _tabController,
          labelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 14,
          ),
          indicator: ThemedTabIndicator(
            context: context,
            radius: 4,
            indicatorHeight: 3,
            insets: const EdgeInsets.symmetric(horizontal: 20),
          ),
          tabs: const [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('BS to AD'),
                  SizedBox(width: 8),
                  Icon(Icons.calendar_today, size: 20),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('AD to BS'),
                  SizedBox(width: 8),
                  Icon(Icons.calendar_month, size: 20),
                ],
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // BS to AD Tab
          _buildBSToADTab(),

          // AD to BS Tab
          _buildADToBSTab(),
        ],
      ),
    );
  }

  // BS to AD Tab
  Widget _buildBSToADTab() {
    // Get screen width to make the layout responsive
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmallScreen = screenWidth < 320;
    final isSmallScreen = screenWidth < 360;
    final spacing = isSmallScreen ? 8.0 : 12.0;
    final labelFontSize = isSmallScreen ? 14.0 : 16.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Instructions
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.getPrimaryColor(context), width: 1),
            ),
            child: Text(
              'Please select your date, month and year to convert',
              style: TextStyle(
                color: Colors.white,
                fontSize: labelFontSize,
                fontFamily: 'JosefinSans',
              ),
            ),
          ),
          SizedBox(height: isSmallScreen ? 16 : 24),

          // Date selection - responsive layout
          if (isVerySmallScreen)
            // Very small screens: Stack fields vertically
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Day field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Day',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: labelFontSize,
                        fontFamily: 'JosefinSans',
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    _buildSelectionButton(
                      label: 'Day',
                      value: _bsDay.toString(),
                      onTap: () => _showDayPicker(context, true),
                    ),
                  ],
                ),
                SizedBox(height: spacing),

                // Month field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Month',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: labelFontSize,
                        fontFamily: 'JosefinSans',
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    _buildSelectionButton(
                      label: 'Month',
                      value: _nepaliMonths[_bsMonth - 1],
                      onTap: () => _showMonthPicker(context, true),
                    ),
                  ],
                ),
                SizedBox(height: spacing),

                // Year field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Year',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: labelFontSize,
                        fontFamily: 'JosefinSans',
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    _buildSelectionButton(
                      label: 'Year',
                      value: _bsYear.toString(),
                      onTap: () => _showYearPicker(context, true),
                    ),
                  ],
                ),
              ],
            )
          else
            // Normal layout with Row for larger screens
            Row(
              children: [
                // Day column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Day',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: labelFontSize,
                          fontFamily: 'JosefinSans',
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 8),
                      _buildSelectionButton(
                        label: 'Day',
                        value: _bsDay.toString(),
                        onTap: () => _showDayPicker(context, true),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: spacing),

                // Month column with reduced flex on small screens
                Expanded(
                  flex: isSmallScreen ? 1 : 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Month',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: labelFontSize,
                          fontFamily: 'JosefinSans',
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 8),
                      _buildSelectionButton(
                        label: 'Month',
                        value: _nepaliMonths[_bsMonth - 1],
                        onTap: () => _showMonthPicker(context, true),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: spacing),

                // Year column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Year',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: labelFontSize,
                          fontFamily: 'JosefinSans',
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 8),
                      _buildSelectionButton(
                        label: 'Year',
                        value: _bsYear.toString(),
                        onTap: () => _showYearPicker(context, true),
                      ),
                    ],
                  ),
                ),
              ],
            ),

          SizedBox(height: isSmallScreen ? 16 : 24),

          // Convert button
          Center(
            child: ElevatedButton(
              onPressed: _updateBSToADResult,
              style: AppColors.getPrimaryButtonStyle(
                context,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Convert',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Conversion result - with responsive text sizes
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.getPrimaryColor(context), width: 1),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Conversion',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: labelFontSize,
                    fontFamily: 'JosefinSans',
                  ),
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),

                // BS Date
                Text(
                  _bsResult,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'JosefinSans',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),

                // AD Date
                Text(
                  _adResult,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 28 : 36,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'JosefinSans',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),

                // Day of week
                Text(
                  _dayOfWeek,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 16 : 18,
                    fontFamily: 'JosefinSans',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // AD to BS Tab
  Widget _buildADToBSTab() {
    // Get screen width to make the layout responsive
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmallScreen = screenWidth < 320;
    final isSmallScreen = screenWidth < 360;
    final spacing = isSmallScreen ? 8.0 : 12.0;
    final labelFontSize = isSmallScreen ? 14.0 : 16.0;
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Instructions
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.getPrimaryColor(context), width: 1),
            ),
            child: Text(
              'Please select your date, month and year to convert',
              style: TextStyle(
                color: Colors.white,
                fontSize: labelFontSize,
                fontFamily: 'JosefinSans',
              ),
            ),
          ),
          SizedBox(height: isSmallScreen ? 16 : 24),

          // Date selection - responsive layout
          if (isVerySmallScreen)
            // Very small screens: Stack fields vertically
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Day field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Day',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: labelFontSize,
                        fontFamily: 'JosefinSans',
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    _buildSelectionButton(
                      label: 'Day',
                      value: _adDay.toString(),
                      onTap: () => _showDayPicker(context, false),
                    ),
                  ],
                ),
                SizedBox(height: spacing),
                
                // Month field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Month',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: labelFontSize,
                        fontFamily: 'JosefinSans',
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    _buildSelectionButton(
                      label: 'Month',
                      value: _englishMonths[_adMonth - 1],
                      onTap: () => _showMonthPicker(context, false),
                    ),
                  ],
                ),
                SizedBox(height: spacing),
                
                // Year field
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Year',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: labelFontSize,
                        fontFamily: 'JosefinSans',
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    _buildSelectionButton(
                      label: 'Year',
                      value: _adYear.toString(),
                      onTap: () => _showYearPicker(context, false),
                    ),
                  ],
                ),
              ],
            )
          else
            // Normal layout with Row for larger screens
            Row(
              children: [
                // Day column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Day',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: labelFontSize,
                          fontFamily: 'JosefinSans',
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 8),
                      _buildSelectionButton(
                        label: 'Day',
                        value: _adDay.toString(),
                        onTap: () => _showDayPicker(context, false),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: spacing),

                // Month column with reduced flex on small screens
                Expanded(
                  flex: isSmallScreen ? 1 : 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Month',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: labelFontSize,
                          fontFamily: 'JosefinSans',
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 8),
                      _buildSelectionButton(
                        label: 'Month',
                        value: _englishMonths[_adMonth - 1],
                        onTap: () => _showMonthPicker(context, false),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: spacing),

                // Year column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Year',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: labelFontSize,
                          fontFamily: 'JosefinSans',
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 8),
                      _buildSelectionButton(
                        label: 'Year',
                        value: _adYear.toString(),
                        onTap: () => _showYearPicker(context, false),
                      ),
                    ],
                  ),
                ),
              ],
            ),

          const SizedBox(height: 24),

          // Convert button
          Center(
            child: ElevatedButton(
              onPressed: _updateADToBSResult,
              style: AppColors.getPrimaryButtonStyle(
                context,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Convert',
                style: TextStyle(
                  fontSize: isSmallScreen ? 14 : 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          SizedBox(height: isSmallScreen ? 24 : 32),
          
          // Conversion result - with responsive text sizes
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.getPrimaryColor(context), width: 1),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Conversion',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: labelFontSize,
                    fontFamily: 'JosefinSans',
                  ),
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),

                // AD Date
                Text(
                  _adResult,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'JosefinSans',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),

                // BS Date
                Text(
                  _bsResult,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 28 : 36,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'JosefinSans',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),

                // Day of week
                Text(
                  _dayOfWeek,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 16 : 18,
                    fontFamily: 'JosefinSans',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
