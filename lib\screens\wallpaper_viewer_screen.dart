import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/wallpaper.dart';
import '../providers/wallpaper_provider.dart';
import '../services/auth_service.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../services/permission_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import '../widgets/permission_banner.dart';
import '../providers/audio_provider.dart';
import '../providers/ad_provider.dart';

class WallpaperViewerScreen extends StatefulWidget {
  final List<Wallpaper> wallpapers;
  final int initialIndex;

  const WallpaperViewerScreen({
    super.key,
    required this.wallpapers,
    required this.initialIndex,
  });

  @override
  State<WallpaperViewerScreen> createState() => _WallpaperViewerScreenState();
}

class _WallpaperViewerScreenState extends State<WallpaperViewerScreen>
    with WidgetsBindingObserver {
  late PageController _pageController;
  bool _isDownloading = false;
  bool _showPermissionBanner = false;
  late ValueNotifier<int> _currentPageNotifier;
  final AuthService _authService = AuthService();
  final PermissionService _permissionService = PermissionService();
  static const platform = MethodChannel('com.studiohabre.hindupath/gallery');

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialIndex);
    _currentPageNotifier = ValueNotifier<int>(widget.initialIndex);

    _pageController.addListener(() {
      if (_pageController.position.isScrollingNotifier.value == false) {
        _currentPageNotifier.value =
            _pageController.page?.round() ?? widget.initialIndex;
      }
    });

    WidgetsBinding.instance.addObserver(this);
    _checkPermissionStatus();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPermissionStatus();
    }
  }

  Future<void> _checkPermissionStatus() async {
    // Force update the permissions status
    await _permissionService.updatePermissionsStatus();
    bool permissionDenied = !await _permissionService.hasStoragePermission;

    debugPrint(
        'Checking storage permission status: granted=${!permissionDenied}');

    if (mounted) {
      setState(() {
        _showPermissionBanner = permissionDenied;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _currentPageNotifier.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _requestStoragePermission() async {
    if (!mounted) return;

    final result = await _permissionService.requestStoragePermission();
    final permissionGranted = result.isGranted;

    if (permissionGranted) {
      setState(() {
        _showPermissionBanner = false;
      });
    } else if (mounted) {
      bool openSettings = await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (context) => PermissionSettingsDialog(
              message:
                  'Please enable storage permission in your device settings to download wallpapers.\n\n'
                  'For Android 11+: Go to App Settings > Permissions > Files and Media > Allow management of all files',
              onResult: (result) => Navigator.pop(context, result),
            ),
          ) ??
          false;

      if (openSettings) {
        await openAppSettings();
      }
    }
  }

  Future<bool> _hasRequiredPermissions() async {
    return await _permissionService.hasRequiredPermissions();
  }

  // Call our native method to save an image file to the gallery
  Future<void> _saveToGallery(String filePath) async {
    try {
      final result = await platform.invokeMethod('saveToGallery', {
        'filePath': filePath,
      });
      print('Save to gallery result: $result');
      return result;
    } catch (e) {
      print('Error saving to gallery: $e');
      throw e;
    }
  }

  Future<void> _downloadWallpaper(Wallpaper wallpaper) async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });

    // Show initial progress indicator to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'Downloading wallpaper...',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1E1E1E),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    File? tempFile;
    try {
      // Force refresh permissions status first
      await _permissionService.updatePermissionsStatus();

      // Check if we already have the required permissions
      final hasPermissions = await _permissionService.hasRequiredPermissions();

      if (!hasPermissions) {
        // If not, request them
        final permissionStatus =
            await _permissionService.requestStoragePermission();

        if (!permissionStatus.isGranted) {
          debugPrint(
              "Storage permission was denied: ${permissionStatus.message}");

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'Storage permission required to download wallpapers',
                  style: TextStyle(color: Colors.white),
                ),
                backgroundColor: const Color(0xFF1E1E1E),
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );

            // Update permission banner state
            await _checkPermissionStatus();

            setState(() {
              _isDownloading = false;
            });
          }
          return;
        }
      }

      // Now we have permissions, update the UI
      setState(() {
        _showPermissionBanner = false;
      });

      debugPrint('Downloading wallpaper: ${wallpaper.url}');

      // Download image with timeout
      final response = await http
          .get(Uri.parse(wallpaper.url))
          .timeout(const Duration(seconds: 30));

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to download image: HTTP ${response.statusCode}');
      }

      if (response.bodyBytes.isEmpty) {
        throw Exception('Downloaded image is empty');
      }

      debugPrint(
          'Image downloaded successfully, size: ${response.bodyBytes.length} bytes');

      // Validate and prepare file
      final fileExtension = wallpaper.filename.split('.').last.toLowerCase();
      if (!['jpg', 'jpeg', 'png'].contains(fileExtension)) {
        throw Exception('Invalid file extension: $fileExtension');
      }

      // Save to temporary file
      final tempDir = await getTemporaryDirectory();
      final uniqueName =
          'hindupath_wallpaper_${DateTime.now().millisecondsSinceEpoch}.$fileExtension';
      tempFile = File('${tempDir.path}/$uniqueName');

      await tempFile.writeAsBytes(response.bodyBytes);
      debugPrint(
          'Temp file saved at: ${tempFile.path}, size: ${await tempFile.length()} bytes');

      if (!await tempFile.exists()) {
        throw Exception('Failed to create temporary file');
      }

      // Save to gallery using platform channel
      await _saveToGallery(tempFile.path);
      debugPrint('Wallpaper successfully saved to gallery');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Wallpaper saved to gallery',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF1E1E1E),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
        // Show interstitial ad after every 5 downloads, unless audio is playing
        final isAudioPlaying =
            Provider.of<AudioProvider>(context, listen: false).isPlaying;
        Provider.of<AdProvider>(context, listen: false)
            .registerWallpaperDownload(isAudioPlaying: isAudioPlaying);
      }
    } catch (e) {
      debugPrint('Error saving wallpaper: $e');
      if (mounted) {
        String errorMessage = 'Failed to save wallpaper';

        // Provide more detailed error messages to help debugging
        if (e.toString().contains('permission')) {
          errorMessage = 'Permission error: Please grant storage access';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Download timed out. Please check your connection';
        } else if (e.toString().contains('Failed to download')) {
          errorMessage = 'Failed to download image. Please try again';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              errorMessage,
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF1E1E1E),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () => _downloadWallpaper(wallpaper),
              textColor: Colors.white,
            ),
          ),
        );
      }
    } finally {
      // Clean up temp file if it exists
      try {
        if (tempFile != null && await tempFile.exists()) {
          await tempFile.delete();
          debugPrint('Temporary file cleaned up');
        }
      } catch (e) {
        debugPrint('Error cleaning up temporary file: $e');
      }

      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  Future<void> _toggleFavorite(BuildContext context, String wallpaperId) async {
    final wallpaperProvider =
        Provider.of<WallpaperProvider>(context, listen: false);
    final isLoggedIn = _authService.currentUser != null;

    if (!isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            'Please sign in to add favorites',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: const Color(0xFF1E1E1E),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
      return;
    }

    try {
      await wallpaperProvider.toggleFavorite(wallpaperId);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            wallpaperProvider.isWallpaperFavorite(wallpaperId)
                ? 'Added to favorites'
                : 'Removed from favorites',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: const Color(0xFF1E1E1E),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          duration: const Duration(seconds: 1),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error updating favorites: $e',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: const Color(0xFF1E1E1E),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: widget.wallpapers.length,
            onPageChanged: (index) {
              _currentPageNotifier.value = index;
            },
            itemBuilder: (context, index) {
              final wallpaper = widget.wallpapers[index];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    // Implement full-screen toggle if desired
                  });
                },
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Hero(
                      tag: 'wallpaper_${wallpaper.id}',
                      child: Center(
                        child: AspectRatio(
                          aspectRatio: 9 /
                              16, // Fixed 9:16 aspect ratio for all wallpapers
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 10,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: CachedNetworkImage(
                              imageUrl: wallpaper.url,
                              fit: BoxFit
                                  .cover, // Use cover to fill the container while maintaining aspect ratio
                              placeholder: (context, url) => const Center(
                                  child: CircularProgressIndicator()),
                              errorWidget: (context, url, error) =>
                                  const Center(
                                child: Icon(Icons.error, color: Colors.red),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back_ios,
                      size: 20, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
          ),

          // Bottom right action buttons
          Positioned(
            bottom: 24,
            right: 16,
            child: Consumer<WallpaperProvider>(
              builder: (context, wallpaperProvider, _) {
                return ValueListenableBuilder<int>(
                  valueListenable: _currentPageNotifier,
                  builder: (context, value, child) {
                    final isLoggedIn = _authService.currentUser != null;
                    final userId = _authService.currentUser?.uid;
                    final wallpaper = widget.wallpapers[value];
                    final isFavorite = isLoggedIn
                        ? wallpaperProvider.isWallpaperFavorite(wallpaper.id)
                        : false;

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Favorite button - only show when logged in
                        if (isLoggedIn)
                          Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.7),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: IconButton(
                              icon: Icon(
                                isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: isFavorite ? Colors.red : Colors.white,
                                size: 28,
                              ),
                              onPressed: () {
                                if (userId != null) {
                                  _toggleFavorite(
                                    context,
                                    wallpaper.id,
                                  );
                                }
                              },
                            ),
                          ),

                        // Download button - adapt position if permission banner is showing
                        Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.download_rounded,
                              color: Colors.white,
                              size: 28,
                            ),
                            onPressed: _isDownloading
                                ? null
                                : () => _downloadWallpaper(wallpaper),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ),

          if (_isDownloading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),

          // Custom positioned permission banner to avoid UI collision
          if (_showPermissionBanner)
            Positioned(
              bottom: 16,
              left: 16,
              // Adjust right margin to avoid collision with download button
              right: 80,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.75),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                      width: 1),
                ),
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: _requestStoragePermission,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      child: Row(
                        children: [
                          Icon(
                            Icons.folder_open,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Storage Access Needed',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'Allow storage access to download wallpapers',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.8),
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white.withOpacity(0.7),
                            size: 14,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
