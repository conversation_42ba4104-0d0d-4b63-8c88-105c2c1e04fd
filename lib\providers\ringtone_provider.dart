import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import '../models/ringtone.dart';
import '../services/ringtone_service.dart';
import '../providers/audio_provider.dart';
import '../services/auth_service.dart';
import '../services/user_preferences_service.dart';
import '../services/audio_manager.dart';

class RingtoneProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RingtoneService _ringtoneService = RingtoneService();
  List<Ringtone> _ringtones = [];
  Set<String> _favoriteRingtones = {};
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _error;
  AudioProvider? _audioProvider;
  final AuthService _authService = AuthService();
  final UserPreferencesService _preferencesService = UserPreferencesService();

  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get error => _error;
  List<Ringtone> get ringtones => _ringtones;
  Set<String> get favoriteRingtones => _favoriteRingtones;
  RingtoneService get ringtoneService => _ringtoneService;

  RingtoneProvider() {
    _loadFavorites();
    loadRingtones();

    // Listen to player state changes to notify listeners
    _ringtoneService.playerStateStream.listen((_) {
      notifyListeners();
    });
  }

  // Method to set the AudioProvider reference
  void setAudioProvider(AudioProvider audioProvider) {
    _audioProvider = audioProvider;
    _ringtoneService.setAudioProvider(audioProvider);
    debugPrint('RingtoneProvider: AudioProvider reference set');
  }

  // Load favorites from Firebase if user is authenticated
  Future<void> _loadFavorites() async {
    try {
      final user = _authService.currentUser;
      // Only load favorites if a user is authenticated
      if (user != null) {
        final favorites =
            await _preferencesService.getFavoriteRingtones(user.uid);
        _favoriteRingtones = Set<String>.from(favorites);
        notifyListeners();
      } else {
        // Clear favorites when no user is authenticated
        _favoriteRingtones = {};
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading ringtone favorites: $e');
    }
  }

  // Toggle favorite status and save to Firebase for authenticated users
  Future<void> toggleFavorite(String ringtoneId) async {
    final user = _authService.currentUser;
    // Return early if not authenticated
    if (user == null) {
      debugPrint('Cannot toggle favorite: User not authenticated');
      return;
    }

    try {
      // Optimistic update - immediately update UI without waiting for server
      final wasAlreadyFavorite = _favoriteRingtones.contains(ringtoneId);

      // Toggle the state locally first
      if (wasAlreadyFavorite) {
        _favoriteRingtones.remove(ringtoneId);
      } else {
        _favoriteRingtones.add(ringtoneId);
      }

      // Notify listeners immediately for responsive UI
      notifyListeners();

      // Then perform the server operation without blocking UI
      if (wasAlreadyFavorite) {
        // Run in microtask to not block UI thread
        Future.microtask(() async {
          try {
            await _preferencesService.removeFavoriteRingtone(
                user.uid, ringtoneId);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            debugPrint('Error removing ringtone from favorites: $e');
            _favoriteRingtones.add(ringtoneId);
            notifyListeners();
          }
        });
      } else {
        Future.microtask(() async {
          try {
            await _preferencesService.addFavoriteRingtone(user.uid, ringtoneId);
          } catch (e) {
            // If server operation fails, revert the optimistic update
            debugPrint('Error adding ringtone to favorites: $e');
            _favoriteRingtones.remove(ringtoneId);
            notifyListeners();
          }
        });
      }
    } catch (e) {
      debugPrint('Error toggling ringtone favorite: $e');
    }
  }

  // Check if ringtone is favorited
  bool isFavorite(String ringtoneId) {
    // Check authentication first
    if (_authService.currentUser == null) return false;
    return _favoriteRingtones.contains(ringtoneId);
  }

  // Get all favorite ringtones
  List<Ringtone> getFavoriteRingtones() {
    // Check if user is authenticated
    if (_authService.currentUser == null) {
      return []; // Return empty list if not authenticated
    }
    return _ringtones
        .where((ringtone) => _favoriteRingtones.contains(ringtone.id))
        .toList();
  }

  // Clear favorites when user logs out
  void clearFavorites() {
    _favoriteRingtones = {};
    notifyListeners();
  }

  // Load favorites after user signs in
  Future<void> loadFavorites() async {
    // Check authentication before loading
    if (_authService.currentUser == null) {
      // Clear favorites if no user is authenticated
      _favoriteRingtones = {};
      notifyListeners();
      return;
    }
    await _loadFavorites();
  }

  Future<void> loadRingtones() async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      debugPrint(
          'RingtoneProvider: Starting to load ringtones from Firestore...');

      final QuerySnapshot snapshot = await _firestore
          .collection('ringtones')
          .orderBy('uploadDate', descending: true)
          .get();

      _ringtones =
          snapshot.docs.map((doc) => Ringtone.fromFirestore(doc)).toList();

      debugPrint('RingtoneProvider: Loaded ${_ringtones.length} ringtones');

      // Initialize the ringtone service with the loaded ringtones
      if (_ringtones.isNotEmpty) {
        try {
          debugPrint('RingtoneProvider: Initializing ringtone service...');
          await _ringtoneService.init(_ringtones);
          _isInitialized = true;
          debugPrint('RingtoneProvider: Ringtone service initialized');
        } catch (e) {
          debugPrint(
              'RingtoneProvider: Error initializing ringtone service: $e');
          _error = 'Failed to initialize ringtone player: $e';
        }
      }

      // Reload favorites after loading ringtones
      await _loadFavorites();

      _error = null;
    } catch (e) {
      debugPrint('RingtoneProvider: Error loading ringtones: $e');
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> playRingtone(int index) async {
    try {
      // Make sure we have ringtones loaded
      if (_ringtones.isEmpty) {
        debugPrint('RingtoneProvider: No ringtones loaded, loading now...');
        await loadRingtones();

        if (_ringtones.isEmpty) {
          throw Exception('No ringtones available');
        }
      }

      // Make sure the index is valid
      if (index < 0 || index >= _ringtones.length) {
        throw Exception('Invalid ringtone index: $index');
      }

      // Completely stop bhajan playback if active
      if (_audioProvider != null) {
        if (_audioProvider!.audioService.isPlaying ||
            _audioProvider!.audioService.getCurrentBhajan() != null) {
          debugPrint(
              'RingtoneProvider: Completely stopping bhajan playback before playing ringtone');

          // First dismiss the mini player - this sets user explicit dismissal flag
          // This is important to ensure the mini player behaves as if user manually dismissed it
          _audioProvider!.dismissMiniPlayer();

          // Small delay to ensure dismissal flag is set
          await Future.delayed(const Duration(milliseconds: 50));

          // Then completely stop playback, hide notification, and release resources
          _audioProvider!.stopPlaybackCompletely();

          // Allow a small delay for resources to be released
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }

      // Force release any existing audio resources
      final audioManager = AudioManager();
      await audioManager.forceStopAndReleasePlayer(AudioType.bhajan);
      await Future.delayed(
          const Duration(milliseconds: 50)); // Ensure release completes

      // Play the ringtone
      debugPrint('RingtoneProvider: Playing ringtone at index $index');
      await _ringtoneService.play(index);
      notifyListeners();
    } catch (e) {
      debugPrint('RingtoneProvider: Error playing ringtone: $e');
      _error = 'Failed to play ringtone: $e';
      notifyListeners();
      rethrow;
    }
  }

  Future<void> pauseRingtone() async {
    try {
      await _ringtoneService.pause();
      notifyListeners();
    } catch (e) {
      debugPrint('RingtoneProvider: Error pausing ringtone: $e');
      rethrow;
    }
  }

  Future<void> resumeRingtone() async {
    try {
      await _ringtoneService.resume();
      notifyListeners();
    } catch (e) {
      debugPrint('RingtoneProvider: Error resuming ringtone: $e');
      rethrow;
    }
  }

  Future<void> playNextRingtone() async {
    try {
      await _ringtoneService.playNext();
      notifyListeners();
    } catch (e) {
      debugPrint('RingtoneProvider: Error playing next ringtone: $e');
      rethrow;
    }
  }

  Future<void> playPreviousRingtone() async {
    try {
      await _ringtoneService.playPrevious();
      notifyListeners();
    } catch (e) {
      debugPrint('RingtoneProvider: Error playing previous ringtone: $e');
      rethrow;
    }
  }

  Future<void> retry() async {
    _error = null;
    notifyListeners();
    await loadRingtones();
  }

  // Method to ensure ringtones are only played in the full-screen player
  Future<void> stopAndReleasePlayer() async {
    try {
      // First pause any playing ringtone
      if (_ringtoneService.isPlaying) {
        await _ringtoneService.pause();
      }

      // Save current ringtones list before disposing
      final currentRingtones = _ringtones;

      // Release resources in the ringtone service
      await _ringtoneService.dispose();

      // Force release of audio resources for both audio types if needed
      final audioManager = AudioManager();
      await audioManager.forceStopAndReleasePlayer(AudioType.bhajan);
      await audioManager.forceStopAndReleasePlayer(AudioType.ringtone);

      // Small delay to ensure resources are fully released
      await Future.delayed(const Duration(milliseconds: 50));

      // Reinitialize the ringtone service with the saved ringtones list
      // This is important because we're reusing the service instance
      if (currentRingtones.isNotEmpty) {
        await _ringtoneService.init(currentRingtones);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('RingtoneProvider: Error stopping and releasing player: $e');
      // Don't rethrow here to avoid cascading errors
    }
  }

  @override
  void dispose() {
    _ringtoneService.dispose();
    super.dispose();
  }
}
