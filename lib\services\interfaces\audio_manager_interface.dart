import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

/// Enum to track which type of audio is currently active
enum AudioType {
  none,
  bhajan,
  ringtone,
}

/// Interface defining the system-level audio manager functionality
abstract class IAudioManager {
  /// The current active audio player instance
  AudioPlayer? get player;
  
  /// The currently active audio type
  AudioType get activeAudioType;
  
  /// Stream of audio type changes
  Stream<AudioType> get audioTypeStream;
  
  /// Initialize the audio manager
  Future<void> initialize();
  
  /// Request an audio player for a specific audio type
  Future<AudioPlayer?> requestPlayer(AudioType requestedType);
  
  /// Release an audio player for a specific type
  Future<void> releasePlayer(AudioType type);
  
  /// Create an AudioSource with MediaItem for a URL
  Future<AudioSource> createAudioSource({
    required String url,
    required String id,
    required String title,
    String? artist,
    Uri? artUri,
    Map<String, dynamic>? extras,
    Map<String, String>? headers,
  });
  
  /// Check if an audio type is active
  bool isAudioTypeActive(AudioType type);
  
  /// Force stop and release the player for a specific type
  Future<void> forceStopAndReleasePlayer(AudioType type);
  
  /// Clean up resources
  Future<void> dispose();
} 