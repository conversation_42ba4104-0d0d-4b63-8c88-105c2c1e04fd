# Playlist Cover Image Feature

## Overview
You can now add custom cover images to your playlists using local images from your device. The images are stored locally and are not uploaded to any server.

## How to Use

1. Open any playlist
2. Tap the three dots menu (⋮) in the top right corner
3. Select "Change Cover"
4. Choose an image from your device's gallery
5. The new cover will be displayed immediately

## Storage Details
- Images are stored in your app's private storage
- Each playlist can have one cover image
- When you delete a playlist, its cover image is also deleted
- Images are not backed up to the cloud

## Supported Image Types
- JPEG/JPG
- PNG

## Permissions
The app requires the following permissions:
- READ_MEDIA_IMAGES (Android 13+)
- READ_EXTERNAL_STORAGE (Android 12 and below)

These permissions are requested only when you try to change a playlist cover for the first time.

## Troubleshooting
If you experience issues:
1. Make sure you've granted storage permissions to the app
2. Try clearing the app cache if images don't load
3. Restart the app if changes don't appear immediately

## Storage Management
The app automatically manages the storage of cover images:
- Old covers are replaced when you change them
- Unused covers are cleaned up when you delete playlists
- Images are optimized for storage efficiency