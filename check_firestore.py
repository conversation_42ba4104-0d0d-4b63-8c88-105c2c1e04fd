import firebase_admin
from firebase_admin import credentials, firestore

# Initialize Firebase
cred = credentials.Certificate('hindupath-studiohabre-firebase-adminsdk.json')
firebase_admin.initialize_app(cred)
db = firestore.client()

# Get all wallpapers and count by category
categories = {}
docs = db.collection('wallpapers').stream()
for doc in docs:
    cat = doc.get('category')
    categories[cat] = categories.get(cat, 0) + 1

print('Wallpaper count by category:')
for cat, count in sorted(categories.items()):
    print(f'{cat}: {count} wallpapers')

# Check for specific deities
for deity in ['Lord <PERSON>', 'Lord <PERSON>', 'Lord <PERSON>', 'Lord <PERSON>']:
    docs = db.collection('wallpapers').where('category', '==', deity).limit(2).stream()
    print(f"\nChecking {deity} wallpapers:")
    found = 0
    for doc in docs:
        found += 1
        data = doc.to_dict()
        print(f"- ID: {doc.id}")
        print(f"  Category: {data.get('category')}")
        print(f"  Has Thumbnail: {'thumbnailUrl' in data}")
        print(f"  Thumbnail URL: {data.get('thumbnailUrl', 'None')}")
    
    print(f"Found {found} wallpapers for {deity}") 