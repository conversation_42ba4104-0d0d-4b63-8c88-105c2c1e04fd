import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import '../models/ringtone.dart';
import '../providers/ringtone_provider.dart';
import '../services/permission_service.dart';
import '../providers/audio_provider.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';
import '../providers/auth_provider.dart';
import '../services/auth_service.dart';
import 'package:permission_handler/permission_handler.dart';
import '../widgets/permission_banner.dart';
import '../providers/ad_provider.dart';

class RingtonePlayerScreen extends StatefulWidget {
  final List<Ringtone> ringtones;
  final int initialIndex;

  const RingtonePlayerScreen({
    super.key,
    required this.ringtones,
    required this.initialIndex,
  });

  @override
  State<RingtonePlayerScreen> createState() => _RingtonePlayerScreenState();
}

class _RingtonePlayerScreenState extends State<RingtonePlayerScreen>
    with SingleTickerProviderStateMixin {
  // Helper method to show error messages
  void _showError(String title, String error, bool showRetry) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '$title: $error',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        duration: _errorSnackBarDuration,
        action: showRetry
            ? SnackBarAction(
                label: 'Retry',
                onPressed: () => _currentIndex == widget.initialIndex
                    ? _retryPlayback()
                    : _retryCurrentRingtone(),
                textColor: Colors.white,
              )
            : null,
      ),
    );
  }

  // Constants
  static const Duration _animationDuration = Duration(milliseconds: 300);
  static const Duration _playbackDelay = Duration(milliseconds: 500);
  static const Duration _snackBarDuration = Duration(seconds: 2);
  static const Duration _errorSnackBarDuration = Duration(seconds: 3);
  static const int _maxRetries = 3;

  // Controllers and services
  late PageController _pageController;
  late AnimationController _animationController;
  final PermissionService _permissionService = PermissionService();
  final AuthService _authService = AuthService();

  // State variables
  late int _currentIndex;
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _wasAudioPlaying = false;
  String? _errorMessage;
  int _retryCount = 0;
  bool _isDownloading = false;
  bool _showPermissionBanner = false;
  double _downloadProgress = 0.0;
  Timer? _playbackStateTimer;
  Timer? _playbackTimer;

  // Asset paths
  final List<String> _artworkPaths = [
    'assets/ringtones_artwork/rtaw(1).png',
    'assets/ringtones_artwork/rtaw (2).png',
    'assets/ringtones_artwork/rtaw (3).png',
  ];

  // Get artwork for a specific index
  String _getArtworkForIndex(int index) {
    return _artworkPaths[index % _artworkPaths.length];
  }

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _checkPermissionStatus();

    // Initialize after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      final ringtoneProvider =
          Provider.of<RingtoneProvider>(context, listen: false);

      // Set audio provider reference
      ringtoneProvider.setAudioProvider(audioProvider);

      // Check and handle bhajan playback state
      _wasAudioPlaying = audioProvider.audioService.isPlaying;
      if (_wasAudioPlaying) {
        audioProvider.pauseBhajan();
        debugPrint('RingtonePlayerScreen: Bhajan was playing, pausing it');
      }

      // Initialize playback
      _initializePlayback();

      // Start periodic check for playback state
      _startPlaybackStateMonitor();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPermissionStatus();
    }
  }

  void _startPlaybackStateMonitor() {
    // Check playback state every 500ms
    _playbackStateTimer =
        Timer.periodic(const Duration(milliseconds: 500), (_) {
      _checkPlaybackState();
    });
  }

  void _checkPlaybackState() {
    if (!mounted) return;

    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);
    final isPlaying = ringtoneProvider.ringtoneService.isPlaying;

    if (isPlaying != _isPlaying) {
      setState(() {
        _isPlaying = isPlaying;
        if (_isPlaying) {
          _isLoading =
              false; // Always ensure loading is false when playback starts
        }
      });

      if (_isPlaying) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  Future<void> _initializePlayback() async {
    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);

    // Ensure the RingtoneProvider has a reference to the AudioProvider
    ringtoneProvider.setAudioProvider(audioProvider);

    // Ensure bhajan player is paused first
    if (audioProvider.audioService.isPlaying) {
      audioProvider.pauseBhajan();
      // Short delay to ensure pause is processed
      await Future.delayed(const Duration(milliseconds: 50));
    }

    setState(() {
      _isLoading = true;
      _isPlaying = false;
      _errorMessage = null;
    });

    try {
      await ringtoneProvider.stopAndReleasePlayer();
      await Future.delayed(const Duration(milliseconds: 100));
      await ringtoneProvider.playRingtone(widget.initialIndex);

      if (mounted) {
        setState(() {
          _isPlaying = ringtoneProvider.ringtoneService.isPlaying;
          _isLoading =
              false; // Always set loading to false after playback attempt
        });

        if (_isPlaying) {
          _animationController.forward();
        }
      }

      debugPrint('RingtonePlayerScreen: Playback initialized successfully');
    } catch (e) {
      debugPrint('RingtonePlayerScreen: Error initializing playback: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
          _isPlaying = false;
          _errorMessage = 'Failed to play ringtone: $e';
        });
      }

      _showError('Failed to play ringtone', e.toString(), true);
    }
  }

  Future<void> _retryPlayback() async {
    if (_retryCount >= _maxRetries) {
      setState(() {
        _errorMessage =
            'Maximum retry attempts reached. Please try again later.';
      });
      return;
    }

    _retryCount++;
    await _initializePlayback();
  }

  @override
  void dispose() {
    _playbackTimer?.cancel();
    _pageController.dispose();
    _animationController.dispose();
    _playbackStateTimer?.cancel();

    // Make sure to release audio resources when this screen is disposed
    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);
    ringtoneProvider.stopAndReleasePlayer();

    super.dispose();
  }

  void _togglePlayPause() {
    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);

    if (ringtoneProvider.ringtoneService.isPlaying) {
      ringtoneProvider.pauseRingtone();
      _animationController.reverse();
      setState(() {
        _isPlaying = false;
      });
    } else {
      ringtoneProvider.resumeRingtone();
      _animationController.forward();
      setState(() {
        _isPlaying = true;
        _isLoading = false;
      });
    }
  }

  void _onPageChanged(int index) {
    // Avoid possible race conditions by checking mounted state immediately
    if (!mounted) return;

    setState(() {
      _currentIndex = index;
      _isLoading = true;
      _errorMessage = null; // Clear any previous error
    });

    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);

    // First stop any currently playing ringtone
    if (ringtoneProvider.ringtoneService.isPlaying) {
      ringtoneProvider.pauseRingtone();
    }

    // Cancel any previous delayed future that might be pending
    _playbackTimer?.cancel();

    _playbackTimer = Timer(_playbackDelay, () async {
      if (!mounted) return;

      try {
        await ringtoneProvider.stopAndReleasePlayer();
        await ringtoneProvider.playRingtone(index);

        if (!mounted) return;

        setState(() {
          _isPlaying = ringtoneProvider.ringtoneService.isPlaying;
          _isLoading = false;
        });

        if (_isPlaying) {
          _animationController.forward();
        } else {
          _animationController.reverse();
        }
      } catch (error) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
          _isPlaying = false;
          _errorMessage = error.toString();
        });

        _showError('Failed to play ringtone', 'Please try again', true);
      }
    });
  }

  // Method to retry playing the current ringtone
  Future<void> _retryCurrentRingtone() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _isPlaying = false;
      _errorMessage = null;
    });

    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);

    try {
      // Force stop and release the player first
      await ringtoneProvider.stopAndReleasePlayer();

      // Wait a moment before trying again
      await Future.delayed(const Duration(milliseconds: 500));

      // Ensure widget is still mounted before proceeding
      if (!mounted) return;

      // Try to play again
      await ringtoneProvider.playRingtone(_currentIndex);

      // Final mounted check before updating UI
      if (!mounted) return;

      setState(() {
        _isPlaying = ringtoneProvider.ringtoneService.isPlaying;
        _isLoading = false;
      });

      if (_isPlaying) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    } catch (e) {
      // Final mounted check before updating error state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _isPlaying = false;
        _errorMessage = e.toString();
      });
      _animationController.reverse();
    }
  }

  Future<void> _checkPermissionStatus() async {
    bool permissionDenied = !await _permissionService.hasStoragePermission;

    if (mounted) {
      setState(() {
        _showPermissionBanner = permissionDenied;
      });
    }
  }

  Future<void> _requestStoragePermission() async {
    final result = await _permissionService.requestStoragePermission();
    final permissionGranted = result.isGranted;

    if (permissionGranted) {
      setState(() {
        _showPermissionBanner = false;
      });
    } else {
      if (mounted) {
        bool openSettings = await showDialog<bool>(
              context: context,
              barrierDismissible: false,
              builder: (context) => PermissionSettingsDialog(
                message:
                    'Please enable storage permission in your device settings to download ringtones.\n\n'
                    'For Android 11+: Go to App Settings > Permissions > Files and Media > Allow management of all files',
                onResult: (result) => Navigator.pop(context, result),
              ),
            ) ??
            false;

        if (openSettings) {
          await openAppSettings();
        }
      }
    }
  }

  Future<bool> _hasRequiredPermissions() async {
    return await _permissionService.hasStoragePermission;
  }

  Future<void> _downloadRingtone() async {
    if (_isDownloading) return;

    if (!await _hasRequiredPermissions()) {
      await _requestStoragePermission();
      if (!await _hasRequiredPermissions()) {
        return;
      }
    }

    setState(() {
      _isDownloading = true;
    });

    try {
      final ringtone = widget.ringtones[_currentIndex];
      final response = await http.get(Uri.parse(ringtone.url));

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to download ringtone: HTTP ${response.statusCode}');
      }

      // Save to public storage
      final directory =
          Directory('/storage/emulated/0/Music/HinduPath_Ringtones');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      final file = File('${directory.path}/${ringtone.filename}');
      await file.writeAsBytes(response.bodyBytes);

      // Notify media scanner to show the file in gallery
      try {
        final uri = Uri.parse('file://${file.path}');
        await const MethodChannel('media_scanner')
            .invokeMethod('scanFile', {'path': file.path});
      } catch (e) {
        debugPrint('Media scanner error: $e');
        // Continue even if scanning fails
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Ringtone downloaded successfully',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.black,
            duration: _snackBarDuration,
          ),
        );
        // Show interstitial ad if audio is not playing
        final isAudioPlaying =
            Provider.of<AudioProvider>(context, listen: false).isPlaying;
        Provider.of<AdProvider>(context, listen: false)
            .showInterstitialAdIfNoAudioPlaying(isAudioPlaying);
      }
    } catch (e) {
      _showError('Failed to download ringtone', e.toString(), false);
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  // Method to handle favorite toggle action
  void _handleFavoriteToggle(String ringtoneId) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);

    // Check if user is signed in as guest
    if (authProvider.isGuest || !authProvider.isSignedIn) {
      // Show message for guest users
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please Log In to add Favorite',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.black,
          duration: _snackBarDuration,
        ),
      );
    } else {
      // Toggle favorite for authenticated users
      final isFavorite = ringtoneProvider.isFavorite(ringtoneId);
      ringtoneProvider.toggleFavorite(ringtoneId);

      // Show confirmation snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isFavorite ? 'Removed from favorites' : 'Added to favorites',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.black,
          duration: _snackBarDuration,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final ringtoneProvider = Provider.of<RingtoneProvider>(context);

    Widget _buildErrorWidget() {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'Playback Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage?.split(':').last.trim() ??
                  'Connection timed out while loading audio',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _retryCurrentRingtone,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // Try next track if available
                    if (_currentIndex < widget.ringtones.length - 1) {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                  child: const Text('Try Next'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Go Back'),
            ),
          ],
        ),
      );
    }

    return WillPopScope(
      // Handle back button press to ensure ringtones are stopped
      onWillPop: () async {
        // Stop ringtone playback when exiting the player
        final ringtoneProvider =
            Provider.of<RingtoneProvider>(context, listen: false);
        await ringtoneProvider.stopAndReleasePlayer();

        // If bhajan was playing before, resume it
        if (_wasAudioPlaying) {
          final audioProvider =
              Provider.of<AudioProvider>(context, listen: false);
          audioProvider.audioService.resume();
          debugPrint(
              'RingtonePlayerScreen: Resuming bhajan playback on back press');
        }

        return true;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            'Now Playing',
            style: AppFontLoader.getPrakrtaStyle(
              fontSize: 22,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.2,
            ),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            onPressed: () async {
              // Stop ringtone playback when exiting the player
              final ringtoneProvider =
                  Provider.of<RingtoneProvider>(context, listen: false);
              await ringtoneProvider.stopAndReleasePlayer();

              Navigator.pop(context);
            },
          ),
        ),
        body: Stack(
          children: [
            // Main content
            _errorMessage != null ? _buildErrorWidget() : _buildPlayerContent(),

            // Permission banner
            if (_showPermissionBanner)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: PermissionBanner(
                  onRequestPermissions: _requestStoragePermission,
                  permissionType: PermissionType.storage,
                  dismissible: true,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerContent() {
    return Consumer<RingtoneProvider>(
      builder: (context, provider, _) {
        final currentRingtone = widget.ringtones[_currentIndex];
        final isFavorite = provider.isFavorite(currentRingtone.id);
        final screenWidth = MediaQuery.of(context).size.width;
        final artworkSize = screenWidth * 0.75;

        return CustomScrollView(
          physics: const NeverScrollableScrollPhysics(),
          slivers: [
            // Artwork section
            SliverToBoxAdapter(
              child: SizedBox(
                height: artworkSize,
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: widget.ringtones.length,
                  onPageChanged: _onPageChanged,
                  padEnds: true,
                  pageSnapping: true,
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (context, index) {
                    final isCurrentPage = index == _currentIndex;

                    return Center(
                      child: Container(
                        width: artworkSize,
                        height: artworkSize,
                        margin: const EdgeInsets.symmetric(horizontal: 15.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.getPrimaryColor(context),
                              const Color(0xFF0D2419),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // Artwork background
                              Positioned.fill(
                                child: Image.asset(
                                  _getArtworkForIndex(index),
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.music_note,
                                      size: 80,
                                      color: Colors.white30,
                                    );
                                  },
                                ),
                              ),

                              // Overlay gradient
                              Positioned.fill(
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Colors.transparent,
                                        Colors.black.withOpacity(0.7),
                                      ],
                                    ),
                                  ),
                                ),
                              ),

                              // Loading indicator - only show when loading AND not playing
                              if (isCurrentPage)
                                Consumer<RingtoneProvider>(
                                  builder: (context, provider, _) {
                                    if (_isLoading &&
                                        !provider.ringtoneService.isPlaying) {
                                      return const CircularProgressIndicator(
                                        color: Colors.white,
                                      );
                                    }
                                    return const SizedBox.shrink();
                                  },
                                ),

                              // Play/Pause button - only show on current page
                              if (isCurrentPage)
                                GestureDetector(
                                  onTap: _togglePlayPause,
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: Colors.white.withOpacity(0.1),
                                        width: 2,
                                      ),
                                    ),
                                    child: Center(
                                      child: AnimatedIcon(
                                        icon: AnimatedIcons.play_pause,
                                        progress: _animationController,
                                        size: 50,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Title section
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.only(
                    top: 32, left: 24, right: 24, bottom: 16),
                child: Text(
                  currentRingtone.title,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),

            // Swipe instruction
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text(
                  'Swipe to browse more ringtones',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

            // Control buttons section
            SliverFillRemaining(
              hasScrollBody: false,
              child: Column(
                children: [
                  // Control buttons row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Previous button
                      _buildControlButton(
                        icon: Icons.skip_previous,
                        onPressed: _currentIndex > 0
                            ? () {
                                _pageController.previousPage(
                                  duration: _animationDuration,
                                  curve: Curves.easeInOut,
                                );
                              }
                            : null,
                        isEnabled: _currentIndex > 0,
                      ),

                      const SizedBox(width: 20),

                      // Favorite button - only show when logged in
                      if (_authService.currentUser != null)
                        _buildControlButton(
                          icon: isFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          onPressed: () =>
                              _handleFavoriteToggle(currentRingtone.id),
                          isEnabled: true,
                          iconColor: isFavorite ? Colors.red : Colors.white,
                        ),

                      if (_authService.currentUser != null)
                        const SizedBox(width: 20),

                      // Download button
                      _buildControlButton(
                        icon: Icons.download,
                        onPressed: _isDownloading ? null : _downloadRingtone,
                        isEnabled: !_isDownloading,
                      ),

                      const SizedBox(width: 20),

                      // Next button
                      _buildControlButton(
                        icon: Icons.skip_next,
                        onPressed: _currentIndex < widget.ringtones.length - 1
                            ? () {
                                _pageController.nextPage(
                                  duration: _animationDuration,
                                  curve: Curves.easeInOut,
                                );
                              }
                            : null,
                        isEnabled: _currentIndex < widget.ringtones.length - 1,
                      ),
                    ],
                  ),

                  // Download progress indicator
                  if (_isDownloading)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 16.0, left: 24.0, right: 24.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          LinearProgressIndicator(
                            value: _downloadProgress,
                            backgroundColor: Colors.grey[800],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.getPrimaryColor(context),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Downloading... ${(_downloadProgress * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),

                  const Spacer(),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // Helper method to build consistent control buttons
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isEnabled,
    Color iconColor = Colors.white,
  }) {
    return Container(
      width: 55,
      height: 55,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: isEnabled ? iconColor : Colors.white.withOpacity(0.3),
          size: 28,
        ),
        onPressed: onPressed,
      ),
    );
  }
}
