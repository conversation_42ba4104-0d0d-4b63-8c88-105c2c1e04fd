import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/bhajan_list_view.dart';
import '../providers/audio_provider.dart';
import '../utils/theme_widgets.dart';
import '../utils/color_utils.dart';
import '../widgets/favorites_bhajan_list.dart';
import '../widgets/playlists_tab.dart';

class BhajanPage extends StatefulWidget {
  const BhajanPage({super.key});

  @override
  State<BhajanPage> createState() => _BhajanPageState();
}

class _BhajanPageState extends State<BhajanPage> with TickerProviderStateMixin {
  late TabController _tabController;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Add listener to detect tab changes and ensure mini player state consistency
    _tabController.addListener(() {
      // Only trigger when the tab selection actually changes
      if (_tabController.indexIsChanging) {
        // Use Future.microtask to ensure this runs after the tab change is complete
        Future.microtask(() {
          final audioProvider = Provider.of<AudioProvider>(context, listen: false);
          // Refresh mini player state to fix any inconsistencies
          audioProvider.refreshMiniPlayerState();
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 14,
            ),
            indicator: ThemedTabIndicator(
              context: context,
              radius: 4,
              indicatorHeight: 3,
              insets: const EdgeInsets.symmetric(horizontal: 20),
            ),
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('All'),
                    SizedBox(width: 8),
                    Icon(Icons.music_note, size: 20),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Favorites'),
                    SizedBox(width: 8),
                    Icon(Icons.favorite, size: 20),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Playlists'),
                    SizedBox(width: 8),
                    Icon(Icons.playlist_play, size: 20),
                  ],
                ),
              ),
            ],
          ),
          // Search widget
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search bhajans...',
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
                prefixIcon: const Icon(Icons.search, color: Colors.white),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Colors.white),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                BhajanListView(
                    searchQuery: _searchQuery), // Bhajans tab with search
                const FavoritesBhajanList(), // Favorites tab
                const PlaylistsTab(), // Playlists tab
              ],
            ),
          ),
        ],
      ),
    );
  }
}
