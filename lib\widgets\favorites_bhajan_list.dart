import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/bhajan.dart';
import '../providers/audio_provider.dart';
import '../services/user_preferences_service.dart';
import '../utils/color_utils.dart';
import 'bhajan_list_item.dart';
import '../screens/player_screen.dart';
import '../services/auth_service.dart';
import '../services/bhajan_player_service.dart';

class FavoritesBhajanList extends StatefulWidget {
  const FavoritesBhajanList({super.key});

  @override
  State<FavoritesBhajanList> createState() => _FavoritesBhajanListState();
}

class _FavoritesBhajanListState extends State<FavoritesBhajanList> {
  final UserPreferencesService _preferencesService = UserPreferencesService();
  final AuthService _authService = AuthService();
  List<String> _favoriteBhajanIds = [];
  bool _isLoading = true;
  bool _isPlayingAction = false; // Track if a play action is in progress

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    try {
      final user = _authService.currentUser;
      if (user != null) {
        final preferences =
            await _preferencesService.getUserPreferences(user.uid);
        if (mounted) {
          setState(() {
            _favoriteBhajanIds = preferences?.favoriteBhajans ?? [];
            _isLoading = false;
          });
        }
      } else {
        // If no user is signed in, show empty favorites
        if (mounted) {
          setState(() {
            _favoriteBhajanIds = [];
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading favorites: $e');
      // Handle error case by setting loading to false
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        if (_isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // Check if user is logged in
        final user = _authService.currentUser;
        if (user == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite,
                  size: 64,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Please Sign In to view your Favorites',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                    fontFamily: 'JosefinSans',
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    // Navigate to login screen
                    Navigator.pushNamed(context, '/login');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.getPrimaryColor(context),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 32, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text(
                    'Sign In',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        if (_favoriteBhajanIds.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_border,
                  size: 48,
                  color: Colors.white70,
                ),
                SizedBox(height: 16),
                Text(
                  'No favorite bhajans yet',
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ],
            ),
          );
        }

        // Filter bhajans to show only favorites
        final favoriteBhajans = audioProvider.bhajans
            .where((bhajan) => _favoriteBhajanIds.contains(bhajan.id))
            .toList();

        // Sort the favorites alphabetically by title
        favoriteBhajans.sort(
            (a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));

        if (favoriteBhajans.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.music_off,
                  size: 48,
                  color: Colors.white70,
                ),
                SizedBox(height: 16),
                Text(
                  'Favorite bhajans not available',
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Play All and Shuffle buttons row
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 4, 16, 8),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _playAllFavorites(audioProvider, favoriteBhajans,
                            shuffle: false);
                      },
                      icon: const Icon(Icons.play_arrow, color: Colors.white),
                      label: const Text('Play All'),
                      style: AppColors.getPrimaryButtonStyle(
                        context,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _playAllFavorites(audioProvider, favoriteBhajans,
                            shuffle: true);
                      },
                      icon: const Icon(Icons.shuffle, color: Colors.white),
                      label: const Text('Shuffle'),
                      style: AppColors.getPrimaryButtonStyle(
                        context,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Favorites list
            Expanded(
              child: ListView.builder(
                itemCount: favoriteBhajans.length,
                padding: EdgeInsets.only(
                  bottom: audioProvider.audioService.getCurrentBhajan() != null &&
                         !audioProvider.isMiniPlayerDismissed
                      ? 70.0 // Mini player height (60) + some extra padding
                      : 10.0, // Default bottom padding
                ),
                itemBuilder: (context, index) {
                  final bhajan = favoriteBhajans[index];
                  return BhajanListItem(
                    bhajan: bhajan,
                    onTap: () => _playBhajan(audioProvider, bhajan),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  void _playBhajan(AudioProvider audioProvider, Bhajan bhajan) {
    // Get the list of favorite bhajans
    final favoriteBhajans = audioProvider.bhajans
        .where((item) => _favoriteBhajanIds.contains(item.id))
        .toList();
    
    BhajanPlayerService.playBhajan(
      audioProvider: audioProvider,
      bhajan: bhajan,
      // Pass favorites list as the source list to ensure only favorites are played
      sourceList: favoriteBhajans,
      updateState: (isPlaying) {
        if (mounted) {
          setState(() {
            _isPlayingAction = isPlaying;
          });
        }
      },
    );
  }

  // Play all favorite bhajans in the list, with option to shuffle
  Future<void> _playAllFavorites(
      AudioProvider audioProvider, List<Bhajan> bhajans,
      {bool shuffle = false}) async {
    
    await BhajanPlayerService.playMultipleBhajans(
      audioProvider: audioProvider,
      bhajans: bhajans,
      updateState: (isPlaying) {
        if (mounted) {
          setState(() {
            _isPlayingAction = isPlaying;
          });
        }
      },
      shuffle: shuffle,
    );
  }
}
