import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/font_loader.dart';
import '../providers/theme_provider.dart';
import '../utils/ui_helpers.dart';
import '../services/permission_service.dart';
import '../utils/color_utils.dart';

class PersonalizationPage extends StatefulWidget {
  const PersonalizationPage({super.key});

  @override
  State<PersonalizationPage> createState() => _PersonalizationPageState();
}

class _PersonalizationPageState extends State<PersonalizationPage> {
  // Predefined theme options with names
  final List<ThemeOption> themeOptions = [
    ThemeOption(
        color: const Color(0xFF1b4332),
        name: '<PERSON> Green',
        description: 'Serene and calming forest-inspired tone'),
    ThemeOption(
        color: const Color(0xFF10B981),
        name: 'Emerald',
        description: 'Vibrant and refreshing green'),
    ThemeOption(
        color: const Color(0xFF6D28D9),
        name: 'Indigo',
        description: 'Spiritual and introspective purple-blue'),
    ThemeOption(
        color: const Color(0xFF7C3AED),
        name: 'Violet',
        description: 'Mystical and creative purple'),
    ThemeOption(
        color: const Color(0xFFEF4444),
        name: '<PERSON>',
        description: 'Passionate and energetic red'),
    ThemeOption(
        color: const Color(0xFFF59E0B),
        name: 'Amber',
        description: 'Warm and inviting golden yellow'),
    ThemeOption(
        color: const Color(0xFF0EA5E9),
        name: 'Ocean',
        description: 'Clear and refreshing blue'),
    ThemeOption(
        color: const Color(0xFF475569),
        name: 'Slate',
        description: 'Sophisticated and neutral gray'),
    ThemeOption(
        color: const Color(0xFF9333EA),
        name: 'Purple',
        description: 'Spiritual and meditative violet'),
  ];

  // Currently selected color
  late Color _selectedColor;
  late bool _notificationsEnabled = true;

  @override
  void initState() {
    super.initState();
    // Initialize with current theme color
    _selectedColor =
        Provider.of<ThemeProvider>(context, listen: false).primaryColor;

    // Load notification status
    _loadNotificationStatus();
  }

  Future<void> _loadNotificationStatus() async {
    final permissionService = PermissionService();
    await permissionService.init();
    final userDenied = permissionService.userHasDeniedNotifications;

    if (mounted) {
      setState(() {
        _notificationsEnabled = !userDenied;
      });
    }
  }

  Future<void> _toggleNotifications(bool enable) async {
    final permissionService = PermissionService();

    if (enable) {
      // User wants to enable notifications
      await permissionService.resetNotificationPreference();
      // Request notification permission
      final result = await permissionService.requestNotificationPermission();

      // Update UI based on actual result
      if (mounted) {
        setState(() {
          _notificationsEnabled = result.isGranted;
        });
      }
    } else {
      // User wants to disable notifications
      await permissionService.setUserNotificationPreference(true);

      if (mounted) {
        setState(() {
          _notificationsEnabled = false;
        });
      }
    }
  }

  void _selectTheme(Color color) {
    setState(() {
      _selectedColor = color;
    });
  }

  Future<void> _applyTheme() async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    // Apply theme changes
    await themeProvider.updateTheme(primaryColor: _selectedColor);

    if (mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
          UIHelpers.getSuccessSnackBar(message: 'Personalization applied'));

      // Navigate back
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: themeProvider.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Personalization',
          style: AppFontLoader.getPrakrtaStyle(
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: themeProvider.backgroundColor,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFFE5E7EB)),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        scrolledUnderElevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Theme preview
                _buildThemePreview(),

                const SizedBox(height: 32),

                // Color selection section
                _buildSectionHeader(
                    icon: Icons.palette_outlined,
                    title: 'Theme Color',
                    description: 'Choose a color theme for the app'),

                const SizedBox(height: 16),

                // Color grid
                _buildColorGrid(),

                const SizedBox(height: 32),

                // Notification Settings
                _buildSectionHeader(
                    icon: Icons.notifications_outlined,
                    title: 'Notifications',
                    description: 'Configure notification preferences'),

                const SizedBox(height: 12),

                // Notification toggle
                _buildNotificationToggle(),

                const SizedBox(height: 32),

                // Reset button
                _buildResetButton(),

                const SizedBox(height: 32),

                // Apply button
                _buildApplyButton(),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThemePreview() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _selectedColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _selectedColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preview',
            style: TextStyle(
              color: _selectedColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // App icon preview
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _selectedColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              // Text preview
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hindu Path',
                      style: AppFontLoader.getPrakrtaStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Your spiritual journey companion',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Button preview
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('PRIMARY'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {},
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _selectedColor,
                    side: BorderSide(color: _selectedColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('SECONDARY'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: themeOptions.length,
      itemBuilder: (context, index) {
        final option = themeOptions[index];
        final isSelected = option.color.value == _selectedColor.value;

        return GestureDetector(
          onTap: () => _selectTheme(option.color),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? option.color : Colors.transparent,
                width: 2,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Color circle
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: option.color,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: option.color.withOpacity(0.5),
                        blurRadius: 8,
                        spreadRadius: 0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 28)
                      : null,
                ),
                const SizedBox(height: 12),
                // Color name
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Text(
                    option.name,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isSelected
                          ? option.color
                          : Colors.white.withOpacity(0.9),
                      fontSize: 14,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: _selectedColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: _selectedColor, size: 22),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationToggle() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Enable Notifications',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _notificationsEnabled
                      ? 'You will receive updates and reminders'
                      : 'You will not receive any notifications',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _notificationsEnabled,
            onChanged: _toggleNotifications,
            activeColor: _selectedColor,
            activeTrackColor: _selectedColor.withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildResetButton() {
    return Center(
      child: TextButton.icon(
        onPressed: () {
          // Reset to default theme
          _selectTheme(ThemeProvider.defaultPrimaryColor);
        },
        icon: const Icon(Icons.refresh_outlined, size: 18),
        label: const Text(
          'Reset to Default Theme',
          style: TextStyle(fontSize: 15),
        ),
        style: TextButton.styleFrom(
          foregroundColor: Colors.white.withOpacity(0.8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: Colors.white.withOpacity(0.3)),
          ),
        ),
      ),
    );
  }

  Widget _buildApplyButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _applyTheme,
        style: ElevatedButton.styleFrom(
          backgroundColor: _selectedColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: _selectedColor.withOpacity(0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          'Apply Changes',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

// Class to store theme options
class ThemeOption {
  final Color color;
  final String name;
  final String description;

  ThemeOption({
    required this.color,
    required this.name,
    required this.description,
  });
}
