import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'dart:async';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';
import '../services/event_service.dart';
import '../utils/tithi_calculator.dart';

class HinduCalendarPage extends StatefulWidget {
  const HinduCalendarPage({super.key});

  @override
  State<HinduCalendarPage> createState() => _HinduCalendarPageState();
}

class _HinduCalendarPageState extends State<HinduCalendarPage>
    with TickerProviderStateMixin {
  // Focus dates
  late DateTime _focusedDay;
  late DateTime? _selectedDay;

  // Nepali date conversion
  late NepaliDateTime _focusedNepaliDay;
  late NepaliDateTime? _selectedNepaliDay;

  // For event markers
  late Set<DateTime> _markedEventDates;

  // For animations
  late AnimationController _animationController;

  // Offline indicator
  bool _isOffline = false;

  // Country selection
  late EventCountry _selectedCountry;
  bool _isLoading = false;

  // Refresh indicator key
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  // Nepali month names in Devanagari
  final List<String> _nepaliMonths = [
    'बैशाख', // Baishakh
    'जेठ', // Jestha
    'असार', // Ashadh
    'श्रावण', // Shrawan
    'भाद्र', // Bhadra
    'आश्विन', // Ashwin
    'कार्तिक', // Kartik
    'मंसिर', // Mangsir
    'पौष', // Poush
    'माघ', // Magh
    'फाल्गुन', // Falgun
    'चैत्र', // Chaitra
  ];

  // English month full names for display
  final List<String> _englishMonths = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];

  // Weekday names in Nepali
  final List<String> _nepaliWeekDays = [
    'आइत', // Sunday
    'सोम', // Monday
    'मङ्गल', // Tuesday
    'बुध', // Wednesday
    'बिही', // Thursday
    'शुक्र', // Friday
    'शनि' // Saturday
  ];

  @override
  void initState() {
    super.initState();

    // Init animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    // Get current date
    final now = DateTime.now();

    // Set initial selected day and focused day to today
    _selectedDay = now;
    _focusedDay = now;

    // Convert to Nepali dates
    _focusedNepaliDay = NepaliDateTime.fromDateTime(now);
    _selectedNepaliDay = _focusedNepaliDay;

    // Initialize the marked event dates as empty
    _markedEventDates = <DateTime>{};

    // Initialize selected country from EventService
    _selectedCountry = EventService.selectedCountry;

    // Register as a listener for country changes
    EventService.addCountryChangeListener(_onCountryChanged);

    // Load events from the EventService
    _loadEvents();

    // Ensure day details are built without using setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Force a rebuild to ensure current day details are shown
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    // Remove the country change listener
    EventService.removeCountryChangeListener(_onCountryChanged);
    // Clear tithi cache when disposing
    TithiCalculator.clearCache();
    super.dispose();
  }

  // Callback for country changes from other parts of the app
  void _onCountryChanged() {
    if (mounted && _selectedCountry != EventService.selectedCountry) {
      debugPrint('Country changed detected in HinduCalendarPage');
      // Update the country and reload events
      setState(() {
        _selectedCountry = EventService.selectedCountry;
      });
      _loadEvents();
      _loadEventMarkers();
    }
  }

  // Load events from service
  Future<void> _loadEvents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize EventService
      await EventService.initialize();

      // Get the current country selection
      _selectedCountry = EventService.selectedCountry;

      // Get all events and update marked dates
      final allEvents = EventService.getAllEvents();

      setState(() {
        // Check if we're offline
        _isOffline = EventService.isOffline();

        // Create a set of normalized dates (without time components)
        _markedEventDates = allEvents
            .map((event) => DateTime(
                  event.date.year,
                  event.date.month,
                  event.date.day,
                ))
            .toSet();

        // Check if selected day has an event
        _updateSelectedDayEvent();
      });
    } catch (e) {
      debugPrint('Error loading events: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Handle manual refresh
  Future<void> _handleRefresh() async {
    // Try to refresh events
    final success = await EventService.refreshEvents();

    if (success) {
      await _loadEvents();

      // Show a brief success animation
      _animationController.forward(from: 0.0);
    }

    return Future.value();
  }

  // Method to handle month navigation
  void _onPageChanged(DateTime focusedDay) {
    debugPrint(
        'Page changed to: ${DateFormat('MMMM yyyy').format(focusedDay)}');

    // Store current day for reference
    final DateTime now = DateTime.now();
    final bool isCurrentMonth =
        focusedDay.year == now.year && focusedDay.month == now.month;

    setState(() {
      _focusedDay = focusedDay;
      _focusedNepaliDay = NepaliDateTime.fromDateTime(focusedDay);

      // If navigating to current month, select current day
      if (isCurrentMonth) {
        _selectedDay = now;
        _selectedNepaliDay = NepaliDateTime.fromDateTime(now);
      }
    });

    // Load any markers for the new month
    _loadEventMarkers();
  }

  // Helper function to check if two dates are the same day
  bool isSameDay(DateTime? date1, DateTime date2) {
    if (date1 == null) return false;
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // Method called when a calendar day is selected
  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    debugPrint('Day selected: ${DateFormat('yyyy-MM-dd').format(selectedDay)}');

    // Only rebuild if the day actually changed
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
        _selectedNepaliDay = NepaliDateTime.fromDateTime(selectedDay);
        _focusedNepaliDay = NepaliDateTime.fromDateTime(focusedDay);
      });

      // Update the events for the newly selected day
      _updateSelectedDayEvent();
    }
  }

  // Method to update selected day event
  void _updateSelectedDayEvent() {
    // Implementation needed
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          const Color(0xFF121212), // Slightly lighter black for better contrast
      appBar: _buildAppBar(),
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _handleRefresh,
        color: AppColors.getPrimaryColor(context),
        backgroundColor: const Color(0xFF1E1E1E),
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),

                  // Country Selector
                  _buildCountrySelector(),

                  // Offline indicator
                  if (_isOffline && !_isLoading) _buildOfflineIndicator(),

                  // Loading indicator
                  if (_isLoading)
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: CircularProgressIndicator(
                          color: AppColors.getPrimaryColor(context),
                        ),
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Calendar header
                  _buildCalendarHeader(),

                  const SizedBox(height: 16),

                  // Calendar Grid
                  _buildCalendarContainer(),

                  const SizedBox(height: 20),

                  // Month events
                  _buildMonthEventsSection(),

                  // Bottom padding for better scrolling experience
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      title: Text(
        'Hindu Calendar',
        style: AppFontLoader.getPrakrtaStyle(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          letterSpacing: 1.2,
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, size: 20),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildCalendarHeader() {
    // Get the appropriate month names based on the focused date
    final nepaliMonthName = _nepaliMonths[_focusedNepaliDay.month - 1];

    // Get current and next English month
    final currentEnglishMonth = _englishMonths[_focusedDay.month - 1];
    final nextMonthIndex = _focusedDay.month % 12;
    final nextEnglishMonth = _englishMonths[nextMonthIndex];
    final englishMonthDisplay = '$currentEnglishMonth/$nextEnglishMonth';

    // Format the years with Devanagari numerals for Nepali year
    final nepaliYear = _getDevanagariNumeral(_focusedNepaliDay.year);
    final englishYear = _focusedDay.year.toString();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Previous month button
              _buildNavigationButton(
                icon: Icons.chevron_left,
                onPressed: _previousMonth,
              ),

              // Center title with month and year
              Expanded(
                child: Column(
                  children: [
                    // Nepali month and year
                    Text(
                      '$nepaliMonthName $nepaliYear',
                      style: AppFontLoader.getPrakrtaStyle(
                        fontSize: 22,
                        color: Colors.white,
                        letterSpacing: 1.2,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    // English month and year
                    Text(
                      '$englishMonthDisplay, $englishYear',
                      style: AppFontLoader.getJosefinStyle(
                        fontSize: 14,
                        color: Colors.white70,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Next month button
              _buildNavigationButton(
                icon: Icons.chevron_right,
                onPressed: _nextMonth,
              ),
            ],
          ),

          // Today button
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: InkWell(
              onTap: _goToToday,
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.getPrimaryColorWithOpacity(context, 0.5),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Today',
                  style: AppFontLoader.getJosefinStyle(
                    fontSize: 12,
                    color: AppColors.getPrimaryColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper to build a modern nav button
  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(30),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onPressed,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppColors.getPrimaryColorWithOpacity(context, 0.3),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            size: 22,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildDayOfWeekHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF222222),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: _nepaliWeekDays.map((day) {
          final bool isWeekend = day == 'आइत' || day == 'शनि';
          return Expanded(
            child: Center(
              child: Text(
                day,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isWeekend
                      ? Colors.red[300]
                      : Colors.white.withOpacity(0.8),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    // Get current Nepali month details
    final int nepaliYear = _focusedNepaliDay.year;
    final int nepaliMonth = _focusedNepaliDay.month;

    // Calculate calendar grid properties once to avoid redundant calculations
    final CalendarGridData gridData =
        _calculateGridData(nepaliYear, nepaliMonth);

    return GridView.builder(
      padding: const EdgeInsets.all(6),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 0.75,
        mainAxisSpacing: 3,
        crossAxisSpacing: 3,
      ),
      itemCount: gridData.itemCount,
      itemBuilder: (context, index) {
        // Calculate the day number considering the offset
        final int dayNumber = index - gridData.firstDayOffset + 1;

        // Only show valid days for the month
        if (dayNumber <= 0 || dayNumber > gridData.daysInMonth) {
          return Container(); // Empty cell
        }

        // Create NepaliDateTime for this day
        final NepaliDateTime nepaliDate =
            NepaliDateTime(nepaliYear, nepaliMonth, dayNumber);

        // Get corresponding Gregorian date
        DateTime gregorianDate = nepaliDate.toDateTime();

        // Determine if this day is selected
        bool isSelected = _selectedNepaliDay != null &&
            _selectedNepaliDay!.year == nepaliYear &&
            _selectedNepaliDay!.month == nepaliMonth &&
            _selectedNepaliDay!.day == dayNumber;

        // Determine if this is today
        NepaliDateTime today = NepaliDateTime.now();
        bool isToday = today.year == nepaliYear &&
            today.month == nepaliMonth &&
            today.day == dayNumber;

        // Check if weekend - in traditional Hindu calendar, Saturday and Sunday are weekends
        // Calculate the weekday based on the actual date
        int weekday = gregorianDate.weekday % 7; // 0 = Sunday, 6 = Saturday
        bool isWeekend = weekday == 0 || weekday == 6; // Sunday or Saturday

        // Check if this day has events
        bool hasEvent = _markedEventDates.contains(
          DateTime(
            gregorianDate.year,
            gregorianDate.month,
            gregorianDate.day,
          ),
        );

        // Custom day builder for Nepali dates with event markers
        return _buildCalendarDay(context, gregorianDate, _focusedDay);
      },
    );
  }

  // Encapsulate all grid calculations to avoid recalculation
  CalendarGridData _calculateGridData(int nepaliYear, int nepaliMonth) {
    // Get first day of month
    final NepaliDateTime firstDayOfMonth =
        NepaliDateTime(nepaliYear, nepaliMonth, 1);

    // Calculate the last day of the month
    final NepaliDateTime firstDayOfNextMonth;
    if (nepaliMonth == 12) {
      firstDayOfNextMonth = NepaliDateTime(nepaliYear + 1, 1, 1);
    } else {
      firstDayOfNextMonth = NepaliDateTime(nepaliYear, nepaliMonth + 1, 1);
    }

    // Convert to DateTime to perform date arithmetic
    final DateTime firstDayNextMonthGregorian =
        firstDayOfNextMonth.toDateTime();
    final DateTime lastDayOfMonthGregorian =
        firstDayNextMonthGregorian.subtract(const Duration(days: 1));

    // Convert back to NepaliDateTime to get the day number
    final NepaliDateTime lastDayOfMonth =
        NepaliDateTime.fromDateTime(lastDayOfMonthGregorian);
    final int daysInMonth = lastDayOfMonth.day;

    // Calculate the weekday of the first day of the month (0 = Sunday, 6 = Saturday)
    final DateTime firstDayGregorian = firstDayOfMonth.toDateTime();
    final int firstDayWeekday =
        firstDayGregorian.weekday % 7; // Convert to 0-based (0 = Sunday)

    // Calculate offset for the first day of the month
    final int firstDayOffset = firstDayWeekday;

    // Calculate the number of rows needed
    final int totalCells = firstDayOffset + daysInMonth;
    final int rowsNeeded =
        (totalCells / 7).ceil(); // Ceiling division to get number of rows
    final int itemCount = rowsNeeded * 7; // Total cells to display

    return CalendarGridData(
      daysInMonth: daysInMonth,
      firstDayOffset: firstDayOffset,
      itemCount: itemCount,
    );
  }

  // Custom day builder for Nepali dates with event markers
  Widget _buildCalendarDay(
      BuildContext context, DateTime date, DateTime focusedDay) {
    // Check if this day is selected
    bool isSelected = _selectedDay != null && isSameDay(_selectedDay, date);

    // Check if this day is today
    bool isToday = isSameDay(date, DateTime.now());

    // Convert the date to Nepali
    NepaliDateTime nepaliDate = NepaliDateTime.fromDateTime(date);
    int dayNumber = nepaliDate.day;

    // Use the original Gregorian date for event checking
    DateTime gregorianDate = date;

    // Check if weekend - in traditional Hindu calendar, Saturday and Sunday are weekends
    // Calculate the weekday based on the actual date
    int weekday = gregorianDate.weekday % 7; // 0 = Sunday, 6 = Saturday
    bool isWeekend = weekday == 0 || weekday == 6; // Sunday or Saturday

    // Check if this day has events
    bool hasEvent = _markedEventDates.contains(
      DateTime(
        gregorianDate.year,
        gregorianDate.month,
        gregorianDate.day,
      ),
    );

    // For today and selected day, get location-based tithi if possible
    if (isToday || isSelected) {
      return FutureBuilder<Map<String, String>>(
        future: TithiCalculator.calculateTithiWithLocation(gregorianDate),
        builder: (context, snapshot) {
          String tithiInitial = '?';

          if (snapshot.connectionState == ConnectionState.done &&
              snapshot.hasData &&
              snapshot.data != null) {
            tithiInitial = snapshot.data!['name']!.substring(0, 1);
          } else {
            // Fallback to basic calculation while loading or on error
            final Map<String, String> fallbackTithiInfo =
                TithiCalculator.calculateTithi(gregorianDate);
            tithiInitial = fallbackTithiInfo['name']!.substring(0, 1);
          }

          return _buildCalendarCell(
            nepaliDay: dayNumber,
            gregorianDay: gregorianDate.day,
            isWeekend: isWeekend,
            isSelected: isSelected,
            isToday: isToday,
            hasEvent: hasEvent,
            tithiInitial: tithiInitial,
          );
        },
      );
    } else {
      // For other days, use the standard calculation to improve performance
      final Map<String, String> tithiInfo =
          TithiCalculator.calculateTithi(gregorianDate);
      final String tithiInitial = tithiInfo['name']!.substring(0, 1);

      return _buildCalendarCell(
        nepaliDay: dayNumber,
        gregorianDay: gregorianDate.day,
        isWeekend: isWeekend,
        isSelected: isSelected,
        isToday: isToday,
        hasEvent: hasEvent,
        tithiInitial: tithiInitial,
      );
    }
  }

  Widget _buildCalendarCell({
    required int nepaliDay,
    required int gregorianDay,
    required bool isWeekend,
    required bool isSelected,
    required bool isToday,
    required bool hasEvent,
    required String tithiInitial,
  }) {
    String nepaliDayText = _getDevanagariNumeral(nepaliDay);

    Color textColor;
    if (isSelected) {
      textColor = Colors.white;
    } else if (isWeekend) {
      textColor = Colors.red[300]!;
    } else {
      textColor = Colors.white;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _onDayTapped(nepaliDay),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          margin: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.getPrimaryColor(context).withOpacity(0.9),
                      AppColors.getPrimaryColor(context).withOpacity(0.7),
                    ],
                  )
                : null,
            color: isToday && !isSelected
                ? AppColors.getPrimaryColorWithOpacity(context, 0.15)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isToday && !isSelected
                  ? AppColors.getPrimaryColor(context)
                  : isSelected
                      ? Colors.transparent
                      : Colors.grey.withOpacity(0.15),
              width: isToday && !isSelected ? 1.5 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.getPrimaryColorWithOpacity(context, 0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Stack(
            children: [
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      nepaliDayText,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: (isSelected || isToday)
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      gregorianDay.toString(),
                      style: TextStyle(
                        fontSize: 10,
                        color: isSelected
                            ? Colors.white.withOpacity(0.9)
                            : Colors.grey,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              if (hasEvent)
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.white : Colors.red[400],
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMonthEventsList() {
    // Get screen width for responsive layout
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    // Get all events for the currently displayed month
    List<HinduEvent> monthEvents = [];

    // Get the date range for the current month view
    DateTime startDate;
    DateTime endDate;

    // Get first and last day of the Nepali month
    final firstDayOfMonth =
        NepaliDateTime(_focusedNepaliDay.year, _focusedNepaliDay.month, 1);
    final lastDayOfMonth = _getLastDayOfNepaliMonth(
        _focusedNepaliDay.year, _focusedNepaliDay.month);

    // Convert to Gregorian dates
    startDate = firstDayOfMonth.toDateTime();
    endDate = lastDayOfMonth.toDateTime();

    // Get events for this date range
    monthEvents = EventService.getEventsForDateRange(startDate, endDate);

    // Sort events by date
    monthEvents.sort((a, b) => a.date.compareTo(b.date));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Month Events Header
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.getPrimaryColorWithOpacity(context, 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.event_note,
                size: 20,
                color: AppColors.getPrimaryColor(context),
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_nepaliMonths[_focusedNepaliDay.month - 1]} Events',
                  style: AppFontLoader.getPrakrtaStyle(
                    fontSize: 18,
                    color: Colors.white,
                    letterSpacing: 0.5,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${DateFormat('MMM d').format(startDate)} - ${DateFormat('MMM d').format(endDate)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white60,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Events list
        monthEvents.isNotEmpty
            ? ListView.separated(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: monthEvents.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final event = monthEvents[index];
                  final isSelectedDayEvent = _selectedDay != null &&
                      isSameDay(_selectedDay!, event.date);
                  final nextUpcoming = EventService.getNextUpcomingEvent();
                  final isNextUpcomingEvent = nextUpcoming != null &&
                      isSameDay(nextUpcoming.date, event.date);

                  // Format dates
                  final formattedDate = DateFormat('MMMM d').format(event.date);
                  final nepaliDate = _formatNepaliDate(event.date);

                  // Build event item
                  return Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(10),
                    clipBehavior: Clip.antiAlias,
                    child: InkWell(
                      onTap: () {
                        _onDaySelected(event.date, _focusedDay);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                          color: isSelectedDayEvent
                              ? AppColors.getPrimaryColorWithOpacity(
                                  context, 0.15)
                              : const Color(0xFF222222),
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: isSelectedDayEvent
                                ? AppColors.getPrimaryColor(context)
                                : isNextUpcomingEvent
                                    ? Colors.amber.withOpacity(0.7)
                                    : Colors.transparent,
                            width: 1.5,
                          ),
                        ),
                        child: Row(
                          children: [
                            // Left date indicator
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: isNextUpcomingEvent
                                    ? Colors.amber.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.3),
                                border: Border.all(
                                  color: isNextUpcomingEvent
                                      ? Colors.amber.withOpacity(0.7)
                                      : Colors.grey.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  event.date.day.toString(),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: isNextUpcomingEvent
                                        ? Colors.amber
                                        : Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: isSmallScreen ? 8 : 12),

                            // Event details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    event.name,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: isSelectedDayEvent ||
                                              isNextUpcomingEvent
                                          ? FontWeight.bold
                                          : FontWeight.w500,
                                      color: isNextUpcomingEvent
                                          ? Colors.amber
                                          : Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Row(
                                    children: [
                                      Text(
                                        formattedDate,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: isSelectedDayEvent
                                              ? AppColors.getPrimaryColor(
                                                      context)
                                                  .withOpacity(0.7)
                                              : Colors.white60,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        nepaliDate,
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.white.withOpacity(0.4),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Next indicator
                            if (isNextUpcomingEvent)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 3,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.amber.withOpacity(0.5),
                                    width: 1,
                                  ),
                                ),
                                child: const Text(
                                  'NEXT',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.amber,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )
            : Container(
                padding: const EdgeInsets.symmetric(vertical: 24),
                alignment: Alignment.center,
                child: Column(
                  children: [
                    Icon(
                      Icons.event_busy,
                      size: 40,
                      color: Colors.grey.withOpacity(0.5),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'No events this month',
                      style: AppFontLoader.getJosefinStyle(
                        fontSize: 16,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
      ],
    );
  }

  // Helper to get date range string for a Nepali month
  String _getDateRangeForNepaliMonth(int year, int month) {
    // Get first and last day of Nepali month
    final firstDay = NepaliDateTime(year, month, 1);
    final lastDay = _getLastDayOfNepaliMonth(year, month);

    // Convert to Gregorian
    final firstDayGreg = firstDay.toDateTime();
    final lastDayGreg = lastDay.toDateTime();

    // Format date range
    return '${DateFormat('MMM d').format(firstDayGreg)} - ${DateFormat('MMM d').format(lastDayGreg)}';
  }

  // Helper method to get the last day of a Nepali month
  NepaliDateTime _getLastDayOfNepaliMonth(int year, int month) {
    debugPrint(
        'Calculating last day of Nepali month: ${_nepaliMonths[month - 1]} $year');

    // Define a more accurate mapping of Nepali months to days based on the Vikram Samvat calendar
    // These values are approximate and may vary slightly in different years
    final Map<int, int> daysInNepaliMonth = {
      1: 31, // Baisakh (mid-April to mid-May)
      2: 31, // Jestha (mid-May to mid-June)
      3: 31, // Ashadh (mid-June to mid-July)
      4: 31, // Shrawan (mid-July to mid-August)
      5: 31, // Bhadra (mid-August to mid-September)
      6: 30, // Ashwin (mid-September to mid-October)
      7: 30, // Kartik (mid-October to mid-November)
      8: 29, // Mangsir (mid-November to mid-December)
      9: 29, // Poush (mid-December to mid-January)
      10: 29, // Magh (mid-January to mid-February)
      11: 30, // Falgun (mid-February to mid-March)
      12: 30, // Chaitra (mid-March to mid-April)
    };

    try {
      // Most accurate method: Calculate the next month's first day and go back one day
      final nextMonth = month == 12 ? 1 : month + 1;
      final nextYear = month == 12 ? year + 1 : year;

      // First day of next month
      final nextMonthFirstDay = NepaliDateTime(nextYear, nextMonth, 1);
      debugPrint(
          'First day of next month (${_nepaliMonths[nextMonth - 1]} $nextYear): ${DateFormat('yyyy-MM-dd').format(nextMonthFirstDay.toDateTime())}');

      // Last day of current month is one day before first day of next month
      final lastDay =
          nextMonthFirstDay.toDateTime().subtract(const Duration(days: 1));

      // Convert back to Nepali
      final lastDayNepali = NepaliDateTime.fromDateTime(lastDay);

      debugPrint(
          'Last day of ${_nepaliMonths[month - 1]} $year: ${lastDayNepali.day} (${DateFormat('yyyy-MM-dd').format(lastDay)})');
      return lastDayNepali;
    } catch (e) {
      debugPrint('Error calculating last day of month: $e');

      // Fallback 1: Try using the days lookup
      try {
        final daysInMonth = daysInNepaliMonth[month] ?? 30;
        debugPrint(
            'Using fallback days for ${_nepaliMonths[month - 1]}: $daysInMonth days');
        return NepaliDateTime(year, month, daysInMonth);
      } catch (e2) {
        debugPrint('Fallback 1 failed: $e2');

        // Fallback 2: Try one day less
        try {
          final daysInMonth = (daysInNepaliMonth[month] ?? 30) - 1;
          debugPrint('Using reduced fallback: ${daysInMonth} days');
          return NepaliDateTime(year, month, daysInMonth);
        } catch (e3) {
          debugPrint('All fallbacks failed, defaulting to day 29');
          // Ultimate fallback - almost all Nepali months have at least 29 days
          return NepaliDateTime(year, month, 29);
        }
      }
    }
  }

  // Helper to format Nepali date in readable format
  String _formatNepaliDate(DateTime gregorianDate) {
    try {
      final nepaliDate = NepaliDateTime.fromDateTime(gregorianDate);
      return '(${_nepaliMonths[nepaliDate.month - 1]} ${nepaliDate.day})';
    } catch (e) {
      return '';
    }
  }

  // Handle day tapped
  void _onDayTapped(int nepaliDay) {
    // Create the corresponding Nepali date
    final selectedNepaliDate = NepaliDateTime(
        _focusedNepaliDay.year, _focusedNepaliDay.month, nepaliDay);

    // Convert to Gregorian date
    final selectedGregorianDate = selectedNepaliDate.toDateTime();

    debugPrint(
        'Day tapped: $nepaliDay, Gregorian date: ${DateFormat('yyyy-MM-dd').format(selectedGregorianDate)}');

    // Use our common handler
    _onDaySelected(selectedGregorianDate, _focusedDay);
  }

  // Navigation methods
  void _previousMonth() {
    _onPageChanged(_getPreviousMonthDate(_focusedDay));
  }

  void _nextMonth() {
    _onPageChanged(_getNextMonthDate(_focusedDay));
  }

  void _goToToday() {
    final DateTime now = DateTime.now();
    final NepaliDateTime nowNepali = NepaliDateTime.fromDateTime(now);

    setState(() {
      // Set Gregorian date
      _selectedDay = now;
      _focusedDay = now;

      // Set Nepali date directly (not derived from Gregorian)
      _selectedNepaliDay = nowNepali;
      _focusedNepaliDay = nowNepali;

      debugPrint('Going to today: ${DateFormat('yyyy-MM-dd').format(now)}');
      debugPrint(
          'Nepali date: ${_nepaliMonths[nowNepali.month - 1]} ${nowNepali.day}, ${nowNepali.year}');

      // Reload event markers
      _loadEventMarkers();
    });
  }

  String _getDevanagariNumeral(int number) {
    // Map of English digits to Devanagari digits
    const Map<String, String> devanagariDigits = {
      '0': '०',
      '1': '१',
      '2': '२',
      '3': '३',
      '4': '४',
      '5': '५',
      '6': '६',
      '7': '७',
      '8': '८',
      '9': '९',
    };

    // Convert the number to a string
    String numStr = number.toString();

    // Replace each digit with its Devanagari equivalent
    String result = '';
    for (int i = 0; i < numStr.length; i++) {
      result += devanagariDigits[numStr[i]] ?? numStr[i];
    }

    return result;
  }

  // Change country and reload events with smoother transition
  Future<void> _changeCountry(EventCountry country) async {
    if (_selectedCountry == country) return;

    // Only update the country indicator in the UI first, without showing a loading state
    setState(() {
      _selectedCountry = country;
    });

    try {
      // Update country in service first (data layer)
      await EventService.setCountry(country);

      // Start loading the data in the background
      // We'll use a local variable to track if we need to update the UI
      bool dataLoadComplete = false;
      Set<DateTime> newMarkedDates = {};

      // Force refresh events from assets
      await EventService.refreshEvents();

      // Get new events data
      final allEvents = EventService.getAllEvents();
      newMarkedDates = allEvents
          .map((event) => DateTime(
                event.date.year,
                event.date.month,
                event.date.day,
              ))
          .toSet();

      // Only show loading state if operation is taking too long (over 2 seconds)
      Timer? loadingTimer = Timer(const Duration(milliseconds: 2000), () {
        if (!dataLoadComplete && mounted) {
          setState(() {
            _isLoading = true;
          });
        }
      });

      // Update the UI once with all changes at once
      if (mounted) {
        setState(() {
          _markedEventDates = newMarkedDates;
          _isLoading = false;
        });

        // Load markers in a non-blocking way
        _loadEventMarkers();
      }

      // Mark as complete and cancel the timer if it hasn't fired
      dataLoadComplete = true;
      loadingTimer.cancel();

      // Debug output
      debugPrint(
          'Country changed to: ${country == EventCountry.india ? 'India' : 'Nepal'}');
      debugPrint('Events loaded: ${_markedEventDates.length} marked dates');
    } catch (e) {
      debugPrint('Error changing country: $e');
      // Only show error state if something goes wrong
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Load event markers for the current month view
  void _loadEventMarkers() {
    // Initialize an empty set
    final markedDates = <DateTime>{};

    // Get the date range for the current month view
    DateTime startDate;
    DateTime endDate;

    // Get the first and last day of the current Nepali month
    final firstDayOfMonth =
        NepaliDateTime(_focusedNepaliDay.year, _focusedNepaliDay.month, 1);
    final lastDayOfMonth = _getLastDayOfNepaliMonth(
        _focusedNepaliDay.year, _focusedNepaliDay.month);

    // Convert to Gregorian dates
    startDate = firstDayOfMonth.toDateTime();
    endDate = lastDayOfMonth.toDateTime();

    // Get all events in this date range
    final monthEvents = EventService.getEventsForDateRange(startDate, endDate);

    // Add the dates to the marked dates set
    for (final event in monthEvents) {
      // Normalize date (remove time component)
      final eventDate = DateTime(
        event.date.year,
        event.date.month,
        event.date.day,
      );
      markedDates.add(eventDate);
    }

    // Update the marked dates
    setState(() {
      _markedEventDates = markedDates;
    });
  }

  // Helper method to get the date for the previous month
  DateTime _getPreviousMonthDate(DateTime date) {
    if (date.month == 1) {
      return DateTime(date.year - 1, 12, 1);
    } else {
      return DateTime(date.year, date.month - 1, 1);
    }
  }

  // Helper method to get the date for the next month
  DateTime _getNextMonthDate(DateTime date) {
    if (date.month == 12) {
      return DateTime(date.year + 1, 1, 1);
    } else {
      return DateTime(date.year, date.month + 1, 1);
    }
  }

  // Build the country selector with a cleaner design
  Widget _buildCountrySelector() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4.0),
            child: Text(
              'Calendar Region',
              style: AppFontLoader.getJosefinStyle(
                fontSize: 15,
                color: Colors.white.withOpacity(0.9),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildCustomToggle(),
        ],
      ),
    );
  }

  // Animated toggle switch
  Widget _buildCustomToggle() {
    return Container(
      width: 170,
      height: 42,
      decoration: BoxDecoration(
        color: const Color(0xFF111111),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Animated selection indicator
          AnimatedPositioned(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            left: _selectedCountry == EventCountry.india ? 0 : 85,
            top: 0,
            bottom: 0,
            width: 85,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.getPrimaryColor(context),
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),

          // Toggle buttons row
          Row(
            children: [
              // India button
              Expanded(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isLoading
                        ? null
                        : () => _changeCountry(EventCountry.india),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25),
                      bottomLeft: Radius.circular(25),
                    ),
                    highlightColor: Colors.transparent,
                    splashColor: Colors.white.withOpacity(0.1),
                    child: Center(
                      child: AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 200),
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'JosefinSans',
                          fontSize: 14,
                          fontWeight: _selectedCountry == EventCountry.india
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                        child: const Text('India'),
                      ),
                    ),
                  ),
                ),
              ),

              // Nepal button
              Expanded(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _isLoading
                        ? null
                        : () => _changeCountry(EventCountry.nepal),
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(25),
                      bottomRight: Radius.circular(25),
                    ),
                    highlightColor: Colors.transparent,
                    splashColor: Colors.white.withOpacity(0.1),
                    child: Center(
                      child: AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 200),
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'JosefinSans',
                          fontSize: 14,
                          fontWeight: _selectedCountry == EventCountry.nepal
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                        child: const Text('Nepal'),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build a more attractive offline indicator
  Widget _buildOfflineIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F1E15),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.amber.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.wifi_off, color: Colors.amber, size: 18),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              'Offline mode active. Pull to refresh when online.',
              style: TextStyle(
                color: Colors.amber[200],
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build a container for the calendar
  Widget _buildCalendarContainer() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDayOfWeekHeader(),
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
            child: _buildCalendarGrid(),
          ),
        ],
      ),
    );
  }

  // Build month events section with improved styling
  Widget _buildMonthEventsSection() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _buildMonthEventsList(),
      ),
    );
  }

  // Helper to determine Nepali month name for a Gregorian date
  String _getNepaliMonthForGregorianDate(DateTime gregorianDate) {
    try {
      final nepaliDate = NepaliDateTime.fromDateTime(gregorianDate);
      final int nepaliMonth = nepaliDate.month;

      return _nepaliMonths[nepaliMonth - 1];
    } catch (e) {
      debugPrint('Error getting Nepali month: $e');
      return 'Unknown';
    }
  }

  // Helper to create an icon container (reducing duplicate code)
  Widget _buildIconContainer({
    required IconData icon,
    Color? iconColor,
    bool smallSize = false,
  }) {
    final double containerSize = smallSize ? 32 : 40;
    final double iconSize = smallSize ? 16 : 20;

    return Container(
      padding: EdgeInsets.all(smallSize ? 6 : 8),
      width: containerSize,
      height: containerSize,
      decoration: BoxDecoration(
        color: AppColors.getPrimaryColorWithOpacity(context, 0.1),
        borderRadius: BorderRadius.circular(smallSize ? 6 : 8),
      ),
      child: Center(
        child: Icon(
          icon,
          size: iconSize,
          color: iconColor ?? AppColors.getPrimaryColor(context),
        ),
      ),
    );
  }
}

// Data class to store grid calculations and avoid redundant calculations
class CalendarGridData {
  final int daysInMonth;
  final int firstDayOffset;
  final int itemCount;

  CalendarGridData({
    required this.daysInMonth,
    required this.firstDayOffset,
    required this.itemCount,
  });
}
