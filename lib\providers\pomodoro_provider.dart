import 'dart:async';
import 'package:flutter/foundation.dart';

enum PomodoroStatus {
  focus,
  shortBreak,
  longBreak,
  paused,
}

class PomodoroProvider with ChangeNotifier {
  Timer? _timer;
  int _currentDuration = 25 * 60; // 25 minutes in seconds
  int _elapsedSeconds = 0;
  PomodoroStatus _status = PomodoroStatus.paused;

  // Settings
  int focusDuration = 25; // minutes
  int shortBreakDuration = 5; // minutes
  int longBreakDuration = 15; // minutes
  int sessionsBeforeLongBreak = 4;
  int completedSessions = 0;

  bool get isRunning => _timer?.isActive ?? false;
  PomodoroStatus get status => _status;
  int get currentDuration => _currentDuration;
  int get elapsedSeconds => _elapsedSeconds;
  double get progress => _elapsedSeconds / _currentDuration;

  void startTimer() {
    if (_timer?.isActive ?? false) return;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_elapsedSeconds < _currentDuration) {
        _elapsedSeconds++;
        notifyListeners();
      } else {
        _completeSession();
      }
    });

    _status = PomodoroStatus.focus;
    notifyListeners();
  }

  void pauseTimer() {
    _timer?.cancel();
    _status = PomodoroStatus.paused;
    notifyListeners();
  }

  void resetTimer() {
    _timer?.cancel();
    _elapsedSeconds = 0;
    _status = PomodoroStatus.paused;
    notifyListeners();
  }

  void _completeSession() {
    _timer?.cancel();
    _elapsedSeconds = 0;
    completedSessions++;

    if (_status == PomodoroStatus.focus) {
      if (completedSessions % sessionsBeforeLongBreak == 0) {
        _status = PomodoroStatus.longBreak;
        _currentDuration = longBreakDuration * 60;
      } else {
        _status = PomodoroStatus.shortBreak;
        _currentDuration = shortBreakDuration * 60;
      }
    } else {
      _status = PomodoroStatus.focus;
      _currentDuration = focusDuration * 60;
    }

    notifyListeners();
  }

  void updateSettings({
    int? newFocusDuration,
    int? newShortBreakDuration,
    int? newLongBreakDuration,
    int? newSessionsBeforeLongBreak,
  }) {
    focusDuration = newFocusDuration ?? focusDuration;
    shortBreakDuration = newShortBreakDuration ?? shortBreakDuration;
    longBreakDuration = newLongBreakDuration ?? longBreakDuration;
    sessionsBeforeLongBreak =
        newSessionsBeforeLongBreak ?? sessionsBeforeLongBreak;

    // Update current duration if timer is not running
    if (!isRunning) {
      _currentDuration = focusDuration * 60;
      _elapsedSeconds = 0;
    }

    notifyListeners();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
