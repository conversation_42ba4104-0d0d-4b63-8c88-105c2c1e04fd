import 'package:flutter/material.dart';

class IconCacheService {
  static final Map<String, ImageProvider> _cache = {};
  static bool _initialized = false;

  static Future<void> initialize(BuildContext context) async {
    if (_initialized) return;

    try {
      final icons = [
        'assets/icons/avatar.png',
        'assets/icons/focus.png',
        // Add other custom icons here
      ];

      await Future.wait(
        icons.map((icon) => precacheImage(
              AssetImage(icon),
              context,
              size: const Size(24, 24),
            ).catchError((e) {
              print('Error precaching icon $icon: $e');
              return null;
            })),
      );

      _initialized = true;
    } catch (e) {
      print('Error initializing icon cache: $e');
      // Mark as initialized anyway to prevent repeated failures
      _initialized = true;
    }
  }

  static ImageProvider getIcon(String assetPath) {
    try {
      if (!_cache.containsKey(assetPath)) {
        _cache[assetPath] = AssetImage(assetPath);
      }
      return _cache[assetPath]!;
    } catch (e) {
      print('Error getting icon $assetPath: $e');
      // Return a placeholder asset image
      return const AssetImage('assets/images/placeholder.png');
    }
  }

  static void clearCache() {
    _cache.clear();
    _initialized = false;
  }
}
