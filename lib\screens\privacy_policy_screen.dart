import 'package:flutter/material.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final primaryColor = AppColors.getPrimaryColor(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Privacy Policy',
          style: AppFontLoader.getPrakrtaStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: const Color(0xFF0D0D0D),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Privacy Policy',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '1. Information We Collect',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'We collect the following information when you use Hindu Path app:\n'
              '• Device information for app functionality\n'
              '• App usage statistics for improving user experience\n'
              '• Downloaded content information (bhajans, wallpapers, DPs)\n'
              '• Google account information (if you sign in with Google)\n'
              '• Local storage data for offline access\n'
              '• Notification preferences and settings',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '2. How We Use Your Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'We use the collected information to:\n'
              '• Provide and maintain our services\n'
              '• Send scheduled notifications for:\n'
              '  - Daily bhajan reminders (6 AM)\n'
              '  - New wallpapers updates (Sunday, Tuesday, Friday at 7 AM)\n'
              '  - New DP updates (Sunday at 8 AM)\n'
              '• Cache audio files for offline playback\n'
              '• Store your favorite content locally\n'
              '• Improve app performance and user experience\n'
              '• Analyze app usage patterns',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '3. Data Storage and Security',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Audio Content: We cache bhajans and audio files locally for offline access\n'
              '• Images: We store downloaded wallpapers and DPs in your device\'s storage\n'
              '• User Data: Your preferences and settings are stored securely\n'
              '• Google Sign-in: We use secure authentication through Firebase\n'
              '• Local Storage: We use device storage for offline content and app settings',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '4. Permissions We Request',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Storage: To save downloaded content (wallpapers, DPs, audio)\n'
              '• Notifications: To send scheduled reminders and updates\n'
              '• Internet: To download content and sync data\n'
              '• Media: To play audio content in background\n'
              '• Camera: To set wallpapers and DPs',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '5. Your Rights and Choices',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'You can:\n'
              '• Disable notifications in app settings\n'
              '• Clear cached content through app settings\n'
              '• Delete downloaded content\n'
              '• Sign out of your Google account\n'
              '• Request deletion of your account and data (GDPR compliance)\n'
              '• Uninstall the app to remove all local data',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '6. Data Deletion (GDPR Compliance)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Under GDPR and similar regulations, you have the right to request deletion of your personal data:\n'
              '• You can delete your account and associated data by selecting "Delete My Account" in the app\'s menu\n'
              '• Upon account deletion, we will permanently remove your user profile, preferences, favorites, and any other stored data\n'
              '• Data that cannot be deleted immediately will be removed within 30 days\n'
              '• Some anonymized analytical data may be retained as it cannot be connected to your identity',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '7. Age Restrictions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Hindu Path is suitable for all ages. However, per Google Play Store requirements:\n'
              '• Users must be at least 13 years old to use the app with a Google account\n'
              '• The app does not target children under 13 years old specifically\n'
              '• Parents and guardians should supervise children\'s use of the app\n'
              '• Content is rated appropriate for all ages according to international rating standards',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              '8. Contact Us',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'If you have any questions about this Privacy Policy or to request data deletion, please contact us at:\n'
              'Email: <EMAIL>',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
