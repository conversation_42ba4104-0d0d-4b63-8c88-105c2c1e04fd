import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/playlist.dart';
import '../models/bhajan.dart';

class PlaylistService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection reference for playlists
  CollectionReference get _playlistsCollection =>
      _firestore.collection('playlists');

  // Get a playlist by ID
  Future<Playlist?> getPlaylist(String playlistId) async {
    try {
      final doc = await _playlistsCollection.doc(playlistId).get();
      if (doc.exists) {
        return Playlist.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting playlist: $e');
      rethrow;
    }
  }

  // Get all playlists for a user
  Future<List<Playlist>> getUserPlaylists(String userId) async {
    try {
      print('Getting playlists for user: $userId');

      // Option 1: Try with the full query first (requires proper index)
      try {
        final querySnapshot = await _playlistsCollection
            .where('userId', isEqualTo: userId)
            .orderBy('updatedAt', descending: true)
            .get(const GetOptions(source: Source.server));

        print('Successful query with sorting by updatedAt');
        return _processQueryResults(querySnapshot);
      } catch (indexError) {
        // Check if it's an index error
        print('Index error occurred: $indexError');
        if (indexError.toString().contains('index')) {
          print('Falling back to simpler query without sorting');

          // Option 2: Fallback to a simpler query that doesn't require an index
          final querySnapshot = await _playlistsCollection
              .where('userId', isEqualTo: userId)
              .get(const GetOptions(source: Source.server));

          return _processQueryResults(querySnapshot);
        } else {
          // If it's not an index error, rethrow
          rethrow;
        }
      }
    } catch (e) {
      print('Error getting user playlists: $e');
      print('Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  // Helper method to process query results
  List<Playlist> _processQueryResults(QuerySnapshot querySnapshot) {
    print(
        'Firestore query completed. Found ${querySnapshot.docs.length} playlists');

    if (querySnapshot.docs.isEmpty) {
      print('No playlists found for this user');
      return [];
    }

    // Print raw data for debugging
    for (var doc in querySnapshot.docs) {
      print('Playlist document: ${doc.id}');
      print('Playlist data: ${doc.data()}');
    }

    final playlists =
        querySnapshot.docs.map((doc) => Playlist.fromFirestore(doc)).toList();

    print('Converted ${playlists.length} playlists from Firestore');
    return playlists;
  }

  // Create a new playlist
  Future<Playlist> createPlaylist({
    required String name,
    required String userId,
    String description = '',
    String imageUrl = '',
    List<String> initialBhajanIds = const [],
  }) async {
    try {
      // Create a document reference with an auto-generated ID
      final docRef = _playlistsCollection.doc();

      final now = DateTime.now();

      // Create the playlist object
      final playlist = Playlist(
        id: docRef.id,
        name: name,
        description: description,
        userId: userId,
        bhajanIds: initialBhajanIds,
        imageUrl: imageUrl,
        createdAt: now,
        updatedAt: now,
      );

      // Save to Firestore
      await docRef.set(playlist.toMap());

      return playlist;
    } catch (e) {
      print('Error creating playlist: $e');
      rethrow;
    }
  }

  // Update a playlist
  Future<void> updatePlaylist(Playlist playlist) async {
    try {
      // Update the 'updatedAt' timestamp
      final updatedPlaylist = playlist.copyWith(updatedAt: DateTime.now());

      await _playlistsCollection
          .doc(playlist.id)
          .update(updatedPlaylist.toMap());
    } catch (e) {
      print('Error updating playlist: $e');
      rethrow;
    }
  }

  // Delete a playlist
  Future<void> deletePlaylist(String playlistId) async {
    try {
      await _playlistsCollection.doc(playlistId).delete();
    } catch (e) {
      print('Error deleting playlist: $e');
      rethrow;
    }
  }

  // Add a bhajan to a playlist
  Future<void> addBhajanToPlaylist(String playlistId, String bhajanId) async {
    try {
      final playlist = await getPlaylist(playlistId);
      if (playlist == null) {
        throw Exception('Playlist not found');
      }

      if (playlist.bhajanIds.contains(bhajanId)) {
        return; // Bhajan already in playlist
      }

      final updatedPlaylist = playlist.addBhajan(bhajanId);
      await updatePlaylist(updatedPlaylist);
    } catch (e) {
      print('Error adding bhajan to playlist: $e');
      rethrow;
    }
  }

  // Remove a bhajan from a playlist
  Future<void> removeBhajanFromPlaylist(
      String playlistId, String bhajanId) async {
    try {
      final playlist = await getPlaylist(playlistId);
      if (playlist == null) {
        throw Exception('Playlist not found');
      }

      final updatedPlaylist = playlist.removeBhajan(bhajanId);
      await updatePlaylist(updatedPlaylist);
    } catch (e) {
      print('Error removing bhajan from playlist: $e');
      rethrow;
    }
  }
}
