import 'package:cloud_firestore/cloud_firestore.dart';

class UserPreferences {
  final String userId;
  final String name;
  final List<String> playlists;
  final List<String> favoriteNames;
  final List<String> favoriteWallpapers;
  final List<String> favoriteRingtones;
  final List<String> favoriteBhajans;
  final List<String> favoriteAvatars;
  final DateTime lastUpdated;

  UserPreferences({
    required this.userId,
    required this.name,
    this.playlists = const [],
    this.favoriteNames = const [],
    this.favoriteWallpapers = const [],
    this.favoriteRingtones = const [],
    this.favoriteBhajans = const [],
    this.favoriteAvatars = const [],
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'playlists': playlists,
      'favoriteNames': favoriteNames,
      'favoriteWallpapers': favoriteWallpapers,
      'favoriteRingtones': favoriteRingtones,
      'favoriteBhajans': favoriteBhajans,
      'favoriteAvatars': favoriteAvatars,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      playlists: List<String>.from(map['playlists'] ?? []),
      favoriteNames: List<String>.from(map['favoriteNames'] ?? []),
      favoriteWallpapers: List<String>.from(map['favoriteWallpapers'] ?? []),
      favoriteRingtones: List<String>.from(map['favoriteRingtones'] ?? []),
      favoriteBhajans: List<String>.from(map['favoriteBhajans'] ?? []),
      favoriteAvatars: List<String>.from(map['favoriteAvatars'] ?? []),
      lastUpdated:
          (map['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}
