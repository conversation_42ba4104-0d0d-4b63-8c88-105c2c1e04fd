import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/ringtone.dart';
import '../providers/audio_provider.dart';
import '../services/audio_manager.dart';

class RingtonePositionData {
  final Duration position;
  final Duration bufferedPosition;
  final Duration duration;

  RingtonePositionData(this.position, this.bufferedPosition, this.duration);
}

class RingtoneService {
  AudioPlayer? _player;
  List<Ringtone> _ringtones = [];
  int _currentIndex = -1;
  bool _isInitialized = false;
  final _currentRingtoneController = StreamController<Ringtone?>.broadcast();
  final _playerStateController = StreamController<PlayerState>.broadcast();
  final _positionDataController =
      StreamController<RingtonePositionData>.broadcast();
  Timer? _positionUpdateTimer;

  AudioProvider? _audioProvider;
  bool _wasBhajanPlaying = false;

  // Reference to the central audio manager
  final AudioManager _audioManager = AudioManager();

  // HTTP client for checking URL accessibility
  final _httpClient = http.Client();

  Stream<RingtonePositionData> get positionDataStream =>
      _positionDataController.stream;
  Stream<PlayerState> get playerStateStream => _playerStateController.stream;
  Stream<int?> get currentIndexStream =>
      Stream.value(_currentIndex >= 0 ? _currentIndex : null);
  Stream<double> get volumeStream => Stream.value(_player?.volume ?? 1.0);

  double get volume => _player?.volume ?? 1.0;
  bool get isPlaying => _player?.playing ?? false;
  Duration? get duration => _player?.duration;
  Duration get position => _player?.position ?? Duration.zero;
  AudioPlayer? get player => _player;
  Ringtone? get currentRingtone => getCurrentRingtone();

  // Expose the current ringtone stream
  Stream<Ringtone?> get currentRingtoneStream =>
      _currentRingtoneController.stream;

  RingtoneService() {
    // No initialization needed here - we'll get the player when needed
  }

  void setAudioProvider(AudioProvider provider) {
    _audioProvider = provider;
    debugPrint('RingtoneService: AudioProvider reference set');
  }

  Future<void> init(List<Ringtone> ringtones, {int initialIndex = 0}) async {
    debugPrint(
        'RingtoneService: Initializing with ${ringtones.length} ringtones');
    if (ringtones.isEmpty) {
      debugPrint(
          'RingtoneService: Cannot initialize with empty ringtones list');
      throw Exception(
          'Cannot initialize ringtone service with empty ringtones list');
    }

    _ringtones = ringtones;
    _currentIndex = initialIndex.clamp(0, ringtones.length - 1);
    _isInitialized = true;

    debugPrint('RingtoneService: Successfully initialized ringtone service');
  }

  void _setupPositionStream() {
    _positionUpdateTimer?.cancel();

    if (_player == null) return;

    _positionUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 200), (_) {
      if (_player != null && !_positionDataController.isClosed) {
        _positionDataController.add(
          RingtonePositionData(
            _player!.position,
            _player!.bufferedPosition,
            _player!.duration ?? Duration.zero,
          ),
        );
      }
    });
  }

  Future<void> play(int index) async {
    if (index < 0 || index >= _ringtones.length) {
      debugPrint('RingtoneService: Invalid index: $index');
      return;
    }

    try {
      // Check if bhajan is playing and remember state
      if (_audioProvider != null) {
        _wasBhajanPlaying = _audioProvider!.audioService.isPlaying;
        if (_wasBhajanPlaying) {
          debugPrint('RingtoneService: Bhajan was playing, pausing it');
          _audioProvider!.pauseBhajan();
          await Future<void>.delayed(const Duration(milliseconds: 100)); // Ensure pause completes
        }
      }

      // Get the ringtone to play
      final ringtone = _ringtones[index];
      _currentIndex = index;
      _currentRingtoneController.add(ringtone);

      debugPrint(
          'RingtoneService: Attempting to play ringtone: ${ringtone.title}');
      debugPrint('RingtoneService: URL: ${ringtone.url}');

      // Force stop and release any existing bhajan player first
      // This is crucial for switching from bhajan to ringtone
      await _audioManager.forceStopAndReleasePlayer(AudioType.bhajan);
      
      // Then check our own player
      if (_player != null) {
        debugPrint(
            'RingtoneService: Stopping current playback before switching ringtones');
        await _player!.stop();
      } 
      
      // Always request a fresh player for ringtones when switching from bhajans
      _player = null;
      
      // Request player from audio manager
      _player = await _audioManager.requestPlayer(AudioType.ringtone);

      if (_player == null) {
        throw Exception('Failed to get audio player for ringtone');
      }

      // Set up listeners
      _player!.playerStateStream.listen((state) {
        _playerStateController.add(state);
        if (state.processingState == ProcessingState.completed) {
          // Auto-play next when completed
          if (_currentIndex < _ringtones.length - 1) {
            play(_currentIndex + 1);
          }
        }
      });

      // Set up position data stream
      _setupPositionStream();

      // Ensure the URL uses HTTPS
      var audioUrl = ringtone.url;
      if (!audioUrl.startsWith('https://')) {
        audioUrl = audioUrl.replaceFirst('http://', 'https://');
      }

      // Check URL accessibility
      try {
        final response = await _httpClient.head(Uri.parse(audioUrl));
        debugPrint('RingtoneService: URL status code: ${response.statusCode}');
        if (response.statusCode != 200) {
          debugPrint(
              'RingtoneService: Warning - URL returned status code: ${response.statusCode}');
        }
      } catch (e) {
        debugPrint('RingtoneService: Warning - Failed to verify URL: $e');
        // Continue anyway, the actual playback will determine if the URL is valid
      }

      // Set headers for the request
      final headers = {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
      };

      // Create AudioSource using the AudioManager helper
      final source = await _audioManager.createAudioSource(
        url: audioUrl,
        id: 'ringtone_${ringtone.id}',
        title: ringtone.title,
        artist: 'Hindu Path Ringtones',
        artUri:
            Uri.parse('https://cdn.hindupath.online/assets/ringtone_icon.png'),
        extras: {
          'type': 'ringtone',
          'filename': ringtone.filename,
        },
        headers: headers,
      );

      try {
        // Set the audio source with a timeout
        await _player!.setAudioSource(source).timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            throw TimeoutException('Connection timed out while loading audio');
          },
        );

        debugPrint('RingtoneService: Starting playback');
        await _player!.play();
        debugPrint('RingtoneService: Playback started successfully');
      } catch (e) {
        debugPrint(
            'RingtoneService: Error setting audio source or playing: $e');
        throw Exception('Failed to load audio: $e');
      }
    } catch (e) {
      debugPrint('RingtoneService: Error playing ringtone: $e');

      // Try fallback method if the first attempt fails
      try {
        debugPrint('RingtoneService: Trying fallback method...');
        await _playRingtoneWithMediaItem(index);
      } catch (fallbackError) {
        debugPrint(
            'RingtoneService: Fallback method also failed: $fallbackError');

        // Reset player to ensure we can try again
        await _audioManager.forceStopAndReleasePlayer(AudioType.ringtone);
        _player = null;

        rethrow;
      }
    }
  }

  // Fallback method that tries a different approach with proper MediaItem
  Future<void> _playRingtoneWithMediaItem(int index) async {
    final ringtone = _ringtones[index];

    try {
      // Force stop any bhajan player that might be active
      await _audioManager.forceStopAndReleasePlayer(AudioType.bhajan);
      
      // If we already have a player, stop it first
      if (_player != null) {
        await _player!.stop();
      } 
      
      // Always request a fresh player
      _player = null;
      
      // Request a new player from the audio manager
      _player = await _audioManager.requestPlayer(AudioType.ringtone);

      if (_player == null) {
        throw Exception('Failed to get audio player for ringtone fallback');
      }

      // Ensure the URL uses HTTPS
      var audioUrl = ringtone.url;
      if (!audioUrl.startsWith('https://')) {
        audioUrl = audioUrl.replaceFirst('http://', 'https://');
      }

      // Create a ConcatenatingAudioSource with a single item
      final audioSource = await _audioManager.createAudioSource(
        url: audioUrl,
        id: 'ringtone_${ringtone.id}_fallback',
        title: ringtone.title,
        artist: 'Hindu Path Ringtones',
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
        },
      );

      final playlist = ConcatenatingAudioSource(
        children: [audioSource],
      );

      // Set up listeners
      _player!.playerStateStream.listen((state) {
        _playerStateController.add(state);
      });

      // Set up position data stream
      _setupPositionStream();

      // Set the audio source and play with timeout
      await _player!.setAudioSource(playlist).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException('Connection timed out while loading audio');
        },
      );

      await _player!.play();
      debugPrint('RingtoneService: Fallback playback started successfully');
    } catch (e) {
      debugPrint('RingtoneService: Error in fallback method: $e');

      // Reset player to ensure we can try again
      await _audioManager.forceStopAndReleasePlayer(AudioType.ringtone);
      _player = null;

      throw Exception('Fallback playback failed: $e');
    }
  }

  Future<void> pause() async {
    if (_player != null) {
      debugPrint('RingtoneService: Pausing at position: ${_player!.position}');
      await _player!.pause();
    }
  }

  Future<void> resume() async {
    if (_player != null) {
      debugPrint(
          'RingtoneService: Resuming from position: ${_player!.position}');
      await _player!.play();
    }
  }

  Future<void> seek(Duration position) async {
    if (_player != null) {
      await _player!.seek(position);
    }
  }

  Future<void> playNext() async {
    if (_currentIndex < _ringtones.length - 1) {
      await play(_currentIndex + 1);
    }
  }

  Future<void> playPrevious() async {
    if (_currentIndex > 0) {
      await play(_currentIndex - 1);
    }
  }

  Future<void> setVolume(double volume) async {
    if (_player != null) {
      await _player!.setVolume(volume);
    }
  }

  Future<void> dispose() async {
    _positionUpdateTimer?.cancel();

    // Force stop and release the player through the AudioManager
    await _audioManager.forceStopAndReleasePlayer(AudioType.ringtone);
    _player = null;

    // Resume bhajan playback if it was playing before
    if (_wasBhajanPlaying && _audioProvider != null) {
      try {
        await _audioProvider!.audioService.resume();
        debugPrint('RingtoneService: Resumed bhajan playback');
      } catch (e) {
        debugPrint('RingtoneService: Error resuming bhajan playback: $e');
      }
    }

    // Reset state
    _wasBhajanPlaying = false;
    _currentIndex = -1;
    _isInitialized = false;

    // Close streams if they're not already closed
    if (!_currentRingtoneController.isClosed)
      await _currentRingtoneController.close();
    if (!_playerStateController.isClosed) await _playerStateController.close();
    if (!_positionDataController.isClosed)
      await _positionDataController.close();
    _httpClient.close();
  }

  Ringtone? getCurrentRingtone() {
    if (_currentIndex >= 0 && _currentIndex < _ringtones.length) {
      return _ringtones[_currentIndex];
    }
    return null;
  }

  List<Ringtone> getPlaylist() => _ringtones;
}
