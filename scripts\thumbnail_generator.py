import boto3
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
import os
import urllib.parse
import io
from PIL import Image
import time

# Service account path
SERVICE_ACCOUNT_PATH = r"C:\pyflutter\projects\hindupath\scripts\hindupath-studiohabre-firebase-adminsdk.json"

# Cloudflare R2 configuration
R2_CONFIG = {
    'endpoint_url': 'https://3a0098f2f5a153bb2a3de2fba20d674d.r2.cloudflarestorage.com',
    'aws_access_key_id': '70664ebbc81354bce2e32098189e3578',
    'aws_secret_access_key': 'd52c62e094eb0e728d083d36909e634aa6c093708bdbe54aaa8e8821ad76f004',
    'region_name': 'auto'
}

# CDN base URL for wallpapers
CDN_BASE_URL = 'https://cdn.hindupath.online'

# Thumbnail settings
THUMBNAIL_SIZE = (400, 600)  # Width, Height
THUMBNAIL_PREFIX = "thumbnails/"
THUMBNAIL_QUALITY = 85

# Initialize R2 client
s3_client = boto3.client('s3', **R2_CONFIG)

# Check if service account file exists
if not os.path.exists(SERVICE_ACCOUNT_PATH):
    print(f"Error: Service account file not found at: {SERVICE_ACCOUNT_PATH}")
    exit(1)

# Initialize Firebase Admin SDK
try:
    cred = credentials.Certificate(SERVICE_ACCOUNT_PATH)
    firebase_admin.initialize_app(cred)
    db = firestore.client()
    print(f"Successfully initialized Firebase with service account from: {SERVICE_ACCOUNT_PATH}")
except Exception as e:
    print(f"Error initializing Firebase: {str(e)}")
    exit(1)

def get_category_from_path(key):
    """Extract category from the file path"""
    # The path format is: wallpapers/category_name/filename
    parts = key.split('/')
    if len(parts) >= 2:
        return parts[1].replace('_', ' ').title()
    return "Unknown"

def format_file_size(size_in_bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_in_bytes < 1024:
            return f"{size_in_bytes:.2f} {unit}"
        size_in_bytes /= 1024
    return f"{size_in_bytes:.2f} GB"

def is_wallpaper_file(key):
    """Check if the file is a wallpaper (in wallpapers folder and is an image)"""
    image_extensions = ('.jpg', '.jpeg', '.png', '.webp')
    return (
        key.startswith('wallpapers/') and 
        any(key.lower().endswith(ext) for ext in image_extensions) and
        not key.startswith('wallpapers/thumbnails/')  # Exclude existing thumbnails
    )

def generate_thumbnail(bucket_name, object_key):
    """Generate a thumbnail for the given image"""
    try:
        print(f"⏳ Generating thumbnail for: {object_key}")
        start_time = time.time()
        
        # Download original image from R2
        response = s3_client.get_object(Bucket=bucket_name, Key=object_key)
        image_data = response['Body'].read()
        
        # Create thumbnail
        with Image.open(io.BytesIO(image_data)) as img:
            # Convert to RGB if needed (for PNGs with transparency)
            if img.mode in ('RGBA', 'LA'):
                bg = Image.new('RGB', img.size, (255, 255, 255))
                bg.paste(img, mask=img.split()[3])
                img = bg
                
            # Resize while maintaining aspect ratio
            img.thumbnail(THUMBNAIL_SIZE)
            
            # Save as optimized JPEG
            buffer = io.BytesIO()
            img.save(buffer, format='JPEG', quality=THUMBNAIL_QUALITY, optimize=True)
            buffer.seek(0)
        
        # Generate thumbnail key (path in R2)
        # Keep the category structure: wallpapers/category/filename → wallpapers/thumbnails/category/filename
        path_parts = object_key.split('/')
        if len(path_parts) >= 3:  # wallpapers/category/filename
            category = path_parts[1]
            filename = path_parts[2]
            filename_base = os.path.splitext(filename)[0]
            thumbnail_key = f"wallpapers/{THUMBNAIL_PREFIX}{category}/{filename_base}.jpg"
        else:
            # Fallback if structure is different
            filename_base = os.path.splitext(os.path.basename(object_key))[0]
            thumbnail_key = f"wallpapers/{THUMBNAIL_PREFIX}{filename_base}.jpg"
        
        # Upload thumbnail to R2
        s3_client.put_object(
            Bucket=bucket_name,
            Key=thumbnail_key,
            Body=buffer,
            ContentType='image/jpeg'
        )
        
        # Return the thumbnail URL (using the same CDN pattern as originals)
        thumbnail_path = thumbnail_key.replace('wallpapers/', '', 1)
        thumbnail_url = f"{CDN_BASE_URL}/wallpapers/{urllib.parse.quote(thumbnail_path)}"
        
        elapsed_time = time.time() - start_time
        print(f"✅ Created thumbnail in {elapsed_time:.2f}s: {thumbnail_url}")
        
        return thumbnail_url
    except Exception as e:
        print(f"❌ Error generating thumbnail for {object_key}: {str(e)}")
        return None

def upload_wallpapers_to_firestore():
    try:
        # First, test if Firestore is accessible
        try:
            db.collection('wallpapers').limit(1).get()
        except Exception as firebase_error:
            if 'SERVICE_DISABLED' in str(firebase_error):
                print("\n❌ Error: Firestore API is not enabled")
                print("Please enable Firestore in your Firebase Console")
                return
            else:
                raise firebase_error

        # List all objects in the R2 bucket
        response = s3_client.list_objects_v2(Bucket='hindupath')
        
        if 'Contents' not in response:
            print("No objects found in the bucket")
            return

        # Filter only wallpaper files
        wallpaper_files = [obj for obj in response['Contents'] if is_wallpaper_file(obj['Key'])]
        total_files = len(wallpaper_files)
        
        if total_files == 0:
            print("No wallpaper files found")
            return

        processed_files = 0

        for obj in wallpaper_files:
            try:
                processed_files += 1
                print(f"\nProcessing file {processed_files}/{total_files}: {obj['Key']}")

                # Get the object metadata
                metadata = s3_client.head_object(
                    Bucket='hindupath',
                    Key=obj['Key']
                )

                # Extract filename and category
                filename = os.path.basename(obj['Key'])
                category = get_category_from_path(obj['Key'])
                
                # Create the CDN URL - remove the initial 'wallpapers/' from the key since it's already in the CDN path
                object_path = obj['Key'].replace('wallpapers/', '', 1)
                cdn_url = f"{CDN_BASE_URL}/wallpapers/{urllib.parse.quote(object_path)}"
                
                # Generate thumbnail and get its URL
                thumbnail_url = generate_thumbnail('hindupath', obj['Key'])

                # Prepare wallpaper document
                wallpaper_data = {
                    'filename': filename,
                    'category': category,
                    'url': cdn_url,
                    'thumbnailUrl': thumbnail_url,  # Add thumbnail URL
                    'size': format_file_size(obj['Size']),
                    'uploadDate': metadata['LastModified'],
                    'r2ObjectKey': object_path  # Store without the initial 'wallpapers/'
                }

                # Add to Firestore
                doc_ref = db.collection('wallpapers').document()
                doc_ref.set(wallpaper_data)
                
                print(f"✅ Successfully uploaded metadata for: {filename}")
                print(f"   Category: {category}")
                print(f"   CDN URL: {cdn_url}")
                print(f"   Thumbnail URL: {thumbnail_url}")
                print(f"   Size: {wallpaper_data['size']}")

            except Exception as e:
                print(f"❌ Error processing {obj['Key']}: {str(e)}")
                continue

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        
def update_existing_wallpapers_with_thumbnails():
    """Update existing wallpapers in Firestore with thumbnails"""
    try:
        print("\n📝 Checking for existing wallpapers that need thumbnails...")
        
        # Get all wallpapers without thumbnails
        wallpapers_ref = db.collection('wallpapers')
        query = wallpapers_ref.where('thumbnailUrl', '==', None)
        wallpapers = query.get()
        
        if not wallpapers:
            print("✅ No existing wallpapers need thumbnails")
            return
            
        print(f"🔍 Found {len(wallpapers)} wallpapers that need thumbnails")
        
        for doc in wallpapers:
            try:
                wallpaper = doc.to_dict()
                object_key = f"wallpapers/{wallpaper['r2ObjectKey']}"
                print(f"\nProcessing: {object_key}")
                
                # Generate thumbnail
                thumbnail_url = generate_thumbnail('hindupath', object_key)
                
                if thumbnail_url:
                    # Update Firestore
                    doc.reference.update({
                        'thumbnailUrl': thumbnail_url
                    })
                    print(f"✅ Updated Firestore document with thumbnail URL")
                
            except Exception as e:
                print(f"❌ Error updating wallpaper {doc.id}: {str(e)}")
                continue
                
    except Exception as e:
        print(f"❌ Error updating existing wallpapers: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting wallpaper processor...")
    
    # Install Pillow if not available
    try:
        import PIL
        print(f"✅ Pillow version {PIL.__version__} is installed")
    except ImportError:
        print("❌ Pillow (PIL) is not installed. Installing...")
        os.system("pip install pillow")
        print("✅ Pillow installed")
    
    # Process options
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--update-existing":
        update_existing_wallpapers_with_thumbnails()
    else:
        upload_wallpapers_to_firestore()
        
    print("\n✨ Processing complete!")