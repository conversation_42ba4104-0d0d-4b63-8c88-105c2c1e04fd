import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../models/user_preferences.dart';
import 'user_preferences_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final UserPreferencesService _preferencesService = UserPreferencesService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Stream of auth changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google sign-in process');

      // Trigger the authentication flow directly
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in flow
        debugPrint('Google Sign-In was canceled by user');
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      final User? user = userCredential.user;
      debugPrint('Firebase sign-in successful: ${user?.uid}');

      if (user != null) {
        try {
          // Create user preferences in Firestore
          final preferences = UserPreferences(
            userId: user.uid,
            name: user.displayName ?? 'User',
          );

          // First try to get existing preferences
          final existingPrefs =
              await _preferencesService.getUserPreferences(user.uid);
          if (existingPrefs == null) {
            // Only create if they don't exist yet
            await _preferencesService.createUserPreferences(preferences);
          }
        } catch (prefError) {
          // Log preference creation error but don't fail login
          debugPrint('Error creating user preferences: $prefError');
        }
      }

      return userCredential;
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      return null;
    }
  }

  // Sign in as guest
  Future<void> signInAsGuest() async {
    // This is just a local state change, no Firebase involved
    // You might want to use shared preferences or other local storage
    // to remember that the user chose guest mode
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // First sign out from Firebase
      await _auth.signOut();

      // Then try to sign out from Google - but don't let it block the process
      try {
        await _googleSignIn.signOut();
      } catch (e) {
        // Just log Google sign-out errors, don't fail
        debugPrint('Non-critical error signing out from Google: $e');
      }
    } catch (e) {
      debugPrint('Error signing out: $e');
      // Don't rethrow to prevent the app from getting stuck
    }
  }

  // Check if user is signed in
  bool get isSignedIn => _auth.currentUser != null;

  // Check if user is guest
  bool get isGuest => false; // Implement guest check logic

  // Delete user data for GDPR compliance
  Future<void> deleteUserData(String userId) async {
    try {
      // Delete user preferences from Firestore
      await _preferencesService.deleteUserPreferences(userId);

      // Delete user favorites
      await _preferencesService.deleteAllFavorites(userId);

      // Delete any other user-related data in Firestore
      await FirebaseFirestore.instance
          .collection('userPlaylists')
          .where('userId', isEqualTo: userId)
          .get()
          .then((snapshot) {
        for (var doc in snapshot.docs) {
          doc.reference.delete();
        }
      });

      // Add other collections that store user data
      await FirebaseFirestore.instance
          .collection('userProgress')
          .where('userId', isEqualTo: userId)
          .get()
          .then((snapshot) {
        for (var doc in snapshot.docs) {
          doc.reference.delete();
        }
      });

      debugPrint('User data deleted successfully for GDPR compliance: $userId');
    } catch (e) {
      debugPrint('Error deleting user data: $e');
      throw Exception('Failed to delete user data: $e');
    }
  }
}
