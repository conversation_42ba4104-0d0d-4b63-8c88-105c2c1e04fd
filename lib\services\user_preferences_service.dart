import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_preferences.dart';

class UserPreferencesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<void> createUserPreferences(UserPreferences preferences) async {
    try {
      print(
          'Creating user preferences in Firestore for user ${preferences.userId}');

      // First check if document already exists
      final docRef = _firestore
          .collection('users')
          .doc(preferences.userId)
          .collection('preferences')
          .doc('user_preferences');

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        print('User preferences already exist, updating instead');
        await docRef.update(preferences.toMap());
      } else {
        print('Creating new user preferences document');
        await docRef.set(preferences.toMap());
      }

      // Verify the document was created
      final verifySnapshot = await docRef.get();
      if (verifySnapshot.exists) {
        print('Successfully created/updated user preferences in Firestore');
      } else {
        print(
            'ERROR: Failed to create user preferences - document does not exist after set operation');
      }
    } catch (e) {
      print('ERROR creating user preferences: $e');
      // Log more details about the error
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      rethrow;
    }
  }

  Future<UserPreferences?> getUserPreferences(String userId) async {
    try {
      print('Getting user preferences for user $userId');
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('user_preferences')
          .get();

      if (doc.exists) {
        print('Found user preferences document');
        return UserPreferences.fromMap(doc.data()!);
      }
      print('User preferences document does not exist');
      return null;
    } catch (e) {
      print('ERROR getting user preferences: $e');
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      rethrow;
    }
  }

  Future<void> updateUserPreferences(UserPreferences preferences) async {
    try {
      print('Updating user preferences for user ${preferences.userId}');
      await _firestore
          .collection('users')
          .doc(preferences.userId)
          .collection('preferences')
          .doc('user_preferences')
          .update(preferences.toMap());
      print('Successfully updated user preferences');
    } catch (e) {
      print('ERROR updating user preferences: $e');
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      rethrow;
    }
  }

  Future<void> addToFavorites(String userId, String type, String itemId) async {
    try {
      print('Adding $type with ID $itemId to favorites for user $userId');
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('user_preferences');

      await _firestore.runTransaction((transaction) async {
        final doc = await transaction.get(docRef);
        if (doc.exists) {
          final data = doc.data()!;
          final fieldName = 'favorite${type}s';
          final favorites = List<String>.from(data[fieldName] ?? []);

          if (!favorites.contains(itemId)) {
            favorites.add(itemId);
            transaction.update(docRef, {
              fieldName: favorites,
              'lastUpdated': FieldValue.serverTimestamp(),
            });
            print('Added item to $fieldName list');
          } else {
            print('Item already in favorites list');
          }
        } else {
          print(
              'User preferences document does not exist, creating new document');
          // Create a new document with this favorite
          final newPreferences = UserPreferences(
            userId: userId,
            name: 'User', // Default name
          );
          final Map<String, dynamic> prefsMap = newPreferences.toMap();
          prefsMap['favorite${type}s'] = [itemId];
          transaction.set(docRef, prefsMap);
        }
      });
      print('Successfully updated favorites');
    } catch (e) {
      print('ERROR adding to favorites: $e');
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      rethrow;
    }
  }

  Future<void> removeFromFavorites(
      String userId, String type, String itemId) async {
    try {
      print('Removing $type with ID $itemId from favorites for user $userId');
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('user_preferences');

      await _firestore.runTransaction((transaction) async {
        final doc = await transaction.get(docRef);
        if (doc.exists) {
          final data = doc.data()!;
          final fieldName = 'favorite${type}s';
          final favorites = List<String>.from(data[fieldName] ?? []);

          favorites.remove(itemId);
          transaction.update(docRef, {
            fieldName: favorites,
            'lastUpdated': FieldValue.serverTimestamp(),
          });
          print('Removed item from $fieldName list');
        } else {
          print('Document does not exist, nothing to remove');
        }
      });
      print('Successfully updated favorites');
    } catch (e) {
      print('ERROR removing from favorites: $e');
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      rethrow;
    }
  }

  // Specific methods for favorite names
  Future<bool> isNameFavorite(String userId, String name) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return false;
      return preferences.favoriteNames.contains(name);
    } catch (e) {
      print('ERROR checking if name is favorite: $e');
      return false;
    }
  }

  Future<void> addFavoriteName(String userId, String name) async {
    try {
      print('Adding name $name to favorites for user $userId');
      await addToFavorites(userId, 'Name', name);
    } catch (e) {
      print('ERROR adding name to favorites: $e');
      rethrow;
    }
  }

  Future<void> removeFavoriteName(String userId, String name) async {
    try {
      print('Removing name $name from favorites for user $userId');
      await removeFromFavorites(userId, 'Name', name);
    } catch (e) {
      print('ERROR removing name from favorites: $e');
      rethrow;
    }
  }

  Future<List<String>> getFavoriteNames(String userId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return [];
      return preferences.favoriteNames;
    } catch (e) {
      print('ERROR getting favorite names: $e');
      return [];
    }
  }

  // Specific methods for favorite wallpapers
  Future<bool> isWallpaperFavorite(String userId, String wallpaperId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return false;
      return preferences.favoriteWallpapers.contains(wallpaperId);
    } catch (e) {
      print('ERROR checking if wallpaper is favorite: $e');
      return false;
    }
  }

  Future<void> addFavoriteWallpaper(String userId, String wallpaperId) async {
    try {
      print('Adding wallpaper $wallpaperId to favorites for user $userId');
      await addToFavorites(userId, 'Wallpaper', wallpaperId);
    } catch (e) {
      print('ERROR adding wallpaper to favorites: $e');
      rethrow;
    }
  }

  Future<void> removeFavoriteWallpaper(
      String userId, String wallpaperId) async {
    try {
      print('Removing wallpaper $wallpaperId from favorites for user $userId');
      await removeFromFavorites(userId, 'Wallpaper', wallpaperId);
    } catch (e) {
      print('ERROR removing wallpaper from favorites: $e');
      rethrow;
    }
  }

  Future<List<String>> getFavoriteWallpapers(String userId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return [];
      return preferences.favoriteWallpapers;
    } catch (e) {
      print('ERROR getting favorite wallpapers: $e');
      return [];
    }
  }

  // Specific methods for favorite avatars
  Future<bool> isAvatarFavorite(String userId, String avatarId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return false;
      return preferences.favoriteAvatars.contains(avatarId);
    } catch (e) {
      print('ERROR checking if avatar is favorite: $e');
      return false;
    }
  }

  Future<void> addFavoriteAvatar(String userId, String avatarId) async {
    try {
      print('Adding avatar $avatarId to favorites for user $userId');
      await addToFavorites(userId, 'Avatar', avatarId);
    } catch (e) {
      print('ERROR adding avatar to favorites: $e');
      rethrow;
    }
  }

  Future<void> removeFavoriteAvatar(String userId, String avatarId) async {
    try {
      print('Removing avatar $avatarId from favorites for user $userId');
      await removeFromFavorites(userId, 'Avatar', avatarId);
    } catch (e) {
      print('ERROR removing avatar from favorites: $e');
      rethrow;
    }
  }

  Future<List<String>> getFavoriteAvatars(String userId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return [];
      return preferences.favoriteAvatars;
    } catch (e) {
      print('ERROR getting favorite avatars: $e');
      return [];
    }
  }

  // Specific methods for favorite ringtones
  Future<bool> isRingtoneFavorite(String userId, String ringtoneId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return false;
      return preferences.favoriteRingtones.contains(ringtoneId);
    } catch (e) {
      print('ERROR checking if ringtone is favorite: $e');
      return false;
    }
  }

  Future<void> addFavoriteRingtone(String userId, String ringtoneId) async {
    try {
      print('Adding ringtone $ringtoneId to favorites for user $userId');
      await addToFavorites(userId, 'Ringtone', ringtoneId);
    } catch (e) {
      print('ERROR adding ringtone to favorites: $e');
      rethrow;
    }
  }

  Future<void> removeFavoriteRingtone(String userId, String ringtoneId) async {
    try {
      print('Removing ringtone $ringtoneId from favorites for user $userId');
      await removeFromFavorites(userId, 'Ringtone', ringtoneId);
    } catch (e) {
      print('ERROR removing ringtone from favorites: $e');
      rethrow;
    }
  }

  Future<List<String>> getFavoriteRingtones(String userId) async {
    try {
      final preferences = await getUserPreferences(userId);
      if (preferences == null) return [];
      return preferences.favoriteRingtones;
    } catch (e) {
      print('ERROR getting favorite ringtones: $e');
      return [];
    }
  }

  // Delete user preferences (GDPR compliance)
  Future<void> deleteUserPreferences(String userId) async {
    try {
      print('Deleting user preferences for user $userId (GDPR compliance)');
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('user_preferences')
          .delete();
      print('Successfully deleted user preferences');
    } catch (e) {
      print('ERROR deleting user preferences: $e');
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      rethrow;
    }
  }

  // Delete all user favorites across all content types (GDPR compliance)
  Future<void> deleteAllFavorites(String userId) async {
    try {
      print('Deleting all favorites for user $userId (GDPR compliance)');

      // Get the user preferences document reference
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('user_preferences');

      // Update the document to remove all favorite collections
      await docRef.update({
        'favoriteNames': [],
        'favoriteWallpapers': [],
        'favoriteBhajans': [],
        'favoriteAvatars': [],
        'favoriteRingtones': [],
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      print('Successfully removed all user favorites');
    } catch (e) {
      print('ERROR deleting all favorites: $e');
      if (e is FirebaseException) {
        print('Firebase error code: ${e.code}');
        print('Firebase error message: ${e.message}');
      }
      // Don't rethrow here since we might be deleting the document itself
    }
  }
}
