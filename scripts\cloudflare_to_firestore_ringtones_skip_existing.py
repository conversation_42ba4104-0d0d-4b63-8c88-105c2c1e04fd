"""
CL<PERSON><PERSON><PERSON><PERSON><PERSON> TO FIRESTORE SCRIPT FOR RINGTONES (SKIP EXISTING VERSION)
This script uploads ringtone metadata from Cloudflare R2 to Firestore.
Unlike the original version, this script:
1. Checks if ringtones already exist in Firestore (by r2ObjectKey or filename)
2. Skips uploading metadata for ringtones that already exist
3. Only adds new ringtones not yet in the database
4. Provides a summary of skipped and added files

Usage: python cloudflare_to_firestore_ringtones_skip_existing.py
"""

import boto3
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
import os
import urllib.parse

# Service account path
SERVICE_ACCOUNT_PATH = r"C:\pyflutter\projects\hindupath\scripts\hindupath-studiohabre-firebase-adminsdk.json"

# Cloudflare R2 configuration
R2_CONFIG = {
    'endpoint_url': 'https://3a0098f2f5a153bb2a3de2fba20d674d.r2.cloudflarestorage.com',
    'aws_access_key_id': '70664ebbc81354bce2e32098189e3578',
    'aws_secret_access_key': 'd52c62e094eb0e728d083d36909e634aa6c093708bdbe54aaa8e8821ad76f004',
    'region_name': 'auto'
}

# CDN base URL for ringtones
CDN_BASE_URL = 'https://cdn.hindupath.online'

# Initialize R2 client
s3_client = boto3.client('s3', **R2_CONFIG)

# Check if service account file exists
if not os.path.exists(SERVICE_ACCOUNT_PATH):
    print(f"Error: Service account file not found at: {SERVICE_ACCOUNT_PATH}")
    exit(1)

# Initialize Firebase Admin SDK
try:
    cred = credentials.Certificate(SERVICE_ACCOUNT_PATH)
    firebase_admin.initialize_app(cred)
    db = firestore.client()
    print(f"Successfully initialized Firebase with service account from: {SERVICE_ACCOUNT_PATH}")
except Exception as e:
    print(f"Error initializing Firebase: {str(e)}")
    exit(1)

def format_file_size(size_in_bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_in_bytes < 1024:
            return f"{size_in_bytes:.2f} {unit}"
        size_in_bytes /= 1024
    return f"{size_in_bytes:.2f} GB"

def is_ringtone_file(key):
    """Check if the file is a ringtone (in ringtones folder and is an audio file)"""
    audio_extensions = ('.mp3', '.wav', '.m4a', '.ogg', '.aac')
    return (
        key.startswith('ringtones/') and 
        any(key.lower().endswith(ext) for ext in audio_extensions)
    )

def get_title_from_filename(filename):
    """Extract a clean title from the filename"""
    # Remove extension
    name_without_ext = os.path.splitext(filename)[0]
    
    # Replace underscores and hyphens with spaces
    clean_name = name_without_ext.replace('_', ' ').replace('-', ' ')
    
    # Title case the name
    return clean_name.title()

def upload_ringtones_to_firestore():
    try:
        # First, test if Firestore is accessible
        try:
            db.collection('ringtones').limit(1).get()
        except Exception as firebase_error:
            if 'SERVICE_DISABLED' in str(firebase_error):
                print("\n❌ Error: Firestore API is not enabled")
                print("Please enable Firestore in your Firebase Console")
                return
            else:
                raise firebase_error

        # List all objects in the R2 bucket
        response = s3_client.list_objects_v2(Bucket='hindupath')
        
        if 'Contents' not in response:
            print("No objects found in the bucket")
            return

        # Filter only ringtone files
        ringtone_files = [obj for obj in response['Contents'] if is_ringtone_file(obj['Key'])]
        total_files = len(ringtone_files)
        
        if total_files == 0:
            print("No ringtone files found")
            return

        processed_files = 0
        skipped_files = 0
        added_files = 0

        # Get all existing ringtones from Firestore to avoid re-adding
        print("Fetching existing ringtones from Firestore...")
        existing_ringtones = {}
        existing_ringtones_by_title = {}
        existing_ringtones_query = db.collection('ringtones').stream()
        for doc in existing_ringtones_query:
            ringtone_data = doc.to_dict()
            if 'r2ObjectKey' in ringtone_data:
                existing_ringtones[ringtone_data['r2ObjectKey']] = doc.id
            if 'title' in ringtone_data:
                existing_ringtones_by_title[ringtone_data['title']] = doc.id
            elif 'filename' in ringtone_data:
                # Fallback to filename if r2ObjectKey and title are not available
                existing_ringtones[ringtone_data['filename']] = doc.id
        
        print(f"Found {len(existing_ringtones)} existing ringtones in Firestore")

        for obj in ringtone_files:
            try:
                processed_files += 1
                print(f"\nProcessing file {processed_files}/{total_files}: {obj['Key']}")

                # Extract filename and prepare object path
                filename = os.path.basename(obj['Key'])
                object_path = obj['Key'].replace('ringtones/', '', 1)
                
                # Get title from filename
                title = get_title_from_filename(filename)

                # Check if ringtone already exists in Firestore by object path
                if object_path in existing_ringtones:
                    print(f"⏭️ Skipping {obj['Key']} - already exists in Firestore (doc ID: {existing_ringtones[object_path]})")
                    skipped_files += 1
                    continue

                # Check if a ringtone with this title already exists
                if title in existing_ringtones_by_title:
                    print(f"⏭️ Skipping {title} - already exists in Firestore with title (doc ID: {existing_ringtones_by_title[title]})")
                    skipped_files += 1
                    continue

                # Also check if a ringtone with this filename already exists
                if filename in existing_ringtones:
                    print(f"⏭️ Skipping {filename} - already exists in Firestore with filename (doc ID: {existing_ringtones[filename]})")
                    skipped_files += 1
                    continue

                # Get the object metadata
                metadata = s3_client.head_object(
                    Bucket='hindupath',
                    Key=obj['Key']
                )
                
                # Create the CDN URL
                cdn_url = f"{CDN_BASE_URL}/ringtones/{urllib.parse.quote(object_path)}"

                # Prepare ringtone document
                ringtone_data = {
                    'title': title,
                    'filename': filename,
                    'url': cdn_url,
                    'size': format_file_size(obj['Size']),
                    'uploadDate': metadata['LastModified'],
                    'r2ObjectKey': object_path  # Store without the initial 'ringtones/'
                }

                # Add to Firestore
                doc_ref = db.collection('ringtones').document()
                doc_ref.set(ringtone_data)
                added_files += 1
                
                print(f"✅ Successfully uploaded metadata for: {title}")
                print(f"   Filename: {filename}")
                print(f"   CDN URL: {cdn_url}")
                print(f"   Size: {ringtone_data['size']}")

            except Exception as e:
                print(f"❌ Error processing {obj['Key']}: {str(e)}")
                continue

        print("\n📊 Summary:")
        print(f"Total ringtone files found: {total_files}")
        print(f"Files processed: {processed_files}")
        print(f"Files skipped (already exist): {skipped_files}")
        print(f"New files added: {added_files}")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting ringtone metadata upload (skipping existing ringtones)...")
    upload_ringtones_to_firestore()
    print("\n✨ Upload complete!") 