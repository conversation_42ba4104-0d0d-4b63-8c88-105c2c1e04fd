import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/bhajan.dart';
import '../interfaces/bhajan_repository_interface.dart';

/// Implementation of the bhajan repository
class BhajanRepository implements IBhajanRepository {
  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Collection references
  final CollectionReference _bhajansCollection;
  
  // Constructor
  BhajanRepository() : _bhajansCollection = FirebaseFirestore.instance.collection('bhajans');
  
  @override
  Future<List<Bhajan>> getAllBhajans() async {
    try {
      final snapshots = await _bhajansCollection.orderBy('uploadDate', descending: true).get();
      
      return snapshots.docs.map((doc) {
        try {
          return Bhajan.fromFirestore(doc);
        } catch (e) {
          debugPrint('BhajanRepository: Error converting document ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    } catch (e) {
      debugPrint('BhajanRepository: Error getting all bhajans: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<Bhajan>> getBhajanPage(int pageSize, {FirestoreDocumentSnapshot? lastDocument}) async {
    try {
      Query query = _bhajansCollection.orderBy('uploadDate', descending: true).limit(pageSize);
      
      if (lastDocument != null) {
        // This is a placeholder implementation. In the real code, you'd use the actual Firestore DocumentSnapshot
        // instead of our placeholder FirestoreDocumentSnapshot.
        // For now, we'll need to get the document by ID first to have a real DocumentSnapshot.
        final docRef = _firestore.collection('bhajans').doc(lastDocument.id);
        final docSnapshot = await docRef.get();
        query = query.startAfterDocument(docSnapshot);
      }
      
      final querySnapshot = await query.get();
      
      return querySnapshot.docs.map((doc) {
        try {
          return Bhajan.fromFirestore(doc);
        } catch (e) {
          debugPrint('BhajanRepository: Error converting document ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    } catch (e) {
      debugPrint('BhajanRepository: Error getting bhajan page: $e');
      rethrow;
    }
  }
  
  @override
  Future<Bhajan?> getBhajanById(String id) async {
    try {
      final doc = await _firestore.collection('bhajans').doc(id).get();
      
      if (!doc.exists) {
        return null;
      }
      
      return Bhajan.fromFirestore(doc);
    } catch (e) {
      debugPrint('BhajanRepository: Error getting bhajan by id: $e');
      rethrow;
    }
  }
  
  @override
  Future<List<Bhajan>> getFavoriteBhajans() async {
    try {
      final userId = _auth.currentUser?.uid;
      
      if (userId == null) {
        return [];
      }
      
      final userFavoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();
      
      final favoriteIds = userFavoritesSnapshot.docs.map((doc) => doc.id).toList();
      
      if (favoriteIds.isEmpty) {
        return [];
      }
      
      // Firestore doesn't support 'where in' with more than 10 values, so we need to chunk
      const int chunkSize = 10;
      final chunks = <List<String>>[];
      
      for (var i = 0; i < favoriteIds.length; i += chunkSize) {
        chunks.add(favoriteIds.sublist(
            i,
            i + chunkSize < favoriteIds.length
                ? i + chunkSize
                : favoriteIds.length));
      }
      
      final List<Bhajan> favoriteBhajans = [];
      
      for (var chunk in chunks) {
        final querySnapshot = await _firestore
            .collection('bhajans')
            .where(FieldPath.documentId, whereIn: chunk)
            .get();
        
        final bhajans = querySnapshot.docs.map((doc) => Bhajan.fromFirestore(doc)).toList();
        favoriteBhajans.addAll(bhajans);
      }
      
      return favoriteBhajans;
    } catch (e) {
      debugPrint('BhajanRepository: Error getting favorite bhajans: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> addToFavorites(String bhajanId) async {
    try {
      final userId = _auth.currentUser?.uid;
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(bhajanId)
          .set({
        'timestamp': FieldValue.serverTimestamp(),
      });
      
      debugPrint('BhajanRepository: Added bhajan $bhajanId to favorites');
    } catch (e) {
      debugPrint('BhajanRepository: Error adding to favorites: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> removeFromFavorites(String bhajanId) async {
    try {
      final userId = _auth.currentUser?.uid;
      
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(bhajanId)
          .delete();
      
      debugPrint('BhajanRepository: Removed bhajan $bhajanId from favorites');
    } catch (e) {
      debugPrint('BhajanRepository: Error removing from favorites: $e');
      rethrow;
    }
  }
  
  @override
  Future<bool> isFavorite(String bhajanId) async {
    try {
      final userId = _auth.currentUser?.uid;
      
      if (userId == null) {
        return false;
      }
      
      final docSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(bhajanId)
          .get();
      
      return docSnapshot.exists;
    } catch (e) {
      debugPrint('BhajanRepository: Error checking if favorite: $e');
      return false;
    }
  }
  
  @override
  Future<void> saveBhajanProgress(String bhajanId, double progress) async {
    try {
      final userId = _auth.currentUser?.uid;
      
      if (userId == null) {
        return;
      }
      
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('progress')
          .doc(bhajanId)
          .set({
        'progress': progress,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('BhajanRepository: Error saving progress: $e');
      // Don't rethrow as this is a non-critical operation
    }
  }
  
  @override
  Future<double?> getBhajanProgress(String bhajanId) async {
    try {
      final userId = _auth.currentUser?.uid;
      
      if (userId == null) {
        return null;
      }
      
      final docSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('progress')
          .doc(bhajanId)
          .get();
      
      if (!docSnapshot.exists) {
        return null;
      }
      
      final data = docSnapshot.data() as Map<String, dynamic>;
      return data['progress'] as double?;
    } catch (e) {
      debugPrint('BhajanRepository: Error getting progress: $e');
      return null;
    }
  }
  
  @override
  Future<List<Bhajan>> searchBhajans(String query) async {
    try {
      // Convert query to lowercase for case-insensitive search
      final lowercaseQuery = query.toLowerCase();
      
      // Get all bhajans (this could be optimized with a better search solution)
      final querySnapshot = await _bhajansCollection.get();
      
      // Filter in-memory (Firestore doesn't support case-insensitive search directly)
      final filteredBhajans = querySnapshot.docs
          .map((doc) => Bhajan.fromFirestore(doc))
          .where((bhajan) {
            return bhajan.title.toLowerCase().contains(lowercaseQuery) ||
                bhajan.artist.toLowerCase().contains(lowercaseQuery);
          })
          .toList();
      
      return filteredBhajans;
    } catch (e) {
      debugPrint('BhajanRepository: Error searching bhajans: $e');
      rethrow;
    }
  }
} 