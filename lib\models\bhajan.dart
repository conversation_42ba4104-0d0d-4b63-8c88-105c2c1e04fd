import 'package:cloud_firestore/cloud_firestore.dart';

class Bhajan {
  final String title;
  final String artist;
  String get id => r2ObjectKey; // Using r2ObjectKey as unique identifier
  final String duration;
  final String size;
  final DateTime uploadDate;
  final String r2ObjectKey;
  final String r2Url;
  final String artworkUrl;

  Bhajan({
    required this.title,
    required this.artist,
    required this.duration,
    required this.size,
    required this.uploadDate,
    required this.r2ObjectKey,
    required this.r2Url,
    required this.artworkUrl,
  });

  factory Bhajan.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    print('Converting Firestore document to Bhajan: ${doc.id}');
    print('Document data: $data');

    // Ensure URLs use HTTPS
    String ensureHttps(String url) {
      if (url.startsWith('http://')) {
        return url.replaceFirst('http://', 'https://');
      }
      return url;
    }

    final r2Url = ensureHttps(data['r2Url'] ?? '');
    final artworkUrl = ensureHttps(data['artworkUrl'] ?? '');

    print('Processed URLs:');
    print('r2Url: $r2Url');
    print('artworkUrl: $artworkUrl');

    return Bhajan(
      title: data['title'] ?? '',
      artist: 'Hindu Path', // Default artist
      duration: data['duration'] ?? '00:00',
      size: data['size'] ?? '0 MB',
      uploadDate: (data['uploadDate'] as Timestamp).toDate(),
      r2ObjectKey: data['r2ObjectKey'] ?? '',
      r2Url: r2Url,
      artworkUrl: artworkUrl,
    );
  }

  @override
  String toString() {
    return 'Bhajan{title: $title, duration: $duration, r2Url: $r2Url}';
  }
}
