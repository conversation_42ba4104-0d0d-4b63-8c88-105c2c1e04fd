# AdMob Integration Guide for Hindu Path App

## Overview
This guide explains how the Google AdMob integration has been implemented in the Hindu Path app to generate revenue through advertisements.

## Current Implementation

### 1. Banner Ads
Banner ads have been implemented to display at the bottom of the screen, just above the navigation bar in the main screen.

### 2. Interstitial Ads
Interstitial ads are full-screen ads that can be displayed at strategic points in the app flow, such as between screen transitions or after completing certain actions.

## Files Added/Modified

1. **Ad Provider (`lib/providers/ad_provider.dart`)**
   - Manages the loading and displaying of both banner and interstitial ads
   - Contains test ad unit IDs that should be replaced with real ones for production

2. **Ad Helper (`lib/utils/ad_helper.dart`)**
   - Utility class to help manage interstitial ad frequency
   - Currently set to show an interstitial ad every 5 actions

3. **Main Screen (`lib/screens/main_screen.dart`)**
   - Modified to display banner ads above the bottom navigation bar
   - Initializes the ad provider when the screen loads

4. **Main App (`lib/main.dart`)**
   - Added AdProvider to the MultiProvider list
   - Initialized the Mobile Ads SDK

5. **AndroidManifest.xml**
   - Added the required AdMob application ID (currently using test ID)

## How to Use Interstitial Ads

To show interstitial ads at strategic points in your app:

```dart
// Import the ad helper
import '../utils/ad_helper.dart';

// Then in your widget or screen:
void someAction() {
  // Perform your action
  
  // Show an interstitial ad based on frequency
  AdHelper().showInterstitialAd(context);
}
```

## Replacing Test Ads with Production Ads

Before releasing the app to production, you need to replace the test ad unit IDs with your actual production IDs:

1. Create an AdMob account if you don't have one already at https://apps.admob.com/
2. Create a new app in the AdMob console and get your app ID and ad unit IDs
3. Replace the test IDs in `lib/providers/ad_provider.dart`:
   ```dart
   // Replace these with your actual production ad unit IDs
   static const String _bannerAdUnitIdAndroid = 'YOUR_PRODUCTION_BANNER_AD_UNIT_ID';
   static const String _interstitialAdUnitIdAndroid = 'YOUR_PRODUCTION_INTERSTITIAL_AD_UNIT_ID';
   ```
4. Replace the test application ID in `android/app/src/main/AndroidManifest.xml`:
   ```xml
   <meta-data
       android:name="com.google.android.gms.ads.APPLICATION_ID"
       android:value="YOUR_PRODUCTION_APP_ID"/>
   ```

## Best Practices

1. **User Experience**: Ensure ads don't disrupt the user experience. Interstitial ads should be shown at natural breaks in the app flow.

2. **Testing**: Always test your ads thoroughly before releasing to production.

3. **Compliance**: Make sure your app complies with Google's ad policies and guidelines.

4. **Performance**: Monitor the impact of ads on app performance and adjust as needed.

## Troubleshooting

If ads are not showing:

1. Check that you have an active internet connection
2. Verify that the ad unit IDs are correct
3. Check the console logs for any error messages
4. Make sure the AdMob account is properly set up and approved

## Additional Ad Formats

You can extend the current implementation to include other ad formats such as:

- **Rewarded Ads**: Offer users rewards for watching video ads
- **Native Ads**: Ads that match the look and feel of your app

Refer to the [Google Mobile Ads Flutter plugin documentation](https://pub.dev/packages/google_mobile_ads) for more information on implementing these ad formats.