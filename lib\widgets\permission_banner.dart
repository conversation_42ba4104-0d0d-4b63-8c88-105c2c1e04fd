import 'package:flutter/material.dart';
import '../services/permission_service.dart';
import '../utils/color_utils.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionBanner extends StatelessWidget {
  final Function() onRequestPermissions;
  final PermissionType permissionType;
  final bool dismissible;

  const PermissionBanner({
    super.key,
    required this.onRequestPermissions,
    required this.permissionType,
    this.dismissible = true,
  });

  @override
  Widget build(BuildContext context) {
    final String title = permissionType == PermissionType.storage
        ? 'Storage Access Needed'
        : 'Stay Updated';

    final String message = permissionType == PermissionType.storage
        ? 'Access to storage is needed to download and play bhajans offline.'
        : 'Enable notifications to receive updates about new bhajans and events.';

    final IconData icon = permissionType == PermissionType.storage
        ? Icons.folder_open
        : Icons.notifications;

    final Color bannerColor = Theme.of(context).primaryColor.withOpacity(0.05);
    final Color borderColor = Theme.of(context).primaryColor.withOpacity(0.3);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: bannerColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: borderColor),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {}, // Empty to show ripple without triggering anything
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.getPrimaryColor(context),
                  size: 28,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Column(
                  children: [
                    ElevatedButton(
                      onPressed: onRequestPermissions,
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.getPrimaryColor(context),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      child: Text(
                        permissionType == PermissionType.storage
                            ? 'Allow'
                            : 'Enable',
                      ),
                    ),
                    if (dismissible)
                      TextButton(
                        onPressed: () {
                          // Close banner using a callback
                          Navigator.of(context).pop();
                        },
                        child: const Text(
                          'Not Now',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class PermissionDialog extends StatelessWidget {
  final List<PermissionType> permissions;
  final Function() onGrantPressed;
  final Function() onCancelPressed;

  const PermissionDialog({
    Key? key,
    required this.permissions,
    required this.onGrantPressed,
    required this.onCancelPressed,
  }) : super(key: key);

  String _getPermissionText() {
    final List<String> permissionTexts = [];

    if (permissions.contains(PermissionType.storage) ||
        permissions.contains(PermissionType.all)) {
      permissionTexts.add(
          '• Storage access for downloading wallpapers and avatars\n• Audio permissions for playing bhajans');
    }

    if (permissions.contains(PermissionType.notification) ||
        permissions.contains(PermissionType.all)) {
      permissionTexts.add('• Notification permissions for daily reminders');
    }

    return permissionTexts.join('\n');
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Permissions Required'),
      content: Text(
        'Hindu Path needs certain permissions to provide you with the best experience:\n\n'
        '${_getPermissionText()}\n\n'
        'Please grant these permissions for the app to function properly.',
      ),
      actions: [
        TextButton(
          onPressed: onCancelPressed,
          child: const Text('Not Now'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.getPrimaryColor(context),
          ),
          onPressed: onGrantPressed,
          child: const Text('Grant', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}

class PermissionSettingsDialog extends StatelessWidget {
  final String message;
  final Function(bool) onResult;

  const PermissionSettingsDialog({
    Key? key,
    required this.message,
    required this.onResult,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Permissions Required'),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => onResult(false),
          child: const Text('Not Now'),
        ),
        TextButton(
          onPressed: () => onResult(true),
          child: const Text('Open Settings'),
        ),
      ],
    );
  }
}

/// A utility class for handling permissions with a unified interface
class PermissionManager {
  static final PermissionService _service = PermissionService();

  /// Request permissions with a dialog
  static Future<bool> requestPermissionsWithDialog(
    BuildContext context,
    List<PermissionType> permissions,
  ) async {
    final bool shouldRequest = await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionDialog(
            permissions: permissions,
            onGrantPressed: () => Navigator.pop(context, true),
            onCancelPressed: () => Navigator.pop(context, false),
          ),
        ) ??
        false;

    if (!shouldRequest || !context.mounted) return false;

    // Request permissions
    List<PermissionResult> results = [];
    if (permissions.contains(PermissionType.all)) {
      results = await _service.requestAllPermissions();
    } else {
      for (var permission in permissions) {
        if (permission == PermissionType.storage) {
          results.add(await _service.requestStoragePermission());
        } else if (permission == PermissionType.notification) {
          results.add(await _service.requestNotificationPermission());
        }
      }
    }

    // Check if any permission was denied
    final bool anyDenied = results.any((result) => !result.isGranted);

    // If any permission was denied, show settings dialog
    if (anyDenied && context.mounted) {
      final openSettings = await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (context) => PermissionSettingsDialog(
              message:
                  'Hindu Path needs certain permissions to provide you with the best experience:\n\n'
                  '${_getPermissionTextsFromResults(results)}\n\n'
                  'Please grant these permissions in your device settings.',
              onResult: (result) => Navigator.pop(context, result),
            ),
          ) ??
          false;

      if (openSettings) {
        await openAppSettings();
      }

      return !anyDenied;
    }

    return !anyDenied;
  }

  static String _getPermissionTextsFromResults(List<PermissionResult> results) {
    final deniedPermissions = results.where((result) => !result.isGranted);
    final List<String> messages = [];

    for (var result in deniedPermissions) {
      if (result.type == PermissionType.storage) {
        messages.add('• Storage access for downloading and saving content');
      } else if (result.type == PermissionType.notification) {
        messages.add('• Notification permissions for reminders');
      }
    }

    return messages.join('\n');
  }
}
