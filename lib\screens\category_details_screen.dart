import 'package:flutter/material.dart';
import '../utils/color_utils.dart';
import '../models/bhajan.dart';
import '../providers/audio_provider.dart';
import '../screens/player_screen.dart';
import '../widgets/mini_player.dart';
import 'package:provider/provider.dart';

class CategoryDetailsScreen extends StatefulWidget {
  final String categoryId;
  final String categoryName;

  const CategoryDetailsScreen({
    Key? key,
    required this.categoryId,
    required this.categoryName,
  }) : super(key: key);

  @override
  State<CategoryDetailsScreen> createState() => _CategoryDetailsScreenState();
}

class _CategoryDetailsScreenState extends State<CategoryDetailsScreen> {
  bool _isLoading = true;
  List<Bhajan> _categoryBhajans = [];

  @override
  void initState() {
    super.initState();
    _loadBhajans();
  }

  Future<void> _loadBhajans() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      // Get all bhajans - in a real app we'd filter by category ID in the database
      // but for now we'll just show all bhajans in the category
      final filteredBhajans = audioProvider.bhajans;

      setState(() {
        _categoryBhajans = filteredBhajans;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading category bhajans: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _playPlaylist() {
    if (_categoryBhajans.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No bhajans in this category'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    // Play the category bhajans but don't navigate to full player
    audioProvider.playMultipleBhajans(_categoryBhajans, 0);
    
    // Let the mini player appear instead of navigating to full player
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        final hasVisibleMiniPlayer = audioProvider.audioService.getCurrentBhajan() != null && 
                                    !audioProvider.isMiniPlayerDismissed;
        
        return Scaffold(
          backgroundColor: const Color(0xFF0D0D0D),
          appBar: AppBar(
            title: Text(widget.categoryName),
            backgroundColor: const Color(0xFF0D0D0D),
            elevation: 0,
          ),
          body: Stack(
            children: [
              // Main content
              _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ElevatedButton.icon(
                          onPressed: _playPlaylist,
                          icon: const Icon(Icons.play_arrow, color: Colors.white),
                          label: const Text('Play'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFE63946),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _categoryBhajans.length,
                          padding: EdgeInsets.only(
                            bottom: hasVisibleMiniPlayer ? 70.0 : 10.0,
                          ),
                          itemBuilder: (context, index) {
                            final bhajan = _categoryBhajans[index];
                            return ListTile(
                              title: Text(
                                bhajan.title,
                                style: const TextStyle(color: Colors.white),
                              ),
                              subtitle: Text(
                                bhajan.artist,
                                style:
                                    TextStyle(color: Colors.white.withOpacity(0.7)),
                              ),
                              onTap: () {
                                final audioProvider = Provider.of<AudioProvider>(
                                    context,
                                    listen: false);
                                // Just play the bhajan without navigating to full player
                                audioProvider.playBhajan(
                                    audioProvider.bhajans.indexOf(bhajan));
                                    
                                // Let the mini player appear instead of navigating to full player
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  
              // Mini player
              if (hasVisibleMiniPlayer)
                Positioned(
                  left: 4,
                  right: 4,
                  bottom: 4,
                  child: Material(
                    color: Colors.transparent,
                    elevation: 0,
                    shadowColor: Colors.transparent,
                    child: GestureDetector(
                      onTap: () {
                        // Get the current bhajan before navigating
                        final Bhajan? currentBhajan =
                            audioProvider.audioService.getCurrentBhajan();
                        if (currentBhajan != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  PlayerScreen(bhajan: currentBhajan),
                            ),
                          );
                        }
                      },
                      child: const MiniPlayer(),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
