import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/avatar.dart';
import '../providers/avatar_provider.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../services/permission_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../services/auth_service.dart';
import 'package:permission_handler/permission_handler.dart';
import '../widgets/permission_banner.dart';

class AvatarViewerScreen extends StatefulWidget {
  final List<Avatar> avatars;
  final int initialIndex;

  const AvatarViewerScreen({
    super.key,
    required this.avatars,
    required this.initialIndex,
  });

  @override
  State<AvatarViewerScreen> createState() => _AvatarViewerScreenState();
}

class _AvatarViewerScreenState extends State<AvatarViewerScreen>
    with WidgetsBindingObserver {
  late PageController _pageController;
  bool _isDownloading = false;
  bool _showPermissionBanner = false;
  late ValueNotifier<int> _currentPageNotifier;
  final PermissionService _permissionService = PermissionService();
  static const platform = MethodChannel('com.studiohabre.hindupath/gallery');
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialIndex);
    _currentPageNotifier = ValueNotifier<int>(widget.initialIndex);

    _pageController.addListener(() {
      if (_pageController.position.isScrollingNotifier.value == false) {
        _currentPageNotifier.value =
            _pageController.page?.round() ?? widget.initialIndex;
      }
    });

    WidgetsBinding.instance.addObserver(this);
    _checkPermissionStatus();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPermissionStatus();
    }
  }

  Future<void> _checkPermissionStatus() async {
    // Force update the permissions status
    await _permissionService.updatePermissionsStatus();
    bool permissionDenied = !await _permissionService.hasStoragePermission;
    
    debugPrint('Checking avatar screen storage permission status: granted=${!permissionDenied}');

    if (mounted) {
      setState(() {
        _showPermissionBanner = permissionDenied;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _currentPageNotifier.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _requestStoragePermission() async {
    if (!mounted) return;

    final result = await _permissionService.requestStoragePermission();
    final permissionGranted = result.isGranted;

    if (permissionGranted) {
      setState(() {
        _showPermissionBanner = false;
      });
    } else if (mounted) {
      bool openSettings = await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (context) => PermissionSettingsDialog(
              message:
                  'Please enable storage permission in your device settings to download avatars.\n\n'
                  'For Android 11+: Go to App Settings > Permissions > Files and Media > Allow management of all files',
              onResult: (result) => Navigator.pop(context, result),
            ),
          ) ??
          false;

      if (openSettings) {
        await openAppSettings();
      }
    }
  }

  Future<bool> _hasRequiredPermissions() async {
    return await _permissionService.hasRequiredPermissions();
  }

  Future<void> _saveToGallery(String filePath) async {
    try {
      final result = await platform.invokeMethod('saveToGallery', {
        'filePath': filePath,
      });
      print('Save to gallery result: $result');
      return result;
    } catch (e) {
      print('Error saving to gallery: $e');
      throw e;
    }
  }

  Future<void> _downloadAvatar(Avatar avatar) async {
    if (_isDownloading) return;

    // Force check permissions (don't rely on cached values)
    if (!await _hasRequiredPermissions()) {
      await _requestStoragePermission();
      // Check again after permission request
      if (!await _hasRequiredPermissions()) {
        return;
      }
    }

    try {
      setState(() {
        _isDownloading = true;
      });

      // Download image
      final response = await http.get(Uri.parse(avatar.url));

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to download image: HTTP ${response.statusCode}');
      }

      // First save to temporary file
      final tempDir = await getTemporaryDirectory();
      final fileExtension = avatar.filename.split('.').last;
      final uniqueName =
          'hindupath_avatar_${DateTime.now().millisecondsSinceEpoch}.$fileExtension';
      final tempFile = File('${tempDir.path}/$uniqueName');

      await tempFile.writeAsBytes(response.bodyBytes);
      print('Temp file saved at: ${tempFile.path}');

      // Then use our platform channel to save it to gallery
      await _saveToGallery(tempFile.path);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Avatar saved to gallery',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.black,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to download avatar: $e',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.black,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      print('Error downloading avatar: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Stack(
        children: [
          // Avatar PageView
          PageView.builder(
            controller: _pageController,
            itemCount: widget.avatars.length,
            onPageChanged: (index) {
              _currentPageNotifier.value = index;
            },
            itemBuilder: (context, index) {
              final avatar = widget.avatars[index];
              return GestureDetector(
                onTap: () {
                  if (MediaQuery.of(context).platformBrightness ==
                      Brightness.dark) {
                    SystemChrome.setEnabledSystemUIMode(
                        SystemUiMode.immersiveSticky);
                  } else {
                    SystemChrome.setEnabledSystemUIMode(
                        SystemUiMode.edgeToEdge);
                  }
                },
                child: Hero(
                  tag: 'avatar-${avatar.id}',
                  child: CachedNetworkImage(
                    imageUrl: avatar.url,
                    fit: BoxFit.contain,
                    placeholder: (context, url) => Container(
                      color: const Color(0xFF222222),
                      child: const Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFF1b4332),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: const Color(0xFF222222),
                      child: const Center(
                        child: Icon(Icons.error, color: Colors.white),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          // Action buttons
          Positioned(
            right: 16,
            bottom: 16 + MediaQuery.of(context).padding.bottom,
            child: ValueListenableBuilder<int>(
              valueListenable: _currentPageNotifier,
              builder: (context, currentPage, child) {
                final currentAvatar = widget.avatars[currentPage];
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Favorite button - only show when logged in
                    if (_authService.currentUser != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: Consumer<AvatarProvider>(
                          builder: (context, provider, child) {
                            final isFavorite =
                                provider.isFavorite(currentAvatar.id);
                            return IconButton(
                              icon: Icon(
                                isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: isFavorite ? Colors.red : Colors.white,
                              ),
                              onPressed: () {
                                provider.toggleFavorite(currentAvatar.id);
                                // Show SnackBar when favorite status changes
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      isFavorite
                                          ? 'Removed from favorites'
                                          : 'Added to favorites',
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                    backgroundColor: Colors.black,
                                    duration: const Duration(seconds: 2),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    // Download button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: _isDownloading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.download, color: Colors.white),
                        onPressed: _isDownloading
                            ? null
                            : () => _downloadAvatar(currentAvatar),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          if (_isDownloading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          if (_showPermissionBanner)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: PermissionBanner(
                onRequestPermissions: _requestStoragePermission,
                permissionType: PermissionType.storage,
                dismissible: true,
              ),
            ),
        ],
      ),
    );
  }
}
