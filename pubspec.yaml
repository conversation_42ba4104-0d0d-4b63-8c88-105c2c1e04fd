name: hindupath
description: A Hindu Bhajan Player App
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  google_sign_in: ^6.1.6
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  just_audio: ^0.9.36
  just_audio_background: ^0.0.1-beta.11
  audio_video_progress_bar: ^2.0.1
  provider: ^6.1.1
  cached_network_image: ^3.3.1
  rxdart: ^0.27.7
  scrollable_positioned_list: ^0.3.8
  http: ^1.1.0
  intl: ^0.19.0
  path_provider: ^2.1.5
  shared_preferences: ^2.5.3
  numberpicker: ^2.1.2
  dio: ^5.3.3
  nepali_utils: ^3.0.3
  table_calendar: ^3.0.9
  permission_handler: ^11.4.0
  share_plus: ^10.1.4
  device_info_plus: ^11.3.0
  image_picker: ^1.0.7
  flutter_staggered_grid_view: ^0.7.0
  flutter_cache_manager: ^3.4.1
  awesome_notifications: ^0.9.3
  timezone: ^0.9.2
  geolocator: ^13.0.4
  get_it: ^8.0.3
  google_mobile_ads: ^4.0.0
  url_launcher: ^6.2.5

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/logo.png
    - assets/images/studio_habre.png
    - assets/images/hindu_temples.png
    - assets/images/event.png
    - assets/icons/bhajan.png
    - assets/icons/home.png
    - assets/icons/naamkaran.png
    - assets/icons/wallpaper.png
    - assets/icons/focus.png
    - assets/icons/avatar.png
    - assets/icons/google.png
    - assets/icons/guest.png
    - assets/icons/theme.png
    - assets/naamkaran/namakaran_database.json
    - assets/hindu_events/events_india.json
    - assets/hindu_events/events_nepal.json
    - assets/hindu_quotes/hindupath_quotes.json
    - assets/ringtones_artwork/rtaw(1).png
    - assets/ringtones_artwork/rtaw (2).png
    - assets/ringtones_artwork/rtaw (3).png
    - assets/images/deities.png
    - assets/images/lord_krishna.png
    - assets/images/splash_image.png
    - assets/images/mahabharat_thumbnail.jpg

  fonts:
    - family: Prakrta
      fonts:
        - asset: assets/fonts/Prakrta.ttf
    - family: JosefinSans
      fonts:
        - asset: assets/fonts/JosefinSans-Regular.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
