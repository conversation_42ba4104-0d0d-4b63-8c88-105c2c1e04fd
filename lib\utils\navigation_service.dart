import 'package:flutter/material.dart';
import '../screens/main_screen.dart';

/// A service that provides navigation functionality throughout the app without requiring BuildContext
class NavigationService {
  // Global navigator key to be used in MaterialApp
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // Navigate to a named route
  static Future<dynamic>? navigateTo(String routeName, {dynamic arguments}) {
    return navigatorKey.currentState
        ?.pushNamed(routeName, arguments: arguments);
  }

  // Navigate and remove all previous routes
  static Future<dynamic>? navigateToReplaceAll(String routeName,
      {dynamic arguments}) {
    return navigatorKey.currentState?.pushNamedAndRemoveUntil(
        routeName, (Route<dynamic> route) => false,
        arguments: arguments);
  }

  // Navigate back to MainScreen's home tab
  static Future<dynamic>? navigateToHomeTab() {
    return navigatorKey.currentState?.pushNamedAndRemoveUntil(
        '/home', (Route<dynamic> route) => false,
        arguments: 0); // Pass 0 as initialIndex to show the home tab
  }

  // Pop to the previous route with special handling for MainScreen
  static void goBack() {
    final context = navigatorKey.currentContext;
    if (context != null) {
      // Check if current page is MainScreen but not on home tab
      final currentRoute = ModalRoute.of(context);
      if (currentRoute != null &&
          currentRoute.settings.name == '/home' &&
          context.findAncestorWidgetOfExactType<MainScreen>() != null) {
        // This is a MainScreen but possibly not on home tab
        // Using Navigator.pop will trigger WillPopScope in MainScreen
        Navigator.of(context).pop();
        return;
      }
    }

    // Default behavior
    navigatorKey.currentState?.pop();
  }

  // Check if we can go back
  static bool canGoBack() {
    return navigatorKey.currentState?.canPop() ?? false;
  }
}
