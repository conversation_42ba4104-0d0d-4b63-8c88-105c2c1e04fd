import 'package:flutter/material.dart';
import '../widgets/date_event_widget.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../providers/avatar_provider.dart';
import '../screens/avatar_viewer_screen.dart';
import '../pages/avatars_page.dart';
import '../pages/ringtones_page.dart';
import '../pages/mantras_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../utils/color_utils.dart';
import '../pages/focus_page.dart';
import '../widgets/hindu_quote_widget.dart';
import '../widgets/latest_wallpapers_row.dart';
import '../widgets/stories_row_widget.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildPillButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    ImageProvider? imageIcon,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6),
        child: Material(
          color: AppColors.getPrimaryColor(context),
          borderRadius: BorderRadius.circular(30),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(30),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (imageIcon != null)
                    Image(
                      image: imageIcon,
                      width: 20,
                      height: 20,
                      color: Colors.white,
                    )
                  else
                    Icon(
                      icon,
                      color: Colors.white,
                      size: 20,
                    ),
                  const SizedBox(width: 8),
                  Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Define a consistent spacing value
    const double sectionSpacing = 24.0;

    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom + 80),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date and upcoming event widget
            const Padding(
              padding: EdgeInsets.only(top: 16.0),
              child: DateEventWidget(),
            ),

            const SizedBox(height: sectionSpacing),

            // Recent Avatars Section
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const AvatarsPage()),
                  );
                },
                child: Row(
                  children: [
                    Text(
                      'Recent Avatars',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 18,
                        fontFamily: 'Prakrta',
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ],
                ),
              ),
            ),

            // Recent Avatars Row
            SizedBox(
              height: 80,
              child: Consumer<AvatarProvider>(
                builder: (context, avatarProvider, child) {
                  if (avatarProvider.isLoading) {
                    return Center(
                      child: CircularProgressIndicator(
                        color: AppColors.getPrimaryColor(context),
                      ),
                    );
                  }

                  if (avatarProvider.error != null) {
                    return Center(
                      child: Text(
                        'Failed to load avatars',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    );
                  }

                  final recentAvatars = avatarProvider.avatars.take(7).toList();

                  if (recentAvatars.isEmpty) {
                    return Center(
                      child: Text(
                        'No avatars available',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    scrollDirection: Axis.horizontal,
                    itemCount: recentAvatars.length,
                    itemBuilder: (context, index) {
                      final avatar = recentAvatars[index];
                      return Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AvatarViewerScreen(
                                  avatars: recentAvatars,
                                  initialIndex: index,
                                ),
                              ),
                            );
                          },
                          child: Hero(
                            tag: 'avatar-${avatar.id}',
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: AppColors.getPrimaryColor(context),
                                  width: 2,
                                ),
                              ),
                              child: ClipOval(
                                child: CachedNetworkImage(
                                  imageUrl: avatar.url,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color: const Color(0xFF222222),
                                    child: Center(
                                      child: SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: AppColors.getPrimaryColor(
                                              context),
                                        ),
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                    color: const Color(0xFF222222),
                                    child: const Icon(
                                      Icons.error,
                                      color: Colors.white70,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),

            const SizedBox(height: sectionSpacing),

            // Quick Access Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  _buildPillButton(
                    context: context,
                    label: 'Mantras',
                    icon: Icons.auto_stories,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MantrasPage(),
                        ),
                      );
                    },
                  ),
                  _buildPillButton(
                    context: context,
                    label: 'Ringtones',
                    icon: Icons.music_note,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const RingtonesPage()),
                      );
                    },
                  ),
                  _buildPillButton(
                    context: context,
                    label: 'Focus',
                    icon: Icons.self_improvement,
                    imageIcon: const AssetImage('assets/icons/focus.png'),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const FocusPage()),
                      );
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: sectionSpacing),

            // Latest Wallpapers Row
            const LatestWallpapersRow(),

            const SizedBox(height: sectionSpacing),

            // Hindu Quote Widget
            const HinduQuoteWidget(),

            const SizedBox(height: sectionSpacing),

            // Stories Widget
            const StoriesRowWidget(),

            // Dynamic bottom padding for scrolling that accounts for mini player and ads
            Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                double padding = 30.0; // Reduced padding to minimize excess space
                final bool isMiniPlayerVisible =
                    audioProvider.audioService.getCurrentBhajan() != null &&
                        !audioProvider.isMiniPlayerDismissed;
                if (isMiniPlayerVisible) {
                  padding += 60.0;
                }
                return SizedBox(height: padding);
              },
            ),
          ],
        ),
      ),
    );
  }
}
