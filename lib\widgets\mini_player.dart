import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../services/audio_service.dart';
import '../services/audio_manager.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../screens/player_screen.dart';
import '../models/bhajan.dart';

class MiniPlayer extends StatefulWidget {
  const MiniPlayer({super.key});

  @override
  State<MiniPlayer> createState() => _MiniPlayerState();
}

class _MiniPlayerState extends State<MiniPlayer> {
  AudioProvider? _audioProvider;
  String? _lastPlayedBhajanUrl;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Get the audio provider and listen to changes
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);

    // Only set up the listener once
    if (_audioProvider != audioProvider) {
      _audioProvider = audioProvider;

      // Listen to both player state and current bhajan streams
      audioProvider.audioService.playerStateStream.listen((playerState) {
        if (playerState.playing) {
          final currentBhajan = audioProvider.audioService.getCurrentBhajan();
          if (currentBhajan != null &&
              currentBhajan.r2Url != _lastPlayedBhajanUrl &&
              !audioProvider.isMiniPlayerDismissed) {
            _lastPlayedBhajanUrl = currentBhajan.r2Url;
            // Show mini player when a new bhajan starts playing and mini player is not dismissed
            audioProvider.showMiniPlayer();
          }
        }
      });
      
      // Also track current bhajan changes
      audioProvider.audioService.currentBhajanStream.listen((currentBhajan) {
        if (currentBhajan != null && !audioProvider.isMiniPlayerDismissed) {
          // When current bhajan changes and mini player is not dismissed, ensure mini player is visible
          WidgetsBinding.instance.addPostFrameCallback((_) {
            audioProvider.showMiniPlayer();
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final audioProvider = Provider.of<AudioProvider>(context);
    
    // No longer auto-fix the mini player if it was explicitly dismissed
    // Only auto-show when player is initialized for the first time
    
    // Check if a current bhajan exists
    final currentBhajan = audioProvider.audioService.getCurrentBhajan();
    if (currentBhajan != null && !audioProvider.isMiniPlayerDismissed) {
      // Return immediately without further stream builders to speed up initial appearance
      return AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 300),
        child: _buildMiniPlayerContent(context, audioProvider, currentBhajan),
      );
    }
    
    // If the player was dismissed, don't show the mini player
    if (audioProvider.isMiniPlayerDismissed) {
      return const SizedBox.shrink();
    }
    
    // Use a StreamBuilder to listen to current bhajan changes
    return StreamBuilder<Bhajan?>(
      stream: audioProvider.audioService.currentBhajanStream,
      builder: (context, snapshot) {
        final currentBhajan = snapshot.data;
        
        // If no bhajan is playing, don't show the mini player
        if (currentBhajan == null) {
          return const SizedBox.shrink();
        }
        
        // Use AnimatedOpacity to smoothly show the mini player
        return AnimatedOpacity(
          opacity: 1.0,
          duration: const Duration(milliseconds: 300),
          child: _buildMiniPlayerContent(context, audioProvider, currentBhajan),
        );
      },
    );
  }
  
  // Extracted method to build the mini player content to avoid duplication
  Widget _buildMiniPlayerContent(BuildContext context, AudioProvider audioProvider, Bhajan currentBhajan) {
    return StreamBuilder<PlayerState>(
      stream: audioProvider.audioService.playerStateStream,
      builder: (context, snapshot) {
        final playerState = snapshot.data;
        final playing = playerState?.playing ?? true;
        
        // Use another StreamBuilder to listen to current bhajan changes
        return StreamBuilder<Bhajan?>(
          stream: audioProvider.audioService.currentBhajanStream,
          initialData: currentBhajan,
          builder: (context, bhajanSnapshot) {
            // Get the most current bhajan
            final latestBhajan = bhajanSnapshot.data ?? currentBhajan;

            // Base container with transparent background
            Widget content = Container(
              height: 60,
              margin: const EdgeInsets.symmetric(horizontal: 0),
              decoration: BoxDecoration(
                color: const Color(0xFF4a5759),
                borderRadius: const BorderRadius.all(Radius.circular(14)),
              ),
              child: Row(
                children: [
                  // Album artwork
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: Hero(
                      tag: 'artwork-${latestBhajan.r2Url}',
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: CachedNetworkImage(
                          imageUrl: latestBhajan.artworkUrl,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.music_note, size: 20),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.error, size: 20),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Title and artist
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          latestBhajan.title,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          latestBhajan.artist,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Controls
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.skip_previous,
                            color: Colors.white, size: 22),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        onPressed: () =>
                            audioProvider.audioService.playPrevious(),
                      ),
                      const SizedBox(width: 8),
                      StreamBuilder<PlayerState>(
                        stream: audioProvider.audioService.playerStateStream,
                        builder: (context, snapshot) {
                          final playerState = snapshot.data;
                          final processingState =
                              playerState?.processingState;
                          final playing = playerState?.playing;

                          if (processingState == ProcessingState.loading ||
                              processingState == ProcessingState.buffering) {
                            return Container(
                              margin: const EdgeInsets.all(8),
                              width: 24,
                              height: 24,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white),
                              ),
                            );
                          }

                          return IconButton(
                            icon: Icon(
                              playing == true
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              color: Colors.white,
                              size: 24,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () => audioProvider.audioService.togglePlayPause(),
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.skip_next,
                            color: Colors.white, size: 22),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        onPressed: () =>
                            audioProvider.audioService.playNext(),
                      ),
                      const SizedBox(width: 16),
                    ],
                  ),
                ],
              ),
            );

            // If paused, wrap with Dismissible
            if (!playing) {
              content = Dismissible(
                key: ValueKey(
                    'mini_player_${latestBhajan.r2Url}_${DateTime.now().millisecondsSinceEpoch}'),
                direction: DismissDirection.endToStart,
                background: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D0D0D).withOpacity(0.2),
                    borderRadius: const BorderRadius.all(Radius.circular(14)),
                  ),
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(right: 16.0),
                  child: Icon(
                    Icons.close,
                    color: Colors.white.withOpacity(0.7),
                    size: 20,
                  ),
                ),
                onDismissed: (_) async {
                  // First pause the audio
                  await audioProvider.audioService.pause();
                  
                  // Log status before dismissal
                  debugPrint('MiniPlayer: Dismissing mini player with swipe gesture');

                  // Use the updated dismissMiniPlayer that also hides the notification
                  audioProvider.dismissMiniPlayer();
                },
                child: content,
              );
            }

            return content;
          },
        );
      },
    );
  }
}
