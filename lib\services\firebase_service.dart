import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FirebaseService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Auth methods
  static Future<UserCredential?> signInAnonymously() async {
    try {
      return await _auth.signInAnonymously();
    } catch (e) {
      print('Error signing in anonymously: $e');
      return null;
    }
  }

  // Firestore methods
  static Future<void> saveBhajanProgress(String bhajanId, double progress) async {
    try {
      String? userId = _auth.currentUser?.uid;
      if (userId != null) {
        await _firestore.collection('user_progress').doc(userId).set({
          'bhajans': {
            bhajanId: {
              'progress': progress,
              'lastPlayed': FieldValue.serverTimestamp(),
            }
          }
        }, SetOptions(merge: true));
      }
    } catch (e) {
      print('Error saving bhajan progress: $e');
    }
  }

  static Future<Map<String, dynamic>?> getBhajanProgress(String bhajanId) async {
    try {
      String? userId = _auth.currentUser?.uid;
      if (userId != null) {
        DocumentSnapshot doc = await _firestore
            .collection('user_progress')
            .doc(userId)
            .get();
        
        if (doc.exists) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          return data['bhajans']?[bhajanId];
        }
      }
      return null;
    } catch (e) {
      print('Error getting bhajan progress: $e');
      return null;
    }
  }

  // Storage methods
  static Future<String?> getBhajanAudioUrl(String bhajanId) async {
    try {
      return await _storage.ref('bhajans/$bhajanId.mp3').getDownloadURL();
    } catch (e) {
      print('Error getting bhajan audio URL: $e');
      return null;
    }
  }

  static Future<String?> getBhajanImageUrl(String bhajanId) async {
    try {
      return await _storage.ref('bhajan_images/$bhajanId.jpg').getDownloadURL();
    } catch (e) {
      print('Error getting bhajan image URL: $e');
      return null;
    }
  }
}