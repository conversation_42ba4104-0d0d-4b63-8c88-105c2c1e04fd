import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class AppColorWrapper extends StatelessWidget {
  final Widget Function(Color primaryColor) builder;

  const AppColorWrapper({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    // Listen to theme changes and rebuild when color changes
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        // Pass the current primary color to the builder
        return builder(themeProvider.primaryColor);
      },
    );
  }
}

// Extension to add theme-aware color access to BuildContext
extension ThemeContextExtension on BuildContext {
  Color get appPrimaryColor => ThemeProvider.getGreenColor(this);
  Color get appBackgroundColor =>
      Provider.of<ThemeProvider>(this).backgroundColor;
}
