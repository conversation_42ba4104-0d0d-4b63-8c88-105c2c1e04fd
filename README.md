# Hindu Path

A Flutter application for playing Hindu bhajans, viewing spiritual content, and more.

## Features

- Bhajan player with background playback
- Hindu events calendar
- Spiritual quotes
- <PERSON><PERSON><PERSON><PERSON> (Name suggestion)
- Temple wallpapers
- Multiple themes

## Setup

1. **Flutter Setup**
   - Ensure you have Flutter installed on your system
   - Run `flutter pub get` to install dependencies

2. **Firebase Configuration**
   - Copy `lib/config/firebase_config.template.dart` to `lib/config/firebase_config.dart`
   - Get your Firebase project credentials from the Firebase Console:
     1. Go to [Firebase Console](https://console.firebase.google.com/)
     2. Create or select your project
     3. Go to Project Settings
     4. Under "Your apps", select your Android app or create one
     5. Copy the configuration values
   - Replace the placeholder values in `firebase_config.dart` with your Firebase credentials:
     ```dart
     apiKey: 'YOUR_API_KEY'
     appId: 'YOUR_APP_ID'
     messagingSenderId: 'YOUR_MESSAGING_SENDER_ID'
     projectId: 'YOUR_PROJECT_ID'
     storageBucket: 'YOUR_STORAGE_BUCKET'
     ```

3. **Run the App**
   ```bash
   flutter run
   ```

## Project Structure

- `lib/models/` - Data models
- `lib/pages/` - UI pages
- `lib/providers/` - State management
- `lib/screens/` - Core screens
- `lib/services/` - Business logic
- `lib/utils/` - Helper functions
- `lib/widgets/` - Reusable components

## Security Note

- The `firebase_config.dart` file contains sensitive credentials and is git-ignored
- Never commit your actual Firebase configuration to version control
- Each developer needs to set up their own `firebase_config.dart` file using the template

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Test thoroughly
4. Create a pull request

## License

See the [LICENSE.md](LICENSE.md) file for details.
