import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/wallpaper_provider.dart';
import '../models/wallpaper.dart';
import '../screens/wallpaper_viewer_screen.dart';
import '../utils/font_loader.dart';
import '../pages/wallpapers_page.dart';
import '../screens/main_screen.dart';

class LatestWallpapersRow extends StatelessWidget {
  const LatestWallpapersRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Latest Wallpapers Header
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: GestureDetector(
            onTap: () {
              // Direct navigation to the Wallpapers tab
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainScreen(initialIndex: 1),
                ),
                (route) => false, // Remove all previous routes
              );
            },
            child: Row(
              children: [
                Text(
                  'Latest Wallpapers',
                  style: AppFontLoader.getPrakrtaStyle(
                    fontSize: 18,
                    color: Colors.white.withOpacity(0.9),
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.white.withOpacity(0.7),
                ),
              ],
            ),
          ),
        ),

        // Wallpapers Row
        SizedBox(
          height: 184, // Increased from 160 by ~15%
          child: Consumer<WallpaperProvider>(
            builder: (context, wallpaperProvider, child) {
              if (wallpaperProvider.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF1b4332),
                  ),
                );
              }

              if (wallpaperProvider.error != null) {
                return Center(
                  child: Text(
                    'Failed to load wallpapers',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                );
              }

              final latestWallpapers =
                  wallpaperProvider.wallpapers.take(9).toList();

              if (latestWallpapers.isEmpty) {
                return Center(
                  child: Text(
                    'No wallpapers available',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                itemCount: latestWallpapers.length,
                itemBuilder: (context, index) {
                  final wallpaper = latestWallpapers[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => WallpaperViewerScreen(
                              wallpapers: latestWallpapers,
                              initialIndex: index,
                            ),
                          ),
                        );
                      },
                      child: Hero(
                        tag: 'wallpaper-${wallpaper.id}',
                        child: Container(
                          width: 104, // Increased from 90 by ~15%
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: CachedNetworkImage(
                            imageUrl: wallpaper.effectiveThumbnailUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: const Color(0xFF222222),
                              child: const Center(
                                child: SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Color(0xFF1b4332),
                                  ),
                                ),
                              ),
                            ),
                            memCacheWidth: 200,
                            fadeInDuration: const Duration(milliseconds: 300),
                            errorWidget: (context, url, error) => Container(
                              color: const Color(0xFF222222),
                              child: const Icon(
                                Icons.error,
                                color: Colors.white70,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
