import 'package:flutter/material.dart';
import '../pages/home_page.dart';
import '../pages/wallpapers_page.dart';
import '../pages/bhajan_page.dart';
import '../pages/naamkaran_page.dart';
import '../widgets/app_drawer.dart';
import '../widgets/mini_player.dart';
import '../providers/audio_provider.dart';
import '../screens/player_screen.dart';
import 'package:provider/provider.dart';
import '../utils/color_utils.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../providers/name_provider.dart';
import '../providers/wallpaper_provider.dart';
import '../providers/avatar_provider.dart';
import '../providers/ringtone_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/permission_service.dart';
import '../services/notification_service.dart';
import 'package:flutter/services.dart';
import '../widgets/permission_banner.dart';
import '../models/bhajan.dart';
import 'package:permission_handler/permission_handler.dart';
import '../providers/ad_provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class MainScreen extends StatefulWidget {
  final int? initialIndex;

  const MainScreen({super.key, this.initialIndex});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  int _selectedIndex = 0;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Permission service
  final PermissionService _permissionService = PermissionService();
  final NotificationService _notificationService = NotificationService();
  bool _isInitialized = false;

  // Constants for layout
  static const double kNavBarHeight = 65.0;
  static const double kMiniPlayerHeight = 60.0;

  // Animation controller for bottom navigation
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Widget> _pages = [
    const HomePage(),
    const WallpapersPage(),
    const BhajanPage(),
    const NaamkaranPage(),
  ];

  // BannerAd management
  BannerAd? _bannerAd;
  bool _isBannerAdLoaded = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (widget.initialIndex != null) {
      _selectedIndex = widget.initialIndex!;
    }

    // Initialize ads (no longer using AdProvider for banner)
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-4661791287735662/**********',
      size: AdSize.banner,
      request: AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isBannerAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    )..load();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();

    // Load user favorites and initialize services when MainScreen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Reset audio state to fix mini player spacing when returning from sign-in
      try {
        final audioProvider =
            Provider.of<AudioProvider>(context, listen: false);
        audioProvider.resetAudioState();
      } catch (e) {
        debugPrint('Error resetting audio state: $e');
      }

      // Start 1-minute surfing timer for interstitial ads
      final isAudioPlaying =
          Provider.of<AudioProvider>(context, listen: false).isPlaying;
      Provider.of<AdProvider>(context, listen: false)
          .startSurfingTimer(isAudioPlaying: isAudioPlaying);

      _loadUserFavorites();

      // Initialize services
      await _initializeServices();
    });
  }

  // Initialize notification service
  Future<void> _initializeServices() async {
    if (_isInitialized) return;

    try {
      // Initialize permission service first
      await _permissionService.init();

      // Initialize notification service
      debugPrint('🔄 Initializing notification service...');
      await _notificationService.initialize();

      // Schedule notifications
      debugPrint('🔄 Scheduling notifications...');
      await _notificationService.scheduleNotifications();

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  // Handle permission requests from settings
  Future<void> _requestPermission(PermissionType type) async {
    PermissionResult result;

    switch (type) {
      case PermissionType.storage:
        result = await _permissionService.requestStoragePermission();
        break;
      case PermissionType.notification:
        result = await _permissionService.requestNotificationPermission();
        if (!result.isGranted && mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Notifications Disabled'),
              content: const Text(
                'Notifications help you stay updated on upcoming Hindu events, '
                'festival reminders, and daily tithi changes. '
                'You can enable them in your device settings.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Not Now'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings(); // Opens device settings for the app
                  },
                  child: const Text('Open Settings'),
                ),
              ],
            ),
          );
        }
        break;
      case PermissionType.location:
        result = await _permissionService.requestLocationPermission();
        if (!result.isGranted && mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Location Access Denied'),
              content: const Text(
                'Your location helps us calculate the correct tithi (lunar day) '
                'based on your timezone. Without this, we\'ll use a default '
                'location in the India/Nepal region.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Use Default Location'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings(); // Opens device settings for the app
                  },
                  child: const Text('Open Settings'),
                ),
              ],
            ),
          );
        }
        break;
      default:
        // Request all permissions
        await _permissionService.requestAllPermissions();
    }
  }

  // Load user favorites
  void _loadUserFavorites() {
    // ... existing code ...
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _bannerAd?.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(MainScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialIndex != null &&
        widget.initialIndex != oldWidget.initialIndex) {
      setState(() {
        _selectedIndex = widget.initialIndex!;
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Get the audio provider and ensure mini player visibility
    // Since we're in didChangeDependencies, use Future.microtask to avoid build phase state changes
    Future.microtask(() {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);

      // Listen to player state changes to ensure mini player visibility
      // but now respect user explicit dismissal
      audioProvider.audioService.playerStateStream.listen((playerState) {
        if (playerState.playing) {
          // Don't force the mini player if user explicitly dismissed it
          // audioProvider's showMiniPlayer now handles this logic
          audioProvider.showMiniPlayer();
        }
      });

      // Extra check to ensure no space is reserved when no bhajan is playing
      if (audioProvider.audioService.getCurrentBhajan() == null &&
          !audioProvider.isMiniPlayerDismissed) {
        // Reset the state if we somehow have the mini player space showing but no bhajan
        audioProvider.resetAudioState();
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Show App Open Ad if available
      Provider.of<AdProvider>(context, listen: false).showAppOpenAd();
      // Reset ad counters when app returns to foreground
      Provider.of<AdProvider>(context, listen: false).resetAdCounters();
    }
  }

  void _onItemTapped(int index) {
    if (_selectedIndex == index) return;
    setState(() {
      _selectedIndex = index;
    });
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // If we're not on the home tab (index 0), navigate to home tab instead of exiting
        if (_selectedIndex != 0) {
          setState(() {
            _selectedIndex = 0;
          });
          _animationController.reset();
          _animationController.forward();
          return false; // Prevent default back behavior
        }
        // If we're already on the home tab, allow the app to exit
        return true;
      },
      child: Scaffold(
        key: _scaffoldKey,
        drawer: const AppDrawer(),
        appBar: _buildAppBar(context),
        backgroundColor: const Color(0xFF0D0D0D),
        extendBodyBehindAppBar: false,
        body: Consumer<AudioProvider>(
          builder: (context, audioProvider, child) {
            return Stack(
              children: [
                // Main background
                Container(
                  color: const Color(0xFF0D0D0D),
                ),

                // Main content
                Column(
                  children: [
                    // Main content
                    Expanded(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _pages[_selectedIndex],
                      ),
                    ),
                    // Add empty space for bottom nav bar only
                    const SizedBox(height: kNavBarHeight),
                  ],
                ),

                // Mini player - only show if bhajan is actually playing
                Consumer<AdProvider>(
                  builder: (context, adProvider, child) {
                    // Check if mini player should be visible
                    final bool shouldShowMiniPlayer =
                        audioProvider.audioService.getCurrentBhajan() != null &&
                            !audioProvider.isMiniPlayerDismissed;

                    // Only show mini player if it should be visible
                    return shouldShowMiniPlayer
                        ? AnimatedPositioned(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                            left: 4,
                            right: 4,
                            bottom: kNavBarHeight,
                            child: Material(
                              color: Colors.transparent,
                              elevation: 0,
                              shadowColor: Colors.transparent,
                              child: GestureDetector(
                                onTap: () {
                                  // Get the current bhajan before navigating
                                  final Bhajan? currentBhajan = audioProvider
                                      .audioService
                                      .getCurrentBhajan();
                                  if (currentBhajan != null) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            PlayerScreen(bhajan: currentBhajan),
                                      ),
                                    );
                                  }
                                },
                                child: const MiniPlayer(),
                              ),
                            ),
                          )
                        : const SizedBox.shrink();
                  },
                ),

                // Banner Ad - Dynamic positioning based on mini player visibility
                Consumer2<AdProvider, AudioProvider>(
                  builder: (context, adProvider, audioProvider, child) {
                    // Determine if mini player is visible
                    final bool isMiniPlayerVisible =
                        audioProvider.audioService.getCurrentBhajan() != null &&
                            !audioProvider.isMiniPlayerDismissed;

                    // Position the ad above mini player if visible, otherwise above nav bar
                    return AnimatedPositioned(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      left: 0,
                      right: 0,
                      bottom: isMiniPlayerVisible
                          ? kNavBarHeight + kMiniPlayerHeight
                          : kNavBarHeight,
                      child: _isBannerAdLoaded && _bannerAd != null
                          ? SizedBox(
                              height: 50,
                              child: AdWidget(ad: _bannerAd!),
                            )
                          : const SizedBox(height: 0),
                    );
                  },
                ),

                // Bottom navigation bar
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: _buildBottomNavigationBar(),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    // Get the title based on the selected index
    String title;
    switch (_selectedIndex) {
      case 0:
        title = 'Hindu Path';
        break;
      case 1:
        title = 'Wallpapers';
        break;
      case 2:
        title = 'Bhajans';
        break;
      case 3:
        title = 'Naamkaran';
        break;
      default:
        title = 'Hindu Path';
    }

    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontFamily: 'Prakrta',
          fontSize: 24,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: const Color(0xFF0D0D0D),
      elevation: 0,
      scrolledUnderElevation: 0,
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.transparent,
      leadingWidth:
          100, // Increased to accommodate drawer icon and profile picture
      leading: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () {
              _scaffoldKey.currentState?.openDrawer();
            },
          ),
          Consumer<app_auth.AuthProvider>(
            builder: (context, authProvider, child) {
              // Get the current user
              final user = FirebaseAuth.instance.currentUser;

              // Check if user is signed in and not in guest mode
              if (user != null &&
                  authProvider.isSignedIn &&
                  !authProvider.isGuest) {
                return Padding(
                  padding: const EdgeInsets.only(left: 4.0),
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor: Colors.grey[800],
                    backgroundImage: user.photoURL != null
                        ? NetworkImage(user.photoURL!)
                        : null,
                    child: user.photoURL == null
                        ? const Icon(Icons.person,
                            size: 16, color: Colors.white)
                        : null,
                  ),
                );
              }

              // Show empty container if not signed in or in guest mode
              return Container(width: 0);
            },
          ),
        ],
      ),
      // No actions needed as refresh is now handled by pull-to-refresh
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      height: kNavBarHeight,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(0, 'Home'),
              _buildNavItem(1, 'Wallpapers'),
              _buildNavItem(2, 'Bhajan'),
              _buildNavItem(3, 'Naamkaran'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, String label) {
    final isSelected = _selectedIndex == index;

    // Helper function to get the icon path based on index
    String getIconPath() {
      switch (index) {
        case 0:
          return 'assets/icons/home.png';
        case 1:
          return 'assets/icons/wallpaper.png';
        case 2:
          return 'assets/icons/bhajan.png';
        case 3:
          return 'assets/icons/naamkaran.png';
        default:
          return 'assets/icons/home.png';
      }
    }

    return GestureDetector(
      onTap: () => _onItemTapped(index),
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        height: kNavBarHeight,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeOutCubic,
              padding: EdgeInsets.symmetric(
                horizontal: isSelected ? 24 : 16,
                vertical: 8,
              ),
              decoration: isSelected
                  ? BoxDecoration(
                      color: AppColors.getPrimaryColor(context),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.getPrimaryColor(context)
                              .withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 1,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    )
                  : null,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon with animation
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 200),
                    tween: Tween<double>(
                      begin: isSelected ? 0.8 : 1.0,
                      end: isSelected ? 1.0 : 0.8,
                    ),
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: value,
                        child: Image.asset(
                          getIconPath(),
                          width: 24,
                          height: 24,
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withOpacity(0.5),
                        ),
                      );
                    },
                  ),
                  // Label with animation
                  if (isSelected) ...[
                    const SizedBox(width: 8),
                    AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 200),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.transparent,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      child: Text(label),
                    ),
                  ],
                ],
              ),
            ),
            // Indicator dot
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: 4,
              width: isSelected ? 4 : 0,
              margin: const EdgeInsets.only(top: 4),
              decoration: BoxDecoration(
                color: AppColors.getPrimaryColor(context),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
