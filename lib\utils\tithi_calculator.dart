import 'dart:math';
import 'package:geolocator/geolocator.dart';
import '../services/permission_service.dart';
import 'astro_tithi.dart';

/// A utility class to calculate Hindu Tithi (lunar day)
class TithiCalculator {
  // Tithi names in Sanskrit
  static const List<String> tithiNames = [
    '<PERSON><PERSON><PERSON><PERSON>', // 1
    '<PERSON>witiya', // 2
    '<PERSON><PERSON>ya', // 3
    '<PERSON><PERSON><PERSON>', // 4
    'Pancha<PERSON>', // 5
    'Shashthi', // 6
    'Saptami', // 7
    'Ashtami', // 8
    'Navami', // 9
    'Dashami', // 10
    'Ekadashi', // 11
    '<PERSON>wadashi', // 12
    'Trayoda<PERSON>', // 13
    'Chaturdashi', // 14
    '<PERSON>urnima', // Full Moon
    'Pratipada', // 1
    'Dwitiya', // 2
    'Tritiya', // 3
    'Chaturthi', // 4
    'Panchami', // 5
    'Shashthi', // 6
    'Saptami', // 7
    'Ashtami', // 8
    'Navami', // 9
    '<PERSON><PERSON>', // 10
    'Ekadashi', // 11
    'Dwadashi', // 12
    'Tray<PERSON><PERSON>', // 13
    '<PERSON>tur<PERSON>hi', // 14
    'Amava<PERSON><PERSON>', // New Moon
  ];

  // Paksha (fortnight) names
  static const String shukla = 'Shukla'; // Bright fortnight
  static const String krishna = 'Krishna'; // Dark fortnight

  // Cache for tithi calculations to improve performance
  static final Map<String, Map<String, String>> _tithiCache = {};

  // Cache for location-based tithi calculations
  static final Map<String, Map<String, String>> _locationTithiCache = {};

  // Application-level cache for today's tithi with timestamp
  static Map<String, dynamic>? _appLevelTithiCache;
  static Map<String, dynamic>? _appLevelLocationTithiCache;
  static DateTime? _lastTithiCacheDate;

  /// Clear all caches - useful when changing dates or for testing
  static void clearCache() {
    _tithiCache.clear();
    _locationTithiCache.clear();
    _appLevelTithiCache = null;
    _appLevelLocationTithiCache = null;
    _lastTithiCacheDate = null;
  }

  /// Check if we need to refresh the cache
  static bool _shouldRefreshCache(DateTime date) {
    // Always refresh if no cache exists
    if (_lastTithiCacheDate == null) return true;

    // Refresh if we're on a different day
    return date.year != _lastTithiCacheDate!.year ||
        date.month != _lastTithiCacheDate!.month ||
        date.day != _lastTithiCacheDate!.day;
  }

  /// Calculate the tithi for a given date
  /// Now uses accurate sun and moon ecliptic longitude calculation
  static Map<String, String> calculateTithi(DateTime date) {
    return AstroTithi.getTithiInfo(date);
  }

  /// Calculate tithi based on location coordinates
  /// For now, just use the accurate calculation (location/timezone not yet used)
  static Future<Map<String, String>> calculateTithiWithLocation(
      DateTime date) async {
    // In the future, you can add timezone/location adjustment here
    return AstroTithi.getTithiInfo(date);
  }

  /// Get a formatted tithi string
  static String getFormattedTithi(DateTime date) {
    final Map<String, String> tithiInfo = calculateTithi(date);
    return '${tithiInfo['name']} (${tithiInfo['paksha']})';
  }

  /// Get a formatted tithi string with location awareness
  static Future<String> getFormattedTithiWithLocation(DateTime date) async {
    final Map<String, String> tithiInfo =
        await calculateTithiWithLocation(date);
    return '${tithiInfo['name']} (${tithiInfo['paksha']})';
  }
}
