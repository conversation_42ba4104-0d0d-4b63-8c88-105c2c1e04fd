# Color Theme Migration Guide

This guide explains how to update all hardcoded green color instances in the app to use the new theme-aware color system.

## Why We're Doing This

We want our app to support theme customization, allowing users to choose from different theme colors. To achieve this, we need to replace all hardcoded color references with theme-aware alternatives.

## What to Look For

The primary green colors used throughout the app are:
- `Color(0xFF1b4332)` - Default green
- `Color(0xFF10B981)` - Alternate green

## How to Migrate

### Step 1: Import the Necessary Utilities

Add these imports to the top of your file:

```dart
import '../utils/color_utils.dart';
```

### Step 2: Replace Hardcoded Colors

Use the following patterns to replace hardcoded color references:

#### Simple Color Usage

```dart
// BEFORE
color: const Color(0xFF1b4332),

// AFTER
color: AppColors.getPrimaryColor(context),
```

#### Color with Opacity

```dart
// BEFORE
color: const Color(0xFF1b4332).withOpacity(0.3),

// AFTER
color: AppColors.getPrimaryColorWithOpacity(context, 0.3),
```

#### Button Styles

```dart
// BEFORE
style: ElevatedButton.styleFrom(
  backgroundColor: const Color(0xFF1b4332),
  foregroundColor: Colors.white,
  padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(30),
  ),
),

// AFTER
style: AppColors.getPrimaryButtonStyle(
  context,
  minimumSize: const Size(120, 45), // Use appropriate size
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(30),
  ),
),
```

#### Border Decorations

```dart
// BEFORE
decoration: BoxDecoration(
  border: Border.all(
    color: const Color(0xFF1b4332),
    width: 1.5,
  ),
  borderRadius: BorderRadius.circular(24),
),

// AFTER
decoration: AppColors.getPrimaryBorderDecoration(
  context,
  width: 1.5,
  borderRadius: 24.0,
),
```

#### Conditional Usage

For places where the green color is conditionally applied:

```dart
// BEFORE
color: isSelected ? const Color(0xFF1b4332) : null,

// AFTER
color: isSelected ? AppColors.getPrimaryColor(context) : null,
```

### Step 3: Test Each Color Change

After updating colors in a screen or widget, test it with different theme colors by:
1. Opening the App Theme screen
2. Selecting a different color
3. Verifying that your updated widget reflects the new color correctly

## Additional Helpers

### AppColorWrapper Widget

For complex widgets that need to rebuild when the theme changes:

```dart
AppColorWrapper(
  builder: (primaryColor) => Container(
    color: primaryColor,
    child: Text('Themed Content'),
  ),
)
```

### BuildContext Extension

You can also use the convenient extension on BuildContext:

```dart
// Access primary color
Color color = context.appPrimaryColor;

// Access background color
Color bgColor = context.appBackgroundColor;
```

## Common Files to Check

The following files have multiple instances of hardcoded green colors:

1. date_converter_page.dart
2. mantras_page.dart
3. home_page.dart
4. wallpapers_page.dart
5. ringtone_player_screen.dart
6. date_event_widget.dart
7. bhajan_list_view.dart

## Need Help?

Refer to `lib/utils/color_utils.dart` for all available helper methods, or create additional helpers for specific use cases. 