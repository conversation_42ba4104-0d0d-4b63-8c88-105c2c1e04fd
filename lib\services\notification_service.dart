import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter/material.dart';
import 'permission_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final PermissionService _permissionService = PermissionService();
  bool _isInitialized = false;

  Future<bool> _checkNotificationsEnabled() async {
    try {
      return await _permissionService.hasNotificationPermission;
    } catch (e) {
      debugPrint('Error checking notification permissions: $e');
      return false;
    }
  }

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize time zones
      tz.initializeTimeZones();

      // Set local time zone - using a fixed time zone instead of detecting
      try {
        tz.setLocalLocation(
            tz.getLocation('Asia/Kolkata')); // Default to India time
        debugPrint('Set time zone to: Asia/Kolkata');
      } catch (e) {
        debugPrint('Error setting local time zone: $e');
      }

      // Define high vibration pattern for important notifications
      final List<int> highVibrationPattern = [
        0,
        1000,
        500,
        1000,
        500,
        1000,
        500
      ];

      // Only cancel scheduled notifications, not displayed ones
      await AwesomeNotifications().cancelAllSchedules();
      debugPrint('Canceled all scheduled notifications during initialization');

      await AwesomeNotifications().initialize(
        'resource://drawable/notification_icon',
        [
          NotificationChannel(
            channelKey: 'scheduled_channel',
            channelName: 'Scheduled Notifications',
            channelDescription: 'Channel for scheduled app notifications',
            defaultColor: const Color(0xFF1b4332),
            ledColor: const Color(0xFF1b4332),
            importance: NotificationImportance.High,
            defaultRingtoneType: DefaultRingtoneType.Notification,
            icon: 'resource://drawable/notification_icon',
            enableVibration: true,
            criticalAlerts: true, // Request special delivery privileges
            locked:
                true, // User can't disable this channel from notification settings
          ),
        ],
      );
      debugPrint('Awesome Notifications initialized successfully');

      // Request permission and disable battery optimization
      bool permissionGranted = await requestBatteryOptimizationExemption();
      if (!permissionGranted) {
        debugPrint(
            '⚠️ Notification permissions not granted. Notifications may not work.');
      } else {
        debugPrint('✅ Notification permissions granted successfully');
      }

      _isInitialized = true;
      debugPrint('NotificationService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing notifications: $e');
    }
  }

  Future<bool> requestBatteryOptimizationExemption() async {
    try {
      // Request basic permission
      await AwesomeNotifications().requestPermissionToSendNotifications();

      // Add a small delay to ensure the permission request is processed
      await Future.delayed(const Duration(milliseconds: 300));

      // Check if permission was granted
      final bool isAllowed =
          await AwesomeNotifications().isNotificationAllowed();
      debugPrint(
          'Notification permission status: ${isAllowed ? "GRANTED" : "DENIED"}');

      if (isAllowed) {
        // On Android, guide user to disable battery optimization
        // This won't show any UI on iOS
        try {
          await AwesomeNotifications().checkPermissionList(
            channelKey: 'scheduled_channel',
            permissions: [
              NotificationPermission.CriticalAlert,
              NotificationPermission.Sound,
              NotificationPermission.Badge,
              NotificationPermission.Alert,
              NotificationPermission.FullScreenIntent,
              NotificationPermission.PreciseAlarms,
            ],
          );
          debugPrint('Requested additional notification permissions');
        } catch (e) {
          debugPrint('Error requesting additional permissions: $e');
        }
      }

      return isAllowed;
    } catch (e) {
      debugPrint('Error requesting battery optimization exemption: $e');
      return false;
    }
  }

  Future<void> createTestNotification() async {
    try {
      bool notificationsEnabled = await _checkNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint(
            'Cannot create test notification: notifications are disabled');
        return;
      }

      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: 100,
          channelKey: 'scheduled_channel',
          title: 'Hindu Path',
          body: 'Notifications are working! 🙏',
          notificationLayout: NotificationLayout.Default,
          icon: 'resource://drawable/notification_icon',
        ),
      );
      debugPrint('Test notification created successfully');
    } catch (e) {
      debugPrint('Error creating test notification: $e');
    }
  }

  Future<void> _createDailyNotification({
    required int id,
    required String title,
    required String body,
    required int hour,
    required int minute,
  }) async {
    try {
      bool notificationsEnabled = await _checkNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint(
            '❌ Cannot create daily notification: notifications are disabled');
        return;
      }

      // First cancel any existing notification with this ID to prevent duplicates
      await AwesomeNotifications().cancel(id);
      debugPrint('Canceled existing notification with ID: $id');

      // Calculate the next trigger time for this hour and minute
      DateTime now = DateTime.now();
      DateTime scheduledDate = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
        0,
        0,
      );

      // If the time has already passed today, schedule for tomorrow
      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      debugPrint(
          'Next trigger time for notification $id: ${scheduledDate.toString()}');

      // Create notification with detailed debugging info using Calendar scheduling
      bool success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: 'scheduled_channel',
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          icon: 'resource://drawable/notification_icon',
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: false,
          autoDismissible: true,
          criticalAlert: false,
        ),
        schedule: NotificationCalendar(
          hour: scheduledDate.hour,
          minute: scheduledDate.minute,
          second: 0,
          millisecond: 0,
          repeats: true,
          preciseAlarm: true,
          allowWhileIdle: true,
        ),
      );

      if (success) {
        debugPrint(
            '✅ Daily notification scheduled successfully: ID=$id, Time=$hour:$minute, Next: ${scheduledDate.toString()}');
      } else {
        debugPrint(
            '❌ Failed to schedule daily notification: ID=$id, Time=$hour:$minute');
      }
      // No backup notification needed; only schedule the repeating daily notification.
    } catch (e) {
      debugPrint('❌ Error creating daily notification: $e');
    }
  }

  Future<void> _createWeeklyNotification({
    required int id,
    required String title,
    required String body,
    required int weekDay,
    required int hour,
    required int minute,
  }) async {
    try {
      bool notificationsEnabled = await _checkNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint(
            '❌ Cannot create weekly notification: notifications are disabled');
        return;
      }

      // First cancel any existing notification with this ID to prevent duplicates
      await AwesomeNotifications().cancel(id);
      debugPrint('Canceled existing notification with ID: $id');

      // Get current date/time
      DateTime now = DateTime.now();

      // Calculate next occurrence of the specified weekday
      int currentWeekday = now.weekday;
      int targetWeekday = weekDay == DateTime.sunday
          ? 7
          : weekDay; // Convert Sunday from 1 to 7

      // Calculate days until next occurrence
      int daysUntil = (targetWeekday - currentWeekday + 7) % 7;
      if (daysUntil == 0 &&
          (now.hour > hour || (now.hour == hour && now.minute >= minute))) {
        daysUntil = 7;
      }

      // Calculate next scheduled date
      DateTime scheduledDate = DateTime(
        now.year,
        now.month,
        now.day + daysUntil,
        hour,
        minute,
      );

      debugPrint('Scheduling weekly notification:'
          '\nCurrent weekday: $currentWeekday'
          '\nTarget weekday: $targetWeekday'
          '\nDays until: $daysUntil'
          '\nScheduled for: $scheduledDate');

      debugPrint(
          'Next trigger time for notification $id: ${scheduledDate.toString()}');

      // Create notification with detailed debugging info
      bool success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: 'scheduled_channel',
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          icon: 'resource://drawable/notification_icon',
          category: NotificationCategory.Reminder, // Same category as daily
          wakeUpScreen: true,
          fullScreenIntent: false,
          autoDismissible: true,
          criticalAlert: false,
        ),
        schedule: NotificationCalendar(
          hour: scheduledDate.hour,
          minute: scheduledDate.minute,
          second: 0,
          millisecond: 0,
          weekday: weekDay, // Set weekday to ensure proper weekly repetition
          repeats: true,
          preciseAlarm: true,
          allowWhileIdle: true,
        ),
      );

      if (success) {
        debugPrint(
            '✅ Weekly notification scheduled successfully: ID=$id, Day=$weekDay, Time=$hour:$minute');
      } else {
        debugPrint(
            '❌ Failed to schedule weekly notification: ID=$id, Day=$weekDay, Time=$hour:$minute');
      }
      // No backup notification needed; only schedule the repeating weekly notification.
    } catch (e) {
      debugPrint('❌ Error creating weekly notification: $e');
    }
  }

  Future<void> _createOneTimeNotification({
    required int id,
    required String title,
    required String body,
    required int hour,
    required int minute,
    int? second,
  }) async {
    try {
      bool notificationsEnabled = await _checkNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint(
            'Cannot create one-time notification: notifications are disabled');
        return;
      }

      // First cancel any existing notification with this ID
      await AwesomeNotifications().cancel(id);

      bool success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: 'scheduled_channel',
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          icon: 'resource://drawable/notification_icon',
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: true,
        ),
        schedule: NotificationCalendar(
          hour: hour,
          minute: minute,
          second: second ?? 0,
          millisecond: 0,
          repeats: false, // One-time only
          allowWhileIdle: true,
          preciseAlarm: true,
        ),
      );

      if (success) {
        debugPrint(
            '✅ One-time notification scheduled successfully: ID=$id, Time=$hour:$minute:${second ?? 0}');
      } else {
        debugPrint(
            '❌ Failed to schedule one-time notification: ID=$id, Time=$hour:$minute:${second ?? 0}');
      }
    } catch (e) {
      debugPrint('❌ Error creating one-time notification: $e');
    }
  }

  Future<void> scheduleNotifications() async {
    try {
      bool notificationsEnabled = await _checkNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint(
            '❌ Cannot schedule notifications: notifications are disabled');
        return;
      }

      debugPrint('📅 Beginning to schedule notifications...');

      // Only cancel scheduled notifications, not displayed ones
      await AwesomeNotifications().cancelAllSchedules();
      debugPrint('Canceled all previously scheduled notifications');

      // Remove the immediate test notification
      // await createImmediateNotification(
      //   id: 999,
      //   title: 'Immediate Notification Test',
      //   body: 'This notification appears immediately without scheduling!'
      // );

      // A slight delay to ensure all cancellations are processed
      await Future.delayed(const Duration(milliseconds: 500));

      // Schedule "Listen to Bhajans" - daily at 6 AM
      await _createDailyNotification(
        id: 1,
        title: 'Morning Bhajan Time',
        body: 'Listen to Bhajans',
        hour: 6,
        minute: 0,
      );

      // Remove the 2-minute test notification
      // final now = DateTime.now();
      // final twominuteslater = now.add(const Duration(minutes: 2));
      // debugPrint('Scheduling test notification for ${twominuteslater.hour}:${twominuteslater.minute}');
      //
      // await _createDailyNotification(
      //   id: 2,
      //   title: 'Test Scheduled Notification',
      //   body: 'If you see this, scheduled notifications are working! Time: ${twominuteslater.hour}:${twominuteslater.minute}',
      //   hour: twominuteslater.hour,
      //   minute: twominuteslater.minute,
      // );

      // Remove the interval-based test notification
      // await _createSimpleScheduledNotification(
      //   id: 3,
      //   title: "Test Notification (Interval)",
      //   body: "This notification should appear 2 minutes after app start",
      //   delaySeconds: 120 // 2 minutes
      // );

      // Schedule "Checkout New Deities Wallpapers" - Sunday, Tuesday, Friday at 7 AM
      final wallpaperDays = [
        DateTime.sunday,
        DateTime.tuesday,
        DateTime.friday,
      ];

      for (var day in wallpaperDays) {
        await _createWeeklyNotification(
          id: day + 10, // Unique ID for each day
          title: 'New Wallpapers Available',
          body: 'Checkout New Deities Wallpapers',
          weekDay: day,
          hour: 7,
          minute: 0,
        );
      }

      // Schedule "Checkout New DPs" - Sunday at 8 AM
      await _createWeeklyNotification(
        id: 20,
        title: 'New Display Pictures',
        body: 'Checkout New DPs',
        weekDay: DateTime.sunday,
        hour: 8,
        minute: 0,
      );

      debugPrint('✅ All notifications scheduled successfully');

      // Debug: List all scheduled notifications
      _debugListScheduledNotifications();
    } catch (e) {
      debugPrint('❌ Error scheduling notifications: $e');
    }
  }

  Future<void> _debugListScheduledNotifications() async {
    try {
      List<NotificationModel> pendingNotifications =
          await AwesomeNotifications().listScheduledNotifications();

      debugPrint(
          '--- SCHEDULED NOTIFICATIONS (${pendingNotifications.length}) ---');
      for (var notification in pendingNotifications) {
        // Fix the nextValidDate access
        debugPrint('ID: ${notification.content?.id}, '
            'Title: ${notification.content?.title}, '
            'Schedule Type: ${notification.schedule?.toMap()['type'] ?? "Unknown"}');
      }
      debugPrint('----------------------------------------------');
    } catch (e) {
      debugPrint('Error listing scheduled notifications: $e');
    }
  }

  // New method for immediate notifications with no scheduling
  Future<void> createImmediateNotification(
      {required int id, required String title, required String body}) async {
    try {
      debugPrint('🔔 Attempting to show immediate notification with ID: $id');

      bool allowed = await AwesomeNotifications().isNotificationAllowed();
      debugPrint('Notifications allowed: $allowed');

      if (!allowed) {
        // Try requesting permission again
        debugPrint('Requesting notification permission...');
        await AwesomeNotifications().requestPermissionToSendNotifications();
        allowed = await AwesomeNotifications().isNotificationAllowed();
        debugPrint('Notification permission after request: $allowed');

        if (!allowed) {
          debugPrint('❌ Notification permission denied');
          return;
        }
      }

      // Cancel any existing notification with this ID
      await AwesomeNotifications().cancel(id);

      bool success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: 'scheduled_channel',
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          icon: 'resource://drawable/notification_icon',
          category: NotificationCategory.Message,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
        ),
        // No schedule parameter means it shows immediately
      );

      if (success) {
        debugPrint('✅ Immediate notification created successfully: ID=$id');
      } else {
        debugPrint('❌ Failed to create immediate notification: ID=$id');
      }

      // Also try to create a notification using a different approach
      // as a fallback method
      try {
        debugPrint('Trying alternative notification method...');
        await AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: id + 1000,
            channelKey: 'scheduled_channel',
            title: 'Alternative Notification',
            body: 'This notification uses a different approach',
            notificationLayout: NotificationLayout.Default,
            bigPicture: 'asset://assets/images/hindu_temples.png',
            largeIcon: 'asset://assets/icons/bhajan.png',
          ),
        );
      } catch (e) {
        debugPrint('Alternative notification failed: $e');
      }
    } catch (e) {
      debugPrint('❌ Error creating immediate notification: $e');
    }
  }

  // Add a simple interval-based notification as another fallback method
  Future<void> _createSimpleScheduledNotification({
    required int id,
    required String title,
    required String body,
    required int delaySeconds,
  }) async {
    try {
      bool notificationsEnabled = await _checkNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint(
            'Cannot create scheduled notification: notifications are disabled');
        return;
      }

      await AwesomeNotifications().cancel(id);

      bool success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: 'scheduled_channel',
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          icon: 'resource://drawable/notification_icon',
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: false,
          criticalAlert: false,
        ),
        schedule: NotificationInterval(
          interval: delaySeconds,
          preciseAlarm: true,
          allowWhileIdle: true,
          repeats: false,
        ),
      );

      if (success) {
        debugPrint(
            '✅ Simple scheduled notification created: ID=$id, Delay=${delaySeconds}s');
      } else {
        debugPrint('❌ Failed to create simple scheduled notification: ID=$id');
      }
    } catch (e) {
      debugPrint('❌ Error creating simple scheduled notification: $e');
    }
  }
}
