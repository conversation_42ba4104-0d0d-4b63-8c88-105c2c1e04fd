import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:async';
import 'dart:io';

// Enum to track which type of audio is currently active
enum AudioType {
  none,
  bhajan,
  ringtone,
}

// Singleton class to manage audio across the app
class AudioManager {
  // Singleton instance
  static final AudioManager _instance = AudioManager._internal();
  factory AudioManager() => _instance;
  AudioManager._internal() {
    _initCacheDir();
  }

  // Cache directory
  Directory? _cacheDir;
  
  Future<void> _initCacheDir() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDir = Directory('${appDir.path}/audio_cache');
      if (!_cacheDir!.existsSync()) {
        _cacheDir!.createSync();
      }
      debugPrint('AudioManager: Cache directory initialized at ${_cacheDir!.path}');
    } catch (e) {
      debugPrint('AudioManager: Error initializing cache directory: $e');
    }
  }

  // The single AudioPlayer instance shared across the app
  AudioPlayer? _player;

  // Track which type of audio is currently active
  AudioType _activeAudioType = AudioType.none;

  // Controllers for notifications
  final _audioTypeController = StreamController<AudioType>.broadcast();

  // Getters
  AudioPlayer? get player => _player;
  AudioType get activeAudioType => _activeAudioType;
  Stream<AudioType> get audioTypeStream => _audioTypeController.stream;

  // Initialize the manager
  Future<void> initialize() async {
    debugPrint('AudioManager: Initializing');
    if (_player != null) {
      await _player!.dispose();
      _player = null;
    }

    _activeAudioType = AudioType.none;
    _audioTypeController.add(_activeAudioType);
    debugPrint('AudioManager: Initialized with no active audio');
  }

  // Request audio player for a specific type
  Future<AudioPlayer?> requestPlayer(AudioType requestedType) async {
    debugPrint('AudioManager: Requested player for $requestedType');

    // If we already have this type active, return the existing player
    if (_activeAudioType == requestedType && _player != null) {
      debugPrint('AudioManager: Returning existing player for $requestedType');
      return _player;
    }

    // Clean up existing player if any
    if (_player != null) {
      debugPrint(
          'AudioManager: Disposing existing player for $_activeAudioType');
      await _player!.stop();
      await _player!.dispose();
      _player = null;
    }

    // Create a new player
    try {
      _player = AudioPlayer();
      _activeAudioType = requestedType;
      _audioTypeController.add(_activeAudioType);

      debugPrint('AudioManager: Created new player for $requestedType');
      return _player;
    } catch (e) {
      debugPrint('AudioManager: Error creating player: $e');
      _activeAudioType = AudioType.none;
      _audioTypeController.add(_activeAudioType);
      return null;
    }
  }

  // Release the player for a specific type
  Future<void> releasePlayer(AudioType type) async {
    debugPrint('AudioManager: Releasing player for $type');

    // Only release if this type is currently active
    if (_activeAudioType == type && _player != null) {
      await _player!.stop();
      await _player!.dispose();
      _player = null;
      _activeAudioType = AudioType.none;
      _audioTypeController.add(_activeAudioType);
      debugPrint('AudioManager: Player released');
    }
  }

  // Create an AudioSource with MediaItem for a URL
  Future<AudioSource> createAudioSource({
    required String url,
    required String id,
    required String title,
    String? artist,
    Uri? artUri,
    Map<String, dynamic>? extras,
    Map<String, String>? headers,
  }) async {
    if (_cacheDir == null) await _initCacheDir();

    // Generate cache file path using ID to ensure uniqueness
    final cacheFile = File('${_cacheDir!.path}/$id.mp3');
    
    // Check if file is already cached
    if (await cacheFile.exists()) {
      debugPrint('AudioManager: Using cached file for $title');
      return AudioSource.uri(
        Uri.file(cacheFile.path),
        tag: MediaItem(
          id: id,
          title: title,
          artist: artist ?? 'Hindu Path',
          artUri: artUri,
          extras: extras,
        ),
      );
    }

    // If not cached, use LockCachingAudioSource for automatic caching
    return LockCachingAudioSource(
      Uri.parse(url),
      tag: MediaItem(
        id: id,
        title: title,
        artist: artist ?? 'Hindu Path',
        artUri: artUri,
        extras: extras,
      ),
      headers: headers,
      cacheFile: cacheFile,
    );
  }

  // Check if an audio type is active
  bool isAudioTypeActive(AudioType type) {
    return _activeAudioType == type && _player != null && _player!.playing;
  }

  // Force stop and release the player for a specific type
  Future<void> forceStopAndReleasePlayer(AudioType type) async {
    debugPrint('AudioManager: Force stopping and releasing player for $type');

    // Only release if this type is currently active
    if (_activeAudioType == type && _player != null) {
      try {
        // First stop playback
        await _player!.stop();

        // Then dispose the player
        await _player!.dispose();

        // Set player to null and reset active type
        _player = null;
        _activeAudioType = AudioType.none;
        _audioTypeController.add(_activeAudioType);

        debugPrint('AudioManager: Player forcefully stopped and released');
      } catch (e) {
        debugPrint('AudioManager: Error force stopping player: $e');
        // Still reset the state even if there was an error
        _player = null;
        _activeAudioType = AudioType.none;
        _audioTypeController.add(_activeAudioType);
      }
    }
  }

  // Dispose the manager
  Future<void> dispose() async {
    if (_player != null) {
      await _player!.dispose();
      _player = null;
    }
    await _audioTypeController.close();
  }
}
