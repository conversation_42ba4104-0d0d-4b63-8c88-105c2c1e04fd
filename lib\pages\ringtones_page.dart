import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/ringtone_provider.dart';
import '../providers/audio_provider.dart';
import '../models/ringtone.dart';
import '../utils/font_loader.dart';
import '../screens/ringtone_player_screen.dart';
import '../utils/color_utils.dart';
import '../utils/theme_widgets.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../services/auth_service.dart';

class RingtonesPage extends StatefulWidget {
  const RingtonesPage({super.key});

  @override
  State<RingtonesPage> createState() => _RingtonesPageState();
}

class _RingtonesPageState extends State<RingtonesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Set up the AudioProvider reference in the RingtoneProvider
    // and load favorites
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      final ringtoneProvider =
          Provider.of<RingtoneProvider>(context, listen: false);
      ringtoneProvider.setAudioProvider(audioProvider);

      // Load favorites
      _loadFavorites();
    });
  }

  // Load ringtone favorites
  Future<void> _loadFavorites() async {
    try {
      final ringtoneProvider =
          Provider.of<RingtoneProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Only load favorites if user is authenticated and not in guest mode
      if (authProvider.isSignedIn && !authProvider.isGuest) {
        debugPrint('Loading ringtone favorites for authenticated user');
        await ringtoneProvider.loadFavorites();
      } else {
        debugPrint(
            'User not authenticated or in guest mode - clearing ringtone favorites');
        ringtoneProvider.clearFavorites();
      }
    } catch (e) {
      debugPrint('Error loading ringtone favorites: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _openRingtonePlayer(List<Ringtone> ringtones, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RingtonePlayerScreen(
          ringtones: ringtones,
          initialIndex: index,
        ),
      ),
    );
  }

  // Method to handle favorite toggle action
  void _handleFavoriteToggle(String ringtoneId) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final ringtoneProvider =
        Provider.of<RingtoneProvider>(context, listen: false);

    // Check if user is signed in as guest
    if (authProvider.isGuest || !authProvider.isSignedIn) {
      // Show message for guest users
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please Log In to add Favorite',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.black,
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      // Toggle favorite for authenticated users
      ringtoneProvider.toggleFavorite(ringtoneId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      appBar: AppBar(
        title: Text(
          'Ringtones',
          style: AppFontLoader.getPrakrtaStyle(
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: const Color(0xFF0D0D0D),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 14,
          ),
          indicator: ThemedTabIndicator(
            context: context,
            radius: 4,
            indicatorHeight: 3,
            insets: const EdgeInsets.symmetric(horizontal: 20),
          ),
          tabs: const [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('All'),
                  SizedBox(width: 8),
                  Icon(Icons.music_note, size: 20),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Favorites'),
                  SizedBox(width: 8),
                  Icon(Icons.favorite, size: 20),
                ],
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // All ringtones tab
          _buildRingtoneList(context, false),
          // Favorites tab
          _buildRingtoneList(context, true),
        ],
      ),
    );
  }

  Widget _buildRingtoneList(BuildContext context, bool favoritesOnly) {
    return Consumer<RingtoneProvider>(
      builder: (context, ringtoneProvider, child) {
        final authProvider = Provider.of<AuthProvider>(context);

        // Show sign in message for guest users in the Favorites tab
        if (favoritesOnly &&
            (authProvider.isGuest || _authService.currentUser == null)) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.music_note,
                  size: 64,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Please Sign In to view your Ringtones',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                    fontFamily: 'JosefinSans',
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.getPrimaryColor(context),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text(
                    'Sign In',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        if (ringtoneProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(
              color: Color(0xFF1b4332),
            ),
          );
        }

        if (ringtoneProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  ringtoneProvider.error!,
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: ringtoneProvider.retry,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final List<Ringtone> ringtones = favoritesOnly
            ? ringtoneProvider.getFavoriteRingtones()
            : ringtoneProvider.ringtones;

        if (ringtones.isEmpty) {
          return Center(
            child: Text(
              favoritesOnly
                  ? 'No favorite ringtones yet'
                  : 'No ringtones available',
              style: const TextStyle(color: Colors.white),
            ),
          );
        }

        return ListView.builder(
          itemCount: ringtones.length,
          itemBuilder: (context, index) {
            final ringtone = ringtones[index];
            final isPlaying = ringtoneProvider.isInitialized &&
                ringtoneProvider.ringtoneService.isPlaying &&
                ringtoneProvider.ringtoneService.currentRingtone?.id ==
                    ringtone.id;

            return ListTile(
              title: Text(
                ringtone.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
              subtitle: Container(),
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Provider.of<ThemeProvider>(context).primaryColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: isPlaying
                      ? const Icon(
                          Icons.pause,
                          color: Colors.white,
                          size: 20,
                        )
                      : Text(
                          'HP',
                          style: AppFontLoader.getPrakrtaStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                ),
              ),
              trailing: _authService.currentUser != null
                  ? IconButton(
                      icon: Icon(
                        ringtoneProvider.isFavorite(ringtone.id)
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: ringtoneProvider.isFavorite(ringtone.id)
                            ? Colors.red
                            : Colors.white,
                      ),
                      onPressed: () => _handleFavoriteToggle(ringtone.id),
                    )
                  : null,
              onTap: () {
                // Find the actual index in the main ringtones list
                final mainIndex = ringtoneProvider.ringtones
                    .indexWhere((r) => r.id == ringtone.id);

                if (mainIndex != -1) {
                  _openRingtonePlayer(ringtoneProvider.ringtones, mainIndex);
                }
              },
            );
          },
        );
      },
    );
  }
}
