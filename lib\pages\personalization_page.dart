import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/font_loader.dart';
import '../providers/theme_provider.dart';
import '../utils/ui_helpers.dart';
import '../services/permission_service.dart';
import '../utils/color_utils.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import '../providers/auth_provider.dart' as app_provider;
import '../screens/sign_in_screen.dart';

class PersonalizationPage extends StatefulWidget {
  const PersonalizationPage({super.key});

  @override
  State<PersonalizationPage> createState() => _PersonalizationPageState();
}

class _PersonalizationPageState extends State<PersonalizationPage> {
  // Predefined theme options with names
  final List<ThemeOption> themeOptions = [
    ThemeOption(
        color: const Color(0xFF800000),
        name: '<PERSON><PERSON>',
        description: 'Rich and traditional deep red shade'),
    ThemeOption(
        color: const Color(0xFF10B981),
        name: 'Emerald',
        description: 'Vibrant and refreshing green'),
    ThemeOption(
        color: const Color(0xFF6D28D9),
        name: 'Indigo',
        description: 'Spiritual and introspective purple-blue'),
    ThemeOption(
        color: const Color(0xFF0047AB),
        name: 'Cobalt Blue',
        description: 'Deep and captivating medium blue'),
    ThemeOption(
        color: const Color(0xFFEF4444),
        name: 'Ruby',
        description: 'Passionate and energetic red'),
    ThemeOption(
        color: const Color(0xFFF59E0B),
        name: 'Amber',
        description: 'Warm and inviting golden yellow'),
    ThemeOption(
        color: const Color(0xFF0EA5E9),
        name: 'Ocean',
        description: 'Clear and refreshing blue'),
    ThemeOption(
        color: const Color(0xFF475569),
        name: 'Slate',
        description: 'Sophisticated and neutral gray'),
  ];
  // Currently selected color
  late Color _selectedColor;
  late bool _notificationsEnabled = true;
  Timer? _notificationCheckTimer;

  @override
  void initState() {
    super.initState();
    // Initialize with current theme color
    _selectedColor =
        Provider.of<ThemeProvider>(context, listen: false).primaryColor;

    // Load notification status and initialize system check
    _loadNotificationStatus();
    _initializeNotificationListener();
  }

  Future<void> _loadNotificationStatus() async {
    final permissionService = PermissionService();
    await permissionService.init();
    final userDenied = permissionService.userHasDeniedNotifications;

    if (mounted) {
      setState(() {
        _notificationsEnabled = !userDenied;
      });
    }
  }

  Future<void> _toggleNotifications(bool enable) async {
    if (enable) {
      // Request permission through app
      final result =
          await AwesomeNotifications().requestPermissionToSendNotifications();
      if (!result && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Permission denied. Please enable notifications in system settings.'),
            duration: Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } else {
      // Open system notification settings
      await openAppSettings();
    }

    // Update state based on actual system permission
    if (mounted) {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      setState(() {
        _notificationsEnabled = isAllowed;
      });
    }
  }

  void _selectTheme(Color color) {
    setState(() {
      _selectedColor = color;
    });
  }

  Future<void> _applyTheme() async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    // Apply theme changes
    await themeProvider.updateTheme(primaryColor: _selectedColor);

    if (mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
          UIHelpers.getSuccessSnackBar(message: 'Personalization applied'));

      // Navigate back
      Navigator.pop(context);
    }
  }

  @override
  void dispose() {
    _notificationCheckTimer?.cancel();
    super.dispose();
  }

  void _initializeNotificationListener() {
    // Initial check
    _checkSystemNotificationStatus();

    // Set up periodic check
    _notificationCheckTimer =
        Timer.periodic(const Duration(seconds: 2), (timer) {
      if (mounted) {
        _checkSystemNotificationStatus();
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _checkSystemNotificationStatus() async {
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (mounted && isAllowed != _notificationsEnabled) {
        setState(() {
          _notificationsEnabled = isAllowed;
        });
      }
    } catch (e) {
      debugPrint('Error checking system notification status: $e');
    }
  }
  
  // Responsive theme preview widget
  Widget _buildThemePreview() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _selectedColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _selectedColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preview',
            style: TextStyle(
              color: _selectedColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // App icon preview
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _selectedColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              // Text preview
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hindu Path',
                      style: AppFontLoader.getPrakrtaStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Your spiritual journey companion',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Button preview
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('PRIMARY'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {},
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _selectedColor,
                    side: BorderSide(color: _selectedColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('SECONDARY'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Section header with icon and description
  Widget _buildSectionHeader({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: _selectedColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: _selectedColor, size: 22),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Responsive color grid for different screen sizes
  Widget _buildColorGrid() {
    // Get screen width to make grid responsive
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Determine if we're on a small screen device
    final bool isSmallScreen = screenWidth < 360; // Adjust this threshold as needed
    
    // Calculate appropriate spacing and sizing for different screen sizes
    final double spacing = isSmallScreen ? 8.0 : 12.0;
    final double aspectRatio = isSmallScreen ? 1.0 : 0.9; // Increase aspect ratio on small screens
    final double circleSize = isSmallScreen ? 40.0 : 50.0;
    final double fontSize = isSmallScreen ? 10.0 : 12.0;
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4, // Keep 4 columns but with adjusted spacing
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: aspectRatio, 
      ),
      itemCount: themeOptions.length,
      itemBuilder: (context, index) {
        final option = themeOptions[index];
        final isSelected = option.color.value == _selectedColor.value;

        return GestureDetector(
          onTap: () => _selectTheme(option.color),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              border: Border.all(
                color: isSelected ? option.color : Colors.transparent,
                width: 2,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Color circle
                Container(
                  width: circleSize,
                  height: circleSize,
                  decoration: BoxDecoration(
                    color: option.color,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: option.color.withOpacity(0.5),
                        blurRadius: 8,
                        spreadRadius: 0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: isSelected
                      ? Icon(Icons.check, color: Colors.white, size: isSmallScreen ? 20 : 24)
                      : null,
                ),
                SizedBox(height: isSmallScreen ? 4 : 8),
                // Color name
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 2 : 4),
                  child: Text(
                    option.name,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: isSelected
                          ? option.color
                          : Colors.white.withOpacity(0.9),
                      fontSize: fontSize,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotificationToggle() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Enable Notifications',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _notificationsEnabled
                      ? 'You will receive updates and reminders'
                      : 'You will not receive any notifications',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _notificationsEnabled,
            onChanged: _toggleNotifications,
            activeColor: _selectedColor,
            activeTrackColor: _selectedColor.withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildResetButton() {
    return Center(
      child: TextButton.icon(
        onPressed: () {
          // Reset to default theme
          _selectTheme(ThemeProvider.defaultPrimaryColor);
        },
        icon: const Icon(Icons.refresh_outlined, size: 18),
        label: const Text(
          'Reset to Default Theme',
          style: TextStyle(fontSize: 15),
        ),
        style: TextButton.styleFrom(
          foregroundColor: Colors.white.withOpacity(0.8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: Colors.white.withOpacity(0.3)),
          ),
        ),
      ),
    );
  }

  // Delete account button - only shown for logged in users
  Widget _buildDeleteAccountButton() {
    final currentUser = FirebaseAuth.instance.currentUser;

    // Only show for signed-in users
    if (currentUser == null ||
        !currentUser.providerData.any((p) => p.providerId == 'google.com')) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        const SizedBox(height: 32),
        _buildSectionHeader(
            icon: Icons.delete_forever,
            title: 'Account Management',
            description: 'Manage your account data and settings'),
        const SizedBox(height: 16),
        Center(
          child: TextButton.icon(
            onPressed: () => _showDeleteAccountDialog(context),
            icon: const Icon(Icons.delete_forever, size: 18, color: Colors.red),
            label: const Text(
              'Delete Account',
              style: TextStyle(fontSize: 15, color: Colors.red),
            ),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Show delete account confirmation dialog
  Future<void> _showDeleteAccountDialog(BuildContext context) async {
    final bool confirmDelete = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1A1A1A),
            title: const Text(
              'Delete Account',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'This will permanently delete your account and all associated data. This action cannot be undone.\n\nDo you want to proceed?',
              style: TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.white70),
                ),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                onPressed: () => Navigator.pop(context, true),
                child: const Text(
                  'Delete Account',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirmDelete) return;

    // If confirmed, proceed with account deletion
    if (context.mounted) {
      await _performAccountDeletion(context);
    }
  }

  // Handle actual account deletion
  Future<void> _performAccountDeletion(BuildContext context) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
          ),
        ),
      ),
    );

    try {
      final User? currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // Delete user data from Firestore first
        final authProvider =
            Provider.of<app_provider.AuthProvider>(context, listen: false);

        // Delete user account data
        await authProvider.deleteUserData(currentUser.uid);

        // Delete Firebase Auth account
        await currentUser.delete();

        // Dismiss loading dialog
        Navigator.pop(context);

        // Navigate to sign in screen
        Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const SignInScreen()),
          (route) => false,
        );

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            UIHelpers.getSuccessSnackBar(
              message: 'Your account has been successfully deleted',
            ),
          );
        }
      }
    } catch (error) {
      // Dismiss loading dialog
      Navigator.pop(context);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          UIHelpers.getErrorSnackBar(
            message: 'Failed to delete account: ${error.toString()}',
          ),
        );
      }
    }
  }

  Widget _buildApplyButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _applyTheme,
        style: ElevatedButton.styleFrom(
          backgroundColor: _selectedColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: _selectedColor.withOpacity(0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          'Apply Changes',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: themeProvider.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Personalization',
          style: AppFontLoader.getPrakrtaStyle(
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: themeProvider.backgroundColor,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFFE5E7EB)),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        scrolledUnderElevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Theme preview
                _buildThemePreview(),

                const SizedBox(height: 32),

                // Color selection section
                _buildSectionHeader(
                    icon: Icons.palette_outlined,
                    title: 'Theme Color',
                    description: 'Choose a color theme for the app'),

                const SizedBox(height: 16),

                // Color grid
                _buildColorGrid(),

                const SizedBox(height: 32),

                // Notification Settings
                _buildSectionHeader(
                    icon: Icons.notifications_outlined,
                    title: 'Notifications',
                    description: 'Configure notification preferences'),

                const SizedBox(height: 12),

                // Notification toggle
                _buildNotificationToggle(),

                const SizedBox(height: 32),

                // Reset button
                _buildResetButton(),

                // Delete account button - only shown for logged in users
                _buildDeleteAccountButton(),

                const SizedBox(height: 32),

                // Apply button
                _buildApplyButton(),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Class to store theme options
class ThemeOption {
  final Color color;
  final String name;
  final String description;

  ThemeOption({
    required this.color,
    required this.name,
    required this.description,
  });
}
