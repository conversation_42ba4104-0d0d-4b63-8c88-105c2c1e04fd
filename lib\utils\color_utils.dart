import 'package:flutter/material.dart';
import '../providers/theme_provider.dart';

/// A utility class for handling app colors
class AppColors {
  /// The default color used throughout the app
  static const Color defaultGreen = Color(0xFFEF4444);

  /// The alternate green color used in some UI elements
  static const Color alternateGreen = Color(0xFF10B981);

  /// The default dark background color
  static const Color defaultBackground = Color(0xFF0D0D0D);

  /// Get the current theme primary color (or default if not available)
  static Color getPrimaryColor(BuildContext context) {
    return ThemeProvider.getGreenColor(context);
  }

  /// Get a theme-aware color based on the app state
  ///
  /// If [useThemeColor] is true, will use the current theme's primary color
  /// Otherwise, will use the provided [defaultColor]
  static Color getThemeAwareColor(
    BuildContext context, {
    required Color defaultColor,
    bool useThemeColor = true,
  }) {
    if (!useThemeColor) return defaultColor;

    if (defaultColor.value == defaultGreen.value ||
        defaultColor.value == alternateGreen.value) {
      return ThemeProvider.getGreenColor(context);
    }

    return defaultColor;
  }

  /// Get the primary color with opacity
  static Color getPrimaryColorWithOpacity(
      BuildContext context, double opacity) {
    return ThemeProvider.getGreenColor(context).withOpacity(opacity);
  }

  /// Get a BoxDecoration with primary color border
  static BoxDecoration getPrimaryBorderDecoration(
    BuildContext context, {
    double width = 1.0,
    double borderRadius = 0.0,
  }) {
    return BoxDecoration(
      border: Border.all(
        color: ThemeProvider.getGreenColor(context),
        width: width,
      ),
      borderRadius: BorderRadius.circular(borderRadius),
    );
  }

  /// Get a ButtonStyle with primary color background
  static ButtonStyle getPrimaryButtonStyle(
    BuildContext context, {
    double elevation = 0.0,
    Size? minimumSize,
    OutlinedBorder? shape,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: ThemeProvider.getGreenColor(context),
      foregroundColor: Colors.white,
      elevation: elevation,
      minimumSize: minimumSize,
      shape: shape,
    );
  }
}
