import 'package:flutter/material.dart';
import '../models/bhajan.dart';
import '../providers/audio_provider.dart';

/// A service class that centralizes bhajan playback operations.
/// This eliminates code duplication across multiple UI components.
class BhajanPlayerService {
  /// Plays a single bhajan from a bhajan object.
  /// 
  /// Parameters:
  /// - audioProvider: The app's AudioProvider instance
  /// - bhajan: The Bhajan to play
  /// - updateState: Callback to update UI state during the operation
  /// - sourceList: Optional list of bhajans to use as the source playlist (e.g., favorites only)
  ///   If not provided, uses the full list of bhajans from the provider
  static Future<void> playBhajan({
    required AudioProvider audioProvider,
    required Bhajan bhajan,
    required Function(bool isPlaying) updateState,
    List<Bhajan>? sourceList,
  }) async {
    try {
      updateState(true);
      
      // Determine which list to use for playback
      final playbackSource = sourceList ?? audioProvider.bhajans;
      
      // Find the bhajan index in the source list
      final index = playbackSource.indexOf(bhajan);
      if (index == -1) {
        debugPrint('Error: Bhajan not found in the list: ${bhajan.title}');
        updateState(false);
        return;
      }

      // Show mini player immediately, so it's visible when playback starts
      audioProvider.forceShowMiniPlayer();
      
      // Pause any current playback
      await audioProvider.audioService.pause();
      
      // Small delay to ensure player state is reset
      await Future.delayed(const Duration(milliseconds: 50));
      
      // Create a sorted list of bhajans in alphabetical order for consistent behavior across tabs
      // Only using bhajans from the provided source list (e.g., favorites only)
      final sortedBhajans = List<Bhajan>.from(playbackSource);
      sortedBhajans.sort((a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));
      
      // Find the index of the selected bhajan in the sorted list
      final sortedIndex = sortedBhajans.indexWhere((item) => item.id == bhajan.id);
      if (sortedIndex == -1) {
        debugPrint('Error: Bhajan not found in sorted list: ${bhajan.title}');
        // Fallback to direct playing from source list if there's an issue
        if (sourceList == null) {
          // Only for the main list
          audioProvider.playBhajan(index);
        } else {
          // For favorites or other filtered lists, create a temporary playlist
          await audioProvider.audioService.createTemporaryPlaylist([bhajan], 0);
        }
      } else {
        final contextName = sourceList == null ? "all bhajans" : 
                          (sourceList.length == audioProvider.bhajans.length ? "all bhajans" : "filtered list");
        debugPrint('BhajanPlayerService: Playing bhajan from alphabetically sorted $contextName at index $sortedIndex: ${bhajan.title}');
        
        // Use createTemporaryPlaylist to play in alphabetical order, starting from the selected bhajan
        // This will only include bhajans from the source list (e.g., only favorites)
        await audioProvider.audioService.createTemporaryPlaylist(sortedBhajans, sortedIndex);
      }
      
      // Force mini player visibility
      audioProvider.forceShowMiniPlayer();
      
      // Small delay to ensure UI updates properly
      await Future.delayed(const Duration(milliseconds: 100));
      
      updateState(false);
    } catch (e) {
      debugPrint('Error playing bhajan: $e');
      updateState(false);
    }
  }

  /// Plays multiple bhajans, starting from the first item in the list.
  /// Optionally shuffles the list before playing.
  /// 
  /// Parameters:
  /// - audioProvider: The app's AudioProvider instance
  /// - bhajans: The list of Bhajans to play
  /// - shuffle: Whether to shuffle the playlist before playing
  /// - updateState: Callback to update UI state during the operation
  static Future<void> playMultipleBhajans({
    required AudioProvider audioProvider,
    required List<Bhajan> bhajans,
    required Function(bool isPlaying) updateState,
    bool shuffle = false,
  }) async {
    try {
      updateState(true);

      if (bhajans.isEmpty) {
        updateState(false);
        return;
      }
      
      // First show mini player to reset dismiss state
      audioProvider.forceShowMiniPlayer();

      // First ensure any current playback is stopped
      await audioProvider.audioService.pause();

      // Small delay to ensure player state is reset
      await Future.delayed(const Duration(milliseconds: 50));

      // Create a copy of the list to avoid modifying the original
      final bhajanList = List<Bhajan>.from(bhajans);

      if (shuffle) {
        // For Shuffle: Properly randomize the list for better user experience
        bhajanList.shuffle();
      } else {
        // For Play All: Ensure alphabetical order by title
        bhajanList.sort((a, b) => 
          a.title.toLowerCase().compareTo(b.title.toLowerCase())
        );
      }

      // Use the new createTemporaryPlaylist method to play the list
      await audioProvider.audioService.createTemporaryPlaylist(bhajanList, 0);
      
      // Force mini player visibility
      audioProvider.forceShowMiniPlayer();
      
      updateState(false);
    } catch (e) {
      debugPrint('Error playing multiple bhajans: $e');
      updateState(false);
    }
  }
} 