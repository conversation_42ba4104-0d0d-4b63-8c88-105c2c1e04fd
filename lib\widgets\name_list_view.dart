import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:provider/provider.dart';
import '../models/name_data.dart';
import '../providers/name_provider.dart';
import '../services/auth_service.dart';
import '../utils/ui_helpers.dart';

class NameListView extends StatefulWidget {
  final List<NameData> names;
  final String searchQuery;

  const NameListView({
    super.key,
    required this.names,
    this.searchQuery = '',
  });

  @override
  State<NameListView> createState() => _NameListViewState();
}

class _NameListViewState extends State<NameListView> {
  final ItemScrollController _scrollController = ItemScrollController();
  final ItemPositionsListener _positionsListener =
      ItemPositionsListener.create();
  late Map<String, int> _alphabetPositions;
  late List<String> _uniqueAlphabets;
  late List<NameData> _filteredNames;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _filterNames();
    _updateAlphabetPositions();
  }

  @override
  void didUpdateWidget(NameListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.names != widget.names ||
        oldWidget.searchQuery != widget.searchQuery) {
      _filterNames();
      _updateAlphabetPositions();
    }
  }

  void _filterNames() {
    if (widget.searchQuery.isEmpty) {
      _filteredNames = List.from(widget.names);
    } else {
      final query = widget.searchQuery.toLowerCase();
      _filteredNames = widget.names.where((name) {
        return name.name.toLowerCase().contains(query) ||
            (name.meaning != 'nan' &&
                name.meaning.toLowerCase().contains(query));
      }).toList();
    }
  }

  void _updateAlphabetPositions() {
    _alphabetPositions = {};
    final Set<String> alphabets = {};

    for (int i = 0; i < _filteredNames.length; i++) {
      final firstLetter = _filteredNames[i].name[0].toUpperCase();
      if (!_alphabetPositions.containsKey(firstLetter)) {
        _alphabetPositions[firstLetter] = i;
        alphabets.add(firstLetter);
      }
    }

    _uniqueAlphabets = alphabets.toList()..sort();
  }

  void _scrollToLetter(String letter) {
    final position = _alphabetPositions[letter];
    if (position != null) {
      _scrollController.scrollTo(
        index: position,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_filteredNames.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 48,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              'No names matching "${widget.searchQuery}"',
              style: const TextStyle(fontSize: 16, color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        ScrollablePositionedList.builder(
          itemCount: _filteredNames.length,
          itemScrollController: _scrollController,
          itemPositionsListener: _positionsListener,
          itemBuilder: (context, index) {
            final name = _filteredNames[index];
            final firstLetter = name.name[0].toUpperCase();

            // Show letter header if this is the first name starting with this letter
            final bool showHeader = index == _alphabetPositions[firstLetter];

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (showHeader)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 4),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        firstLetter,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ListTile(
                  title: Text(
                    name.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    name.meaning != 'nan' ? name.meaning : '',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  onTap: () {
                    _showNameDetailDialog(context, name);
                  },
                ),
                // Add divider after each name except the last one
                if (index < _filteredNames.length - 1)
                  Divider(
                    color: Theme.of(context).dividerColor,
                    height: 1,
                    thickness: 0.5,
                    indent: 16,
                    endIndent: 16,
                  ),
              ],
            );
          },
        ),
        Positioned(
          right: 0,
          top: 0,
          bottom: 0,
          child: Container(
            width: 40,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                bottomLeft: Radius.circular(8),
              ),
            ),
            child: _uniqueAlphabets.isEmpty
                ? const SizedBox.shrink()
                : ListView.builder(
                    itemCount: _uniqueAlphabets.length,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        onTap: () => _scrollToLetter(_uniqueAlphabets[index]),
                        child: Container(
                          height: 28,
                          alignment: Alignment.center,
                          child: Text(
                            _uniqueAlphabets[index],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ),
      ],
    );
  }

  void _showNameDetailDialog(BuildContext context, NameData name) {
    final nameProvider = Provider.of<NameProvider>(context, listen: false);
    final isLoggedIn = _authService.currentUser != null;
    final isFavorite = nameProvider.isNameFavorite(name.name);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            backgroundColor: const Color(0xFF0D0D0D),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    name.name,
                    style: const TextStyle(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: Icon(
                    nameProvider.isNameFavorite(name.name)
                        ? Icons.favorite
                        : Icons.favorite_border,
                    color: nameProvider.isNameFavorite(name.name)
                        ? Colors.red
                        : Colors.white,
                  ),
                  onPressed: isLoggedIn
                      ? () async {
                          await nameProvider.toggleFavorite(name.name);
                          setState(() {}); // Update the dialog UI

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              UIHelpers.getSuccessSnackBar(
                                message: nameProvider.isNameFavorite(name.name)
                                    ? 'Added to favorites'
                                    : 'Removed from favorites',
                              ),
                            );
                          }
                        }
                      : () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            UIHelpers.getInfoSnackBar(
                                message: 'Please sign in to add favorites'),
                          );
                        },
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (name.nameInDevanagari.isNotEmpty) ...[
                  Text(
                    'देवनागरी: ${name.nameInDevanagari}',
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                ],
                if (name.meaning != 'nan') ...[
                  Text(
                    'Meaning: ${name.meaning}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          );
        },
      ),
    );
  }
}
