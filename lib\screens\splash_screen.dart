import 'package:flutter/material.dart';
import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import '../providers/auth_provider.dart' as app_auth;
import 'package:provider/provider.dart';
import 'sign_in_screen.dart';
import 'main_screen.dart';
import '../services/permission_service.dart';
import 'permission_onboarding_screen.dart';
import '../services/audio_manager.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Permission service
  final PermissionService _permissionService = PermissionService();

  @override
  void initState() {
    super.initState();

    // Animation setup
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    // Start animation
    _animationController.forward();

    // Initialize permission service
    _permissionService.init().then((_) {
      // Navigate to the appropriate screen after a shorter delay
      Timer(const Duration(milliseconds: 1000), () {
        navigateToNextScreen();
      });
    });
  }

  void navigateToNextScreen() async {
    // Check if user is already logged in
    final user = FirebaseAuth.instance.currentUser;
    final authProvider =
        Provider.of<app_auth.AuthProvider>(context, listen: false);
    
    // Check if permission onboarding is completed
    final bool hasCompletedOnboarding = _permissionService.hasCompletedOnboarding;
    
    if (hasCompletedOnboarding) {
      // If permission onboarding is completed, proceed to normal flow
      if (user != null || authProvider.isGuest) {
        Navigator.pushReplacement(
            context, MaterialPageRoute(builder: (context) => const MainScreen()));
      } else {
        // Stop any playing bhajan before navigating to sign-in
        await AudioManager().forceStopAndReleasePlayer(AudioType.bhajan);
        
        Navigator.pushReplacement(context,
            MaterialPageRoute(builder: (context) => const SignInScreen()));
      }
    } else {
      // If permission onboarding is not completed, navigate to permission onboarding
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: (context) => const PermissionOnboardingScreen()));
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFF0D0D0D),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Using a simple image file that should work reliably
              Image.asset(
                'assets/images/splash_image.png',
                width: size.width * 0.6875,
                fit: BoxFit.fitWidth,
              ),

              const SizedBox(height: 16),

              // App name with shimmer effect
              ShaderMask(
                shaderCallback: (bounds) {
                  return const LinearGradient(
                    colors: [
                      Colors.white,
                      Color(0xFFFFC107),
                      Colors.white,
                    ],
                    stops: [0.0, 0.5, 1.0],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds);
                },
                child: const Text(
                  'Hindu Path',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Prakrta',
                    letterSpacing: 1.2,
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Tagline
              Text(
                'Your spiritual journey begins here',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                  fontFamily: 'JosefinSans',
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
