import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/name_provider.dart';
import '../widgets/name_list_view.dart';
import '../utils/color_utils.dart';
import '../utils/theme_widgets.dart';
import '../widgets/loading_indicator.dart';

class NaamkaranPage extends StatefulWidget {
  const NaamkaranPage({super.key});

  @override
  State<NaamkaranPage> createState() => _NaamkaranPageState();
}

class _NaamkaranPageState extends State<NaamkaranPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeNames();
  }

  Future<void> _initializeNames() async {
    try {
      await context.read<NameProvider>().loadNames();
    } catch (e) {
      debugPrint('Error loading names: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0D0D),
      body: Column(
        children: [
          Builder(
            builder: (BuildContext context) {
              return TabBar(
                controller: _tabController,
                labelStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontSize: 14,
                ),
                indicator: ThemedTabIndicator(
                  context: context,
                  radius: 4,
                  indicatorHeight: 3,
                  insets: const EdgeInsets.symmetric(horizontal: 20),
                ),
                tabs: const [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('Boy Names'),
                        SizedBox(width: 8),
                        Icon(Icons.male, size: 20),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('Girl Names'),
                        SizedBox(width: 8),
                        Icon(Icons.female, size: 20),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
          // Search widget
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search names...',
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
                prefixIcon: const Icon(Icons.search, color: Colors.white),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Colors.white),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          // Tab content
          Expanded(
            child: Consumer<NameProvider>(
              builder: (context, nameProvider, child) {
                if (nameProvider.isLoading) {
                  return const LoadingIndicator(
                    message: 'Loading Names...',
                    size: LoadingSize.medium,
                    withBackground: true,
                  );
                }

                if (nameProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: AppColors.getPrimaryColor(context),
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32.0),
                          child: Text(
                            'Failed to load names: ${nameProvider.error}',
                            style: const TextStyle(color: Colors.white),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: _initializeNames,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.getPrimaryColor(context),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                return TabBarView(
                  controller: _tabController,
                  children: [
                    NameListView(
                      names: nameProvider.boyNames,
                      searchQuery: _searchQuery,
                    ),
                    NameListView(
                      names: nameProvider.girlNames,
                      searchQuery: _searchQuery,
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
