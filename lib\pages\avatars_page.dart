import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/avatar_provider.dart';
import '../models/avatar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../screens/avatar_viewer_screen.dart';
import '../utils/font_loader.dart';
import '../utils/color_utils.dart';
import '../utils/theme_widgets.dart';
import '../providers/auth_provider.dart';
import '../services/auth_service.dart';

class AvatarsPage extends StatefulWidget {
  const AvatarsPage({super.key});

  @override
  State<AvatarsPage> createState() => _AvatarsPageState();
}

class _AvatarsPageState extends State<AvatarsPage> {
  late TabController _tabController;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();

    // Load favorite avatars when the page is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFavorites();
    });
  }

  // Load avatar favorites
  Future<void> _loadFavorites() async {
    try {
      final avatarProvider =
          Provider.of<AvatarProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Only load favorites if user is authenticated and not in guest mode
      if (authProvider.isSignedIn && !authProvider.isGuest) {
        debugPrint('Loading avatar favorites for authenticated user');
        await avatarProvider.loadFavorites();
      } else {
        debugPrint(
            'User not authenticated or in guest mode - clearing avatar favorites');
        avatarProvider.clearFavorites();
      }
    } catch (e) {
      debugPrint('Error loading avatar favorites: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        appBar: AppBar(
          title: Text(
            'Avatars',
            style: AppFontLoader.getPrakrtaStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
            ),
          ),
          backgroundColor: const Color(0xFF0D0D0D),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => Navigator.pop(context),
          ),
          bottom: TabBar(
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 14,
            ),
            indicator: ThemedTabIndicator(
              context: context,
              radius: 4,
              indicatorHeight: 3,
              insets: const EdgeInsets.symmetric(horizontal: 20),
            ),
            tabs: const [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Avatars'),
                    SizedBox(width: 8),
                    Icon(Icons.face, size: 20),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Favorites'),
                    SizedBox(width: 8),
                    Icon(Icons.favorite, size: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // Avatars tab content
            Consumer<AvatarProvider>(
              builder: (context, avatarProvider, child) {
                if (avatarProvider.isLoading) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: AppColors.getPrimaryColor(context),
                    ),
                  );
                }

                if (avatarProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          avatarProvider.error!,
                          style: const TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: avatarProvider.retry,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                return _buildAvatarGrid(context, avatarProvider.avatars);
              },
            ),
            // Favorites tab content
            Consumer<AvatarProvider>(
              builder: (context, avatarProvider, child) {
                final favoriteAvatars = avatarProvider.getFavoriteAvatars();
                final authProvider = Provider.of<AuthProvider>(context);

                // Show sign in message for guest users
                if (authProvider.isGuest || _authService.currentUser == null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.face,
                          size: 64,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Please Sign In to view your Avatars',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 16,
                            fontFamily: 'JosefinSans',
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.pushReplacementNamed(context, '/login');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.getPrimaryColor(context),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: const Text(
                            'Sign In',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                if (favoriteAvatars.isEmpty) {
                  return const Center(
                    child: Text(
                      'No favorite avatars yet',
                      style: TextStyle(color: Colors.white),
                    ),
                  );
                }

                return _buildAvatarGrid(context, favoriteAvatars);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarGrid(BuildContext context, List<Avatar> avatars) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: avatars.length,
      itemBuilder: (context, index) {
        final avatar = avatars[index];
        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AvatarViewerScreen(
                  avatars: avatars,
                  initialIndex: index,
                ),
              ),
            );
          },
          child: Hero(
            tag: 'avatar-${avatar.id}',
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF222222),
              ),
              clipBehavior: Clip.antiAlias,
              child: CachedNetworkImage(
                imageUrl: avatar.url,
                fit: BoxFit.cover,
                httpHeaders: const {
                  'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                  'User-Agent': 'HinduPath/1.0',
                },
                placeholder: (context, url) => Container(
                  color: const Color(0xFF333333),
                  child: Center(
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppColors.getPrimaryColor(context),
                      ),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) {
                  print('Avatar image error: $url');
                  print('Error details: $error');
                  return Container(
                    color: const Color(0xFF333333),
                    child: const Icon(Icons.error, color: Colors.white70),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
