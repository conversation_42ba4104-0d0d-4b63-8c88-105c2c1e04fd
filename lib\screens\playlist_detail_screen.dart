import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import '../models/playlist.dart';
import '../models/bhajan.dart';
import '../services/playlist_service.dart';
import '../providers/audio_provider.dart';
import '../services/local_storage_service.dart';
import '../utils/color_utils.dart';
import '../utils/font_loader.dart';
import '../utils/ui_helpers.dart';
import '../screens/player_screen.dart';
import '../widgets/mini_player.dart';
import 'dart:ui';

class PlaylistDetailScreen extends StatefulWidget {
  final Playlist playlist;

  const PlaylistDetailScreen({Key? key, required this.playlist})
      : super(key: key);

  @override
  State<PlaylistDetailScreen> createState() => _PlaylistDetailScreenState();
}

class _PlaylistDetailScreenState extends State<PlaylistDetailScreen> {
  final PlaylistService _playlistService = PlaylistService();
  final LocalStorageService _localStorageService = LocalStorageService();
  bool _isLoading = true;
  List<Bhajan> _playlistBhajans = [];
  String? _localImagePath;

  late Playlist _currentPlaylist;

  @override
  void initState() {
    super.initState();
    _currentPlaylist = widget.playlist;
    _loadPlaylistBhajans();
    _loadLocalCoverImage();
  }

  Future<void> _loadLocalCoverImage() async {
    final localPath =
        await _localStorageService.getPlaylistCoverPath(_currentPlaylist.id);
    if (localPath != null) {
      setState(() {
        _localImagePath = localPath;
        // Update the current playlist with local path
        _currentPlaylist = _currentPlaylist.updateLocalImage(localPath);
      });
    }
  }

  Future<void> _reloadPlaylist() async {
    final updatedPlaylist =
        await _playlistService.getPlaylist(_currentPlaylist.id);
    if (updatedPlaylist != null) {
      setState(() {
        _currentPlaylist = updatedPlaylist;
      });
      await _loadLocalCoverImage();
    }
  }

  Future<void> _loadPlaylistBhajans() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);

      // Filter the bhajans from the audio provider based on IDs in the playlist
      final allBhajans = audioProvider.bhajans;
      final playlistBhajans = allBhajans
          .where((bhajan) => _currentPlaylist.bhajanIds.contains(bhajan.id))
          .toList();
          
      // Sort bhajans alphabetically by title for consistency with other tabs
      playlistBhajans.sort((a, b) => 
        a.title.toLowerCase().compareTo(b.title.toLowerCase())
      );

      setState(() {
        _playlistBhajans = playlistBhajans;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading playlist bhajans: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showImagePickerDialog() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        // Save image locally
        final savedPath = await _localStorageService.savePlaylistCover(
          _currentPlaylist.id,
          File(image.path),
        );

        // Update playlist with new local image path
        _currentPlaylist = _currentPlaylist.updateLocalImage(savedPath);
        await _playlistService.updatePlaylist(_currentPlaylist);

        if (mounted) {
          setState(() {
            _localImagePath = savedPath;
            // Update current playlist reference to trigger UI refresh
            _currentPlaylist = _currentPlaylist;
          });

          // Force a reload to ensure we have the latest state
          await _reloadPlaylist();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Playlist cover updated',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: const Color(0xFF1E1E1E),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update cover: $e',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: const Color(0xFF1E1E1E),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  Widget _buildPlaylistHeader(BuildContext context, Size screenSize) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _currentPlaylist.name,
              style: AppFontLoader.getPrakrtaStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            if (_currentPlaylist.description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                _currentPlaylist.description,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              '${_playlistBhajans.length} ${_playlistBhajans.length == 1 ? 'bhajan' : 'bhajans'}',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _playPlaylist,
                icon: const Icon(Icons.play_arrow, color: Colors.white),
                label: const Text('Play'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.getPrimaryColor(context),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.1),
              radius: 24,
              child: IconButton(
                icon: const Icon(Icons.shuffle, color: Colors.white),
                onPressed: () => _playPlaylistShuffled(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _playPlaylist() {
    if (_playlistBhajans.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Playlist is empty'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    // Play the playlist but don't navigate to full player
    audioProvider.playMultipleBhajans(_playlistBhajans, 0);
    
    // Let the mini player appear instead of navigating to full player
  }

  void _playPlaylistShuffled() {
    if (_playlistBhajans.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Playlist is empty'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final shuffledBhajans = List<Bhajan>.from(_playlistBhajans)..shuffle();
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    // Play the shuffled playlist but don't navigate to full player
    audioProvider.playMultipleBhajans(shuffledBhajans, 0);
    
    // Let the mini player appear instead of navigating to full player
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        return Scaffold(
          backgroundColor: const Color(0xFF0D0D0D),
          body: Stack(
            children: [
              // Main content
              _isLoading
                ? const Center(child: CircularProgressIndicator())
                : CustomScrollView(
                    slivers: [
                      _buildAppBar(context, screenSize),
                      _buildPlaylistHeader(context, screenSize),
                      _buildControlButtons(context),
                      _buildBhajansList(context),
                    ],
                  ),
                  
              // Mini player - only show if bhajan is actually playing
              if (audioProvider.audioService.getCurrentBhajan() != null &&
                  !audioProvider.isMiniPlayerDismissed)
                Positioned(
                  left: 4,
                  right: 4,
                  bottom: 4,
                  child: Material(
                    color: Colors.transparent,
                    elevation: 0,
                    shadowColor: Colors.transparent,
                    child: GestureDetector(
                      onTap: () {
                        // Get the current bhajan before navigating
                        final Bhajan? currentBhajan =
                            audioProvider.audioService.getCurrentBhajan();
                        if (currentBhajan != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  PlayerScreen(bhajan: currentBhajan),
                            ),
                          );
                        }
                      },
                      child: const MiniPlayer(),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAppBar(BuildContext context, Size screenSize) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      expandedHeight: screenSize.height * 0.4,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Show local image if available, otherwise fallback to network image
            if (_currentPlaylist.localImagePath != null)
              Image.file(
                File(_currentPlaylist.localImagePath!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, _) {
                  print('Error loading local image: $error');
                  // Fall back to network image if local fails
                  if (_currentPlaylist.imageUrl.isNotEmpty) {
                    return CachedNetworkImage(
                      imageUrl: _currentPlaylist.imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => _buildLoadingWidget(),
                      errorWidget: (context, url, error) =>
                          _buildFallbackImage(),
                    );
                  }
                  return _buildFallbackImage();
                },
              )
            else if (_currentPlaylist.imageUrl.isNotEmpty)
              CachedNetworkImage(
                imageUrl: _currentPlaylist.imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildLoadingWidget(),
                errorWidget: (context, url, error) => _buildFallbackImage(),
              )
            else
              _buildFallbackImage(),
            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.3),
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
            // Blur overlay
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color: Colors.black.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          color: const Color(0xFF1E1E1E),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          onSelected: (value) async {
            if (value == 'change_cover') {
              await _showImagePickerDialog();
            } else if (value == 'delete') {
              // Delete functionality
              final confirm = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  backgroundColor: const Color(0xFF1E1E1E),
                  title: const Text('Delete Playlist',
                      style: TextStyle(color: Colors.white)),
                  content: const Text(
                    'Are you sure you want to delete this playlist? This action cannot be undone.',
                    style: TextStyle(color: Colors.white70),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: const Text('Cancel',
                          style: TextStyle(color: Colors.white70)),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context, true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Delete'),
                    ),
                  ],
                ),
              );

              if (confirm == true) {
                try {
                  await _playlistService.deletePlaylist(_currentPlaylist.id);
                  // Also delete the local cover image if it exists
                  await _localStorageService
                      .deletePlaylistCover(_currentPlaylist.id);
                  if (mounted) {
                    Navigator.pop(context);
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to delete playlist: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem<String>(
              value: 'change_cover',
              child: ListTile(
                leading: Icon(Icons.image, color: Colors.white),
                title:
                    Text('Change Cover', style: TextStyle(color: Colors.white)),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Delete Playlist',
                    style: TextStyle(color: Colors.red)),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBhajansList(BuildContext context) {
    if (_playlistBhajans.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.music_note,
                size: 64,
                color: Colors.white.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              const Text(
                'No Bhajans in this Playlist',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add bhajans from the bhajan player screen',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Get the audio provider to check mini player status
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    final hasVisibleMiniPlayer = audioProvider.audioService.getCurrentBhajan() != null && 
                                !audioProvider.isMiniPlayerDismissed;

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final bhajan = _playlistBhajans[index];
          return ListTile(
            contentPadding: index == _playlistBhajans.length - 1 && hasVisibleMiniPlayer
                ? const EdgeInsets.fromLTRB(16, 0, 16, 70) // Add padding to last item when mini player is visible
                : null,
            leading: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: CachedNetworkImage(
                imageUrl: bhajan.artworkUrl,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[800],
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white54,
                  ),
                ),
              ),
            ),
            title: Text(
              bhajan.title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              bhajan.artist,
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
            trailing: IconButton(
              icon: const Icon(Icons.remove_circle_outline,
                  color: Colors.white70),
              onPressed: () => _removeBhajanFromPlaylist(bhajan),
            ),
            onTap: () {
              final audioProvider =
                  Provider.of<AudioProvider>(context, listen: false);
              // Create a playlist starting from the tapped item
              final playlistStartingFrom = [
                ..._playlistBhajans.sublist(index),
                ..._playlistBhajans.sublist(0, index),
              ];
              // Play the bhajans but don't navigate to full player
              audioProvider.playMultipleBhajans(playlistStartingFrom, 0);
              
              // Don't navigate to full player screen immediately
              // Let the mini player appear instead
            },
          );
        },
        childCount: _playlistBhajans.length,
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.grey[900],
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildFallbackImage() {
    return Container(
      color: Colors.grey[900],
      child: const Icon(
        Icons.music_note,
        color: Colors.white54,
        size: 80,
      ),
    );
  }

  Future<void> _removeBhajanFromPlaylist(Bhajan bhajan) async {
    try {
      setState(() {
        _isLoading = true;
      });

      await _playlistService.removeBhajanFromPlaylist(
        widget.playlist.id,
        bhajan.id,
      );

      // Reload playlist bhajans
      await _loadPlaylistBhajans();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Removed "${bhajan.title}" from playlist'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('Error removing bhajan from playlist: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to remove bhajan from playlist'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
