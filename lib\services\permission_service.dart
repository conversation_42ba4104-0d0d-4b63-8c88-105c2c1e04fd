import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';

enum PermissionType { storage, notification, location, all }

class PermissionResult {
  final bool isGranted;
  final PermissionType type;
  final String? message;
  final bool shouldShowRationale;

  PermissionResult({
    required this.isGranted,
    required this.type,
    this.message,
    this.shouldShowRationale = false,
  });
}

class PermissionService {
  // Singleton pattern
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  int? _androidSdkVersion;

  // Permission status
  bool _storagePermissionGranted = false;
  bool _notificationPermissionGranted = false;
  bool _locationPermissionGranted = false;

  // User preference for notifications
  bool _userDeniedNotifications = false;
  static const String _userDeniedNotificationsKey = 'user_denied_notifications';

  // User preference for location
  bool _userDeniedLocation = false;
  static const String _userDeniedLocationKey = 'user_denied_location';

  // Default location (Nepal/India region) as fallback
  static const double _defaultLatitude = 27.7172; // Kathmandu, Nepal
  static const double _defaultLongitude = 85.3240;

  // Key for tracking onboarding completion
  static const String _onboardingCompletedKey = 'permission_onboarding_completed';
  
  // Track if onboarding has been completed
  bool _onboardingCompleted = false;

  // Getters for permission status
  bool get hasStoragePermission => _storagePermissionGranted;
  bool get hasNotificationPermission => _notificationPermissionGranted;
  bool get hasLocationPermission => _locationPermissionGranted;
  bool get hasAllPermissions =>
      _storagePermissionGranted &&
      _notificationPermissionGranted &&
      _locationPermissionGranted;
  bool get userHasDeniedNotifications => _userDeniedNotifications;
  bool get userHasDeniedLocation => _userDeniedLocation;
  bool get hasCompletedOnboarding => _onboardingCompleted;

  /// Initialize the permission service
  Future<void> init() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        _androidSdkVersion = androidInfo.version.sdkInt;
      }

      // Load user preferences
      await _loadUserNotificationPreference();
      await _loadUserLocationPreference();
      await _loadOnboardingStatus();

      // Check the current status of permissions
      await _updatePermissionStatus();
    } on PlatformException catch (e) {
      debugPrint('Error initializing PermissionService: $e');
    }
  }

  /// Load user notification preference from storage
  Future<void> _loadUserNotificationPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _userDeniedNotifications =
          prefs.getBool(_userDeniedNotificationsKey) ?? false;
      debugPrint(
          'User notification preference loaded: denied=$_userDeniedNotifications');
    } catch (e) {
      debugPrint('Error loading user notification preference: $e');
      _userDeniedNotifications = false;
    }
  }

  /// Load user location preference from storage
  Future<void> _loadUserLocationPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _userDeniedLocation = prefs.getBool(_userDeniedLocationKey) ?? false;
      debugPrint(
          'User location preference loaded: denied=$_userDeniedLocation');
    } catch (e) {
      debugPrint('Error loading user location preference: $e');
      _userDeniedLocation = false;
    }
  }

  /// Load onboarding completion status
  Future<void> _loadOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _onboardingCompleted = prefs.getBool(_onboardingCompletedKey) ?? false;
      debugPrint('Permission onboarding status loaded: completed=$_onboardingCompleted');
    } catch (e) {
      debugPrint('Error loading onboarding status: $e');
      _onboardingCompleted = false;
    }
  }

  /// Save user notification preference
  Future<void> setUserNotificationPreference(bool denied) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_userDeniedNotificationsKey, denied);
      _userDeniedNotifications = denied;
      debugPrint('User notification preference saved: denied=$denied');
    } catch (e) {
      debugPrint('Error saving user notification preference: $e');
    }
  }

  /// Save user location preference
  Future<void> setUserLocationPreference(bool denied) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_userDeniedLocationKey, denied);
      _userDeniedLocation = denied;
      debugPrint('User location preference saved: denied=$denied');
    } catch (e) {
      debugPrint('Error saving user location preference: $e');
    }
  }

  /// Reset user notification preference (for when users want to enable notifications again)
  Future<void> resetNotificationPreference() async {
    await setUserNotificationPreference(false);
  }

  /// Reset user location preference (for when users want to enable location again)
  Future<void> resetLocationPreference() async {
    await setUserLocationPreference(false);
  }

  /// Update the current status of all permissions
  Future<void> _updatePermissionStatus() async {
    _storagePermissionGranted = await _checkStoragePermission();
    _notificationPermissionGranted = await _checkNotificationPermission();
    _locationPermissionGranted = await _checkLocationPermission();
  }

  /// Check if storage permission is granted based on Android version
  Future<bool> _checkStoragePermission() async {
    if (_androidSdkVersion == null) {
      await init();
    }

    try {
      // Android 13+ (API 33+)
      if (_androidSdkVersion != null && _androidSdkVersion! >= 33) {
        debugPrint('Checking Android 13+ storage permissions');
        final photos = await Permission.photos.status;
        final videos = await Permission.videos.status;
        
        final allGranted = photos.isGranted && videos.isGranted;
        debugPrint('Android 13+ permissions - Photos: ${photos.isGranted}, Videos: ${videos.isGranted}');
        return allGranted;
      } 
      // Android 11-12 (API 30-32)
      else if (_androidSdkVersion != null && _androidSdkVersion! >= 30) {
        debugPrint('Checking Android 11-12 storage permissions');
        final storage = await Permission.storage.status;
        final manageExternal = await Permission.manageExternalStorage.status;
        
        debugPrint('Android 11-12 permissions - Storage: ${storage.isGranted}, Manage External: ${manageExternal.isGranted}');
        // For Android 11+, we'll consider either permission sufficient
        return storage.isGranted || manageExternal.isGranted;
      } 
      // Below Android 11
      else {
        debugPrint('Checking legacy storage permission');
        final storage = await Permission.storage.status;
        debugPrint('Legacy storage permission: ${storage.isGranted}');
        return storage.isGranted;
      }
    } catch (e) {
      debugPrint('Error checking storage permissions: $e');
      return false;
    }
  }

  /// Check if notification permission is granted
  Future<bool> _checkNotificationPermission() async {
    try {
      return await AwesomeNotifications().isNotificationAllowed();
    } catch (e) {
      debugPrint('Error checking notification permission: $e');
      // Fallback to permission handler
      return await Permission.notification.status.isGranted;
    }
  }

  /// Check if location permission is granted
  Future<bool> _checkLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
    } catch (e) {
      debugPrint('Error checking location permission: $e');
      return false;
    }
  }

  /// Request storage permission
  Future<PermissionResult> requestStoragePermission() async {
    if (_androidSdkVersion == null) {
      await init();
    }

    bool granted = false;
    String message = '';
    bool shouldShowRationale = false;

    try {
      debugPrint('Requesting storage permissions for Android SDK: $_androidSdkVersion');
      
      // Android 13+ (API 33+)
      if (_androidSdkVersion != null && _androidSdkVersion! >= 33) {
        debugPrint('Requesting Android 13+ specific permissions');
        
        // Request photos permission first
        final photos = await Permission.photos.request();
        debugPrint('Photos permission result: $photos');
        
        // Then request videos permission
        final videos = await Permission.videos.request();
        debugPrint('Videos permission result: $videos');
        
        granted = photos.isGranted && videos.isGranted;
        shouldShowRationale = photos.isPermanentlyDenied || videos.isPermanentlyDenied;
        
        if (granted) {
          message = 'Storage permissions granted';
        } else {
          message = 'For Android 13+, both Photos and Videos permissions are needed to download and save wallpapers';
        }
      }
      // Android 11-12 (API 30-32)
      else if (_androidSdkVersion != null && _androidSdkVersion! >= 30) {
        debugPrint('Requesting Android 11-12 specific permissions');
        
        // Request regular storage permission
        final storage = await Permission.storage.request();
        debugPrint('Storage permission result: $storage');
        
        if (!storage.isGranted) {
          // For Android 11+, we might need MANAGE_EXTERNAL_STORAGE
          final manageExternal = await Permission.manageExternalStorage.request();
          debugPrint('Manage external storage permission result: $manageExternal');
          
          granted = storage.isGranted || manageExternal.isGranted;
          shouldShowRationale = storage.isPermanentlyDenied && manageExternal.isPermanentlyDenied;
          
          if (granted) {
            message = 'Storage permissions granted';
          } else {
            message = 'For Android 11+, you might need to grant special permission in Settings';
          }
        } else {
          granted = true;
          message = 'Storage permission granted';
        }
      }
      // Below Android 11
      else {
        debugPrint('Requesting legacy storage permission');
        
        final storage = await Permission.storage.request();
        debugPrint('Legacy storage permission result: $storage');
        
        granted = storage.isGranted;
        shouldShowRationale = storage.isPermanentlyDenied;
        
        if (granted) {
          message = 'Storage permission granted';
        } else {
          message = 'Storage permission is required to download wallpapers';
        }
      }

      _storagePermissionGranted = granted;
      
      debugPrint('Storage permission request result: granted=$granted, message=$message');

      return PermissionResult(
        isGranted: granted,
        type: PermissionType.storage,
        message: message,
        shouldShowRationale: shouldShowRationale,
      );
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return PermissionResult(
        isGranted: false,
        type: PermissionType.storage,
        message: 'Error requesting permission: $e',
      );
    }
  }

  /// Request notification permission
  Future<PermissionResult> requestNotificationPermission() async {
    try {
      // If user has explicitly denied notifications before, respect that choice
      if (_userDeniedNotifications) {
        return PermissionResult(
          isGranted: false,
          type: PermissionType.notification,
          message: 'Notification permission denied by user preference',
        );
      }

      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (!isAllowed) {
        isAllowed =
            await AwesomeNotifications().requestPermissionToSendNotifications();

        // If user denied the permission, mark it in preferences
        if (!isAllowed) {
          await setUserNotificationPreference(true);
        }
      }

      _notificationPermissionGranted = isAllowed;

      return PermissionResult(
        isGranted: isAllowed,
        type: PermissionType.notification,
        message: isAllowed
            ? 'Notification permission granted'
            : 'Notification permission denied',
      );
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');

      // Fallback to permission handler
      try {
        final status = await Permission.notification.request();
        _notificationPermissionGranted = status.isGranted;

        // If user denied the permission, mark it in preferences
        if (!status.isGranted) {
          await setUserNotificationPreference(true);
        }

        return PermissionResult(
          isGranted: status.isGranted,
          type: PermissionType.notification,
          message: status.isGranted
              ? 'Notification permission granted'
              : 'Notification permission denied',
        );
      } catch (e) {
        return PermissionResult(
          isGranted: false,
          type: PermissionType.notification,
          message: 'Error requesting permission: $e',
        );
      }
    }
  }

  /// Request location permission
  Future<PermissionResult> requestLocationPermission() async {
    try {
      // If user has explicitly denied location before, respect that choice
      if (_userDeniedLocation) {
        return PermissionResult(
          isGranted: false,
          type: PermissionType.location,
          message: 'Location permission denied by user preference',
        );
      }

      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();

        // If user denied the permission, mark it in preferences
        if (permission == LocationPermission.denied ||
            permission == LocationPermission.deniedForever) {
          await setUserLocationPreference(true);
        }
      }

      bool isGranted = permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;

      _locationPermissionGranted = isGranted;

      return PermissionResult(
        isGranted: isGranted,
        type: PermissionType.location,
        message: isGranted
            ? 'Location permission granted'
            : 'Location permission denied',
      );
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      return PermissionResult(
        isGranted: false,
        type: PermissionType.location,
        message: 'Error requesting permission: $e',
      );
    }
  }

  /// Get current location if permission is granted
  /// Returns default location (Nepal/India region) if not granted
  Future<Position> getCurrentLocation() async {
    try {
      if (!_locationPermissionGranted && !_userDeniedLocation) {
        // Try to request permission if we don't have it yet and user hasn't denied it
        final result = await requestLocationPermission();
        if (!result.isGranted) {
          return Position(
            longitude: _defaultLongitude,
            latitude: _defaultLatitude,
            timestamp: DateTime.now(),
            accuracy: 0,
            altitude: 0,
            heading: 0,
            speed: 0,
            speedAccuracy: 0,
            altitudeAccuracy: 0,
            headingAccuracy: 0,
          );
        }
      } else if (_userDeniedLocation) {
        // Use default location if user denied permission
        return Position(
          longitude: _defaultLongitude,
          latitude: _defaultLatitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }

      // Get location if we have permission
      return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.low);
    } catch (e) {
      debugPrint('Error getting current location: $e');
      // Return default location if there's an error
      return Position(
        longitude: _defaultLongitude,
        latitude: _defaultLatitude,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        heading: 0,
        speed: 0,
        speedAccuracy: 0,
        altitudeAccuracy: 0,
        headingAccuracy: 0,
      );
    }
  }

  /// Check if all required permissions are granted for downloading and saving content
  Future<bool> hasRequiredPermissions() async {
    await _updatePermissionStatus();
    return _storagePermissionGranted;
  }

  /// Force update the current permissions status
  Future<void> updatePermissionsStatus() async {
    await _updatePermissionStatus();
    debugPrint("Permission status - Storage: $_storagePermissionGranted, Notification: $_notificationPermissionGranted, Location: $_locationPermissionGranted");
  }

  /// Request all permissions at once
  Future<List<PermissionResult>> requestAllPermissions() async {
    final List<PermissionResult> results = [];

    // Request storage permission
    final storageResult = await requestStoragePermission();
    results.add(storageResult);

    // Only request notification permission if user hasn't denied it before
    if (!_userDeniedNotifications) {
      final notificationResult = await requestNotificationPermission();
      results.add(notificationResult);
    }

    // Only request location permission if user hasn't denied it before
    if (!_userDeniedLocation) {
      final locationResult = await requestLocationPermission();
      results.add(locationResult);
    }

    return results;
  }

  /// Get detailed permission status
  Future<Map<PermissionType, bool>> getPermissionStatus() async {
    await _updatePermissionStatus();
    return {
      PermissionType.storage: _storagePermissionGranted,
      PermissionType.notification: _notificationPermissionGranted,
      PermissionType.location: _locationPermissionGranted,
    };
  }

  /// Mark onboarding as completed
  Future<void> setOnboardingCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, true);
      _onboardingCompleted = true;
      debugPrint('Permission onboarding marked as completed');
    } catch (e) {
      debugPrint('Error saving onboarding status: $e');
    }
  }
  
  /// Reset onboarding status (useful for testing)
  Future<void> resetOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, false);
      _onboardingCompleted = false;
      debugPrint('Permission onboarding status reset');
    } catch (e) {
      debugPrint('Error resetting onboarding status: $e');
    }
  }
  
  /// Reset all settings for a clean slate (useful for testing)
  Future<void> resetAllSettings() async {
    try {
      await resetOnboardingStatus();
      await setUserNotificationPreference(false);
      await setUserLocationPreference(false);
      _onboardingCompleted = false;
      _userDeniedNotifications = false;
      _userDeniedLocation = false;
      debugPrint('All permission settings reset');
    } catch (e) {
      debugPrint('Error resetting all settings: $e');
    }
  }
}
