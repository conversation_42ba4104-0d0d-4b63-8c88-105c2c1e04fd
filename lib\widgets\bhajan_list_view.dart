import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import 'bhajan_list_item.dart';
import '../models/bhajan.dart';
import '../screens/player_screen.dart';
import '../utils/color_utils.dart';
import 'loading_indicator.dart';
import '../utils/ui_helpers.dart';
import '../services/bhajan_player_service.dart';

class BhajanListView extends StatefulWidget {
  final String searchQuery;

  const BhajanListView({
    super.key,
    this.searchQuery = '',
  });

  @override
  State<BhajanListView> createState() => _BhajanListViewState();
}

class _BhajanListViewState extends State<BhajanListView>
    with AutomaticKeepAliveClientMixin {
  bool _hasError = false;
  bool _isPlayingAction = false; // Track if a play action is in progress
  bool _isInitializing = false;

  @override
  bool get wantKeepAlive => true; // Keep state when switching tabs

  @override
  void initState() {
    super.initState();
    debugPrint('BhajanListView: initState called');
    _initializeAudio();
  }

  Future<void> _initializeAudio() async {
    if (_isInitializing) return;

    setState(() {
      _isInitializing = true;
    });

    try {
      debugPrint('BhajanListView: Starting audio initialization');
      final audioProvider = context.read<AudioProvider>();

      if (!audioProvider.isInitialized) {
        debugPrint('BhajanListView: Loading bhajans...');
        try {
          await audioProvider.loadBhajans();
          debugPrint(
              'BhajanListView: Bhajans loaded. Count: ${audioProvider.bhajans.length}');
          if (mounted) {
            setState(() {
              _hasError = false;
              _isInitializing = false;
            });
          }
        } catch (e) {
          debugPrint('BhajanListView: Error during loadBhajans: $e');
          if (mounted) {
            setState(() {
              _hasError = true;
              _isInitializing = false;
            });
          }
        }
      } else {
        debugPrint('BhajanListView: AudioProvider already initialized');
        if (mounted) {
          setState(() {
            _hasError = false;
            _isInitializing = false;
          });
        }
      }
    } catch (e) {
      debugPrint('BhajanListView: Error in _initializeAudio: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _isInitializing = false;
        });
      }
    }
  }

  Future<void> _refreshBhajans() async {
    try {
      final audioProvider = context.read<AudioProvider>();
      final hasChanged = await audioProvider.refreshBhajans();

      if (hasChanged) {
        ScaffoldMessenger.of(context).showSnackBar(UIHelpers.getSuccessSnackBar(
            message: 'Bhajan list updated successfully!'));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
          UIHelpers.getErrorSnackBar(message: 'Failed to refresh bhajans: $e'));
    }
  }

  List<Bhajan> _filterBhajans(List<Bhajan> bhajans, String query) {
    // Create a sorted copy of the bhajans list by title
    List<Bhajan> sortedBhajans = List<Bhajan>.from(bhajans);
    sortedBhajans
        .sort((a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));

    if (query.isEmpty) {
      return sortedBhajans;
    }

    final lowercaseQuery = query.toLowerCase();
    final filtered = sortedBhajans.where((bhajan) {
      return bhajan.title.toLowerCase().contains(lowercaseQuery) ||
          bhajan.artist.toLowerCase().contains(lowercaseQuery);
    }).toList();

    return filtered;
  }

  void _playBhajan(AudioProvider audioProvider, Bhajan bhajan) {
    // Get filtered bhajans based on current search query to use as source list
    final filteredBhajans =
        _filterBhajans(audioProvider.bhajans, widget.searchQuery);

    BhajanPlayerService.playBhajan(
      audioProvider: audioProvider,
      bhajan: bhajan,
      // Pass filtered bhajans as source list to ensure playback respects current view
      sourceList: filteredBhajans,
      updateState: (isPlaying) {
        if (mounted) {
          setState(() {
            _isPlayingAction = isPlaying;
          });
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // For AutomaticKeepAliveClientMixin

    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        // Show error state if either local or provider error exists
        if (_hasError || audioProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Color(0xFF1b4332),
                  size: 48,
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    audioProvider.error ?? 'Failed to load bhajans',
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _hasError = false;
                    });
                    _initializeAudio();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.getPrimaryColor(context),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        // Show loading state during initial load
        if (_isInitializing || audioProvider.isLoading) {
          return const LoadingIndicator(
            message: 'Loading Bhajans...',
            size: LoadingSize.large,
            withBackground: true,
          );
        }

        final filteredBhajans =
            _filterBhajans(audioProvider.bhajans, widget.searchQuery);

        // Return an empty container if no bhajans are found after filtering
        if (filteredBhajans.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.music_note,
                  color: Colors.grey[700],
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  widget.searchQuery.isEmpty
                      ? 'No bhajans available'
                      : 'No bhajans matching "${widget.searchQuery}"',
                  style: const TextStyle(fontSize: 16, color: Colors.white),
                ),
                if (widget.searchQuery.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      // Clear search (would need to be implemented by parent)
                      Navigator.of(context).pop();
                    },
                    child: const Text('Clear Search'),
                  ),
              ],
            ),
          );
        }

        // Return a ListView with bhajans and pull-to-refresh
        return Column(
          children: [
            // Play All and Shuffle buttons row
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 4, 16, 8),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _playAllBhajans(audioProvider, filteredBhajans,
                            shuffle: false);
                      },
                      icon: const Icon(Icons.play_arrow, color: Colors.white),
                      label: const Text('Play All'),
                      style: AppColors.getPrimaryButtonStyle(
                        context,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _playAllBhajans(audioProvider, filteredBhajans,
                            shuffle: true);
                      },
                      icon: const Icon(Icons.shuffle, color: Colors.white),
                      label: const Text('Shuffle'),
                      style: AppColors.getPrimaryButtonStyle(
                        context,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Bhajan list with refresh indicator
            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshBhajans,
                color: AppColors.getPrimaryColor(context),
                child: Builder(
                  builder: (context) {
                    final isMiniPlayerVisible =
                        audioProvider.audioService.getCurrentBhajan() != null &&
                            !audioProvider.isMiniPlayerDismissed;
                    final adHeight = 70.0;
                    final miniPlayerHeight = 60.0;
                    final bottomPadding =
                        MediaQuery.of(context).padding.bottom +
                            adHeight +
                            (isMiniPlayerVisible ? miniPlayerHeight : 0);
                    return ListView.builder(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: EdgeInsets.only(
                        bottom: bottomPadding,
                      ),
                      itemCount: filteredBhajans.length,
                      itemBuilder: (context, index) {
                        // Build bhajan item
                        final bhajan = filteredBhajans[index];
                        return BhajanListItem(
                          bhajan: bhajan,
                          onTap: () => _playBhajan(audioProvider, bhajan),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Play all bhajans in the list, with option to shuffle
  Future<void> _playAllBhajans(
      AudioProvider audioProvider, List<Bhajan> bhajans,
      {bool shuffle = false}) async {
    await BhajanPlayerService.playMultipleBhajans(
      audioProvider: audioProvider,
      bhajans: bhajans,
      updateState: (isPlaying) {
        if (mounted) {
          setState(() {
            _isPlayingAction = isPlaying;
          });
        }
      },
      shuffle: shuffle,
    );
  }
}
